package com.example.corredores

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AppCompatActivity
import com.example.corredores.utils.PreferenceManager

/**
 * Actividad de splash que se muestra al iniciar la aplicación
 * Redirige según el estado del usuario (perfil seleccionado o no)
 */
class SplashActivity : AppCompatActivity() {

    companion object {
        private const val SPLASH_DELAY = 2000L // 2 segundos
    }

    private lateinit var preferenceManager: PreferenceManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_splash)

        preferenceManager = PreferenceManager(this)

        // Esperar 2 segundos y luego redirigir
        Handler(Looper.getMainLooper()).postDelayed({
            redirectToNextActivity()
        }, SPLASH_DELAY)
    }

    private fun redirectToNextActivity() {
        val intent = if (preferenceManager.hasSelectedProfile()) {
            // Si ya seleccionó perfil, ir según el tipo
            when (preferenceManager.getUserType()) {
                PreferenceManager.USER_TYPE_PARENT -> {
                    if (preferenceManager.isLoggedIn()) {
                        // Padre logueado -> MainActivity
                        Intent(this, MainActivity::class.java)
                    } else {
                        // Padre no logueado -> LoginActivity
                        Intent(this, LoginActivity::class.java)
                    }
                }
                PreferenceManager.USER_TYPE_CHILD -> {
                    // Hijo -> MainActivity (siempre)
                    Intent(this, MainActivity::class.java)
                }
                else -> {
                    // Tipo desconocido -> Selección de perfil
                    Intent(this, ProfileSelectionActivity::class.java)
                }
            }
        } else {
            // No ha seleccionado perfil -> ProfileSelectionActivity
            Intent(this, ProfileSelectionActivity::class.java)
        }

        startActivity(intent)
        finish()
    }
}
