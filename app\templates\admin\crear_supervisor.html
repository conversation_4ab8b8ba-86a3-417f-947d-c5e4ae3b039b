<!-- app/templates/admin/crear_supervisor.html -->

{% extends 'base.html' %}

{% block content %}
    <h2>Crear Nuevo Supervisor</h2>
    <form method="POST" action="{{ url_for('admin.crear_supervisor') }}">
        {{ form.hidden_tag() }}
        
        <div class="form-group">
            {{ form.username.label }}
            {{ form.username(class="form-control") }}
            {% for error in form.username.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </div>
        
        <div class="form-group">
            {{ form.email.label }}
            {{ form.email(class="form-control") }}
            {% for error in form.email.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </div>
        
        <div class="form-group">
            {{ form.first_name.label }}
            {{ form.first_name(class="form-control") }}
            {% for error in form.first_name.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </div>
        
        <div class="form-group">
            {{ form.last_name.label }}
            {{ form.last_name(class="form-control") }}
            {% for error in form.last_name.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </div>
        
        <div class="form-group">
            {{ form.phone.label }}
            {{ form.phone(class="form-control") }}
            {% for error in form.phone.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </div>
        
        <div class="form-group">
            {{ form.password.label }}
            {{ form.password(class="form-control") }}
            {% for error in form.password.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </div>
        
        <div class="form-group">
            {{ form.confirm_password.label }}
            {{ form.confirm_password(class="form-control") }}
            {% for error in form.confirm_password.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </div>
        
        <div class="form-group">
            {{ form.submit(class="btn btn-success") }}
        </div>
    </form>
{% endblock %}
