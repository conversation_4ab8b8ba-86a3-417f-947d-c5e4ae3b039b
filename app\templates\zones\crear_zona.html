<!-- app/templates/zones/crear_zona.html -->
{% extends 'base.html' %}

{% block content %}
  <h2>Crear Zona Segura</h2>
  <p>Haz clic en el mapa para definir el centro de la zona segura (se creará un círculo de 50 metros de radio).</p>

  {# Usar la URL del nuevo blueprint/ruta #}
  <form method="POST" action="{{ url_for('zones.crear_zona') }}">
    {{ form.hidden_tag() }}

    <div>
      {{ form.zone_name.label }}<br> {# Usar form.zone_name #}
      {{ form.zone_name(size=64) }}<br>
      {% for error in form.zone_name.errors %}
          <span style="color: red;">[{{ error }}]</span>
      {% endfor %}
    </div>
    <div>
      {{ form.schedule_start.label }}<br>
      {{ form.schedule_start() }}<br>
      {% for error in form.schedule_start.errors %}
          <span style="color: red;">[{{ error }}]</span>
      {% endfor %}
    </div>
    <div>
      {{ form.schedule_end.label }}<br>
      {{ form.schedule_end() }}<br>
      {% for error in form.schedule_end.errors %}
          <span style="color: red;">[{{ error }}]</span>
      {% endfor %}
    </div>

    <!-- Campos ocultos para Latitud y Longitud -->
    {{ form.latitude(id="latitude") }}
    {{ form.longitude(id="longitude") }}
    {# Mostrar errores si los validadores DataRequired fallan #}
    {% for error in form.latitude.errors %}
        <span style="color: red;">[{{ error }}]</span><br>
    {% endfor %}
     {% for error in form.longitude.errors %}
        <span style="color: red;">[{{ error }}]</span><br>
    {% endfor %}


    <!-- Campo para seleccionar el hijo -->
    <div>
      {{ form.child_id.label }}<br>
      {{ form.child_id() }}<br>
      {% for error in form.child_id.errors %}
          <span style="color: red;">[{{ error }}]</span>
      {% endfor %}
    </div>

    <!-- Contenedor del mapa -->
    <div id="map" style="height: 400px; margin-top: 15px; border: 1px solid #ccc;"></div>

    <div style="margin-top: 10px;">
      {{ form.submit() }} {# El texto del botón viene del form #}
    </div>
  </form>

{% endblock %}


{% block scripts %}
  {# Incluir scripts base si existen #}
  {# {{ super() }} #}

  <!-- Script para inicializar el mapa y capturar el punto central -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Inicializar el mapa centrado en Viedma
        var map = L.map('map').setView([-40.8136, -62.9936], 13); // Coordenadas de Viedma
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          maxZoom: 19,
          attribution: 'Map data © <a href="https://openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Referencias a los inputs ocultos de coordenadas
        const latitudeInput = document.getElementById('latitude');
        const longitudeInput = document.getElementById('longitude');

        // Variables para guardar el marcador y el círculo actuales
        let currentMarker = null;
        let currentCircle = null;
        const zoneRadius = 50; // Radio fijo en metros

        // Función que se ejecuta al hacer clic en el mapa
        map.on('click', function(e) {
          const clickedLat = e.latlng.lat;
          const clickedLng = e.latlng.lng;

          console.log(`Map clicked at: Lat ${clickedLat}, Lng ${clickedLng}`); // Log para depuración

          // 1. Actualizar los campos ocultos del formulario
          if (latitudeInput && longitudeInput) {
            latitudeInput.value = clickedLat;
            longitudeInput.value = clickedLng;
            console.log(`Hidden fields updated: Lat ${latitudeInput.value}, Lng ${longitudeInput.value}`);
          } else {
              console.error("Error: Latitude or Longitude hidden input not found!");
              alert("Error interno: No se pueden guardar las coordenadas. Contacte al administrador.");
              return; // Detener si no se encuentran los inputs
          }

          // 2. Limpiar marcador y círculo anteriores (si existen)
          if (currentMarker) {
            map.removeLayer(currentMarker);
            console.log("Previous marker removed.");
          }
          if (currentCircle) {
            map.removeLayer(currentCircle);
            console.log("Previous circle removed.");
          }

          // 3. Añadir nuevo marcador en el punto del clic
          currentMarker = L.marker([clickedLat, clickedLng]).addTo(map)
              .bindPopup(`Centro de la Zona<br>Lat: ${clickedLat.toFixed(6)}<br>Lon: ${clickedLng.toFixed(6)}`)
              .openPopup(); // Mostrar popup inmediatamente
          console.log("New marker added.");


          // 4. Añadir nuevo círculo alrededor del marcador
          currentCircle = L.circle([clickedLat, clickedLng], {
              radius: zoneRadius, // Usar el radio definido
              color: 'blue',      // Color del borde
              fillColor: '#30f',  // Color de relleno
              fillOpacity: 0.3    // Opacidad del relleno
          }).addTo(map);
           console.log(`New circle added with radius ${zoneRadius}m.`);

           // Opcional: Ajustar el zoom para ver bien el círculo
           map.fitBounds(currentCircle.getBounds());

        }); // Fin de map.on('click')

        // Mensaje inicial si no hay coordenadas (opcional)
         if (!latitudeInput.value || !longitudeInput.value) {
             console.log("No initial coordinates found. Waiting for map click.");
         }

    }); // Fin de DOMContentLoaded
  </script>
{% endblock %}