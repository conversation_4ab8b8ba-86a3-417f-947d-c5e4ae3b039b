package com.example.corredores.models

// Modelo para el usuario (respuesta del servidor)
data class User(
    val id: Int,
    val folio: String,
    val name: String?,
    val role: String,
    val is_active: Boolean
)

// Request para login de padre
data class LoginRequest(
    val folio: String,
    val password: String
)

// Response del login
data class LoginResponse(
    val access_token: String,
    val user: User
)

// Request para token de hijo
data class TokenRequest(
    val token: String
)

// Response del token
data class TokenResponse(
    val status: String,
    val message: String,
    val child_id: Int? = null
)

// Modelos para ubicación
data class LocationUpdateRequest(
    val latitude: Double,
    val longitude: Double,
    val accuracy: Float? = null,
    val timestamp: String // ISO 8601 format como espera el servidor
)

data class LocationUpdateResponse(
    val success: Boolean,
    val message: String
)

// Modelos para geofencing
data class GeofenceStatusResponse(
    val success: Boolean,
    val zones: List<GeofenceZone>
)

data class GeofenceZone(
    val id: Int,
    val name: String,
    val latitude: Double,
    val longitude: Double,
    val radius: Float,
    val start_time: String,
    val end_time: String,
    val is_active: Boolean
)

// Modelos para sesión web
data class WebSessionRequest(
    val child_id: Int
)

data class WebSessionResponse(
    val status: String,
    val dashboard_url: String? = null,
    val expires_in: Int? = null,
    val message: String? = null
)

// Modelos para notificaciones
data class NotificationRequest(
    val notification_type: String,
    val message_text: String? = null
)

data class NotificationResponse(
    val status: String,
    val message: String
)

// Modelos para solicitud de ubicación inmediata
data class LocationRequestResponse(
    val status: String,
    val message: String
)
