<!-- app/templates/routes/crear_ruta.html -->
{% extends 'base.html' %}

{% block content %}
  <h2>Crear Ruta de Corredor Seguro</h2>
  
  <form method="POST" action="{{ url_for('routes.crear_ruta') }}">
    {{ form.hidden_tag() }}
    
    <div>
      {{ form.route_name.label }}<br>
      {{ form.route_name(size=64) }}<br>
      {% for error in form.route_name.errors %}
          <span style="color: red;">[{{ error }}]</span>
      {% endfor %}
    </div>
    <div>
      {{ form.schedule_start.label }}<br>
      {{ form.schedule_start() }}<br>
      {% for error in form.schedule_start.errors %}
          <span style="color: red;">[{{ error }}]</span>
      {% endfor %}
    </div>
    <div>
      {{ form.schedule_end.label }}<br>
      {{ form.schedule_end() }}<br>
      {% for error in form.schedule_end.errors %}
          <span style="color: red;">[{{ error }}]</span>
      {% endfor %}
    </div>
    
    <!-- Campo oculto para almacenar el GeoJSON generado a partir del mapa -->
    <input type="hidden" id="route_data" name="route_data" value="">
    
    <!-- Campo para seleccionar el hijo -->
    <div>
      {{ form.child_id.label }}<br>
      {{ form.child_id() }}<br>
      {% for error in form.child_id.errors %}
          <span style="color: red;">[{{ error }}]</span>
      {% endfor %}
    </div>
    
    <!-- Contenedor del mapa -->
    <div id="map" style="height: 400px; margin-top: 15px;"></div>
    
    <div style="margin-top: 10px;">
      {{ form.submit() }}
    </div>
  </form>

  <!-- Script para inicializar el mapa y capturar los puntos -->
  <script>
    // Inicializar el mapa centrado en una ubicación predeterminada (ejemplo: Buenos Aires)
    var map = L.map('map').setView([-40.8136, -62.9936], 13);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: 'Map data © <a href="https://openstreetmap.org">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Array para almacenar los puntos
    var puntos = [];
    // Capa para la polilínea que une los puntos
    var polyline = L.polyline([], {color: 'red'}).addTo(map);

    // Función para actualizar el campo oculto con los datos en formato GeoJSON
    function actualizarGeoJSON() {
      var geojsonFeature = {
        "type": "FeatureCollection",
        "features": [
          {
            "type": "Feature",
            "geometry": {
              "type": "LineString",
              "coordinates": puntos.map(function(pt) {
                return [pt.lng, pt.lat];
              })
            },
            "properties": {
              "name": document.getElementById("route_name") ? document.getElementById("route_name").value : ""
            }
          }
        ]
      };
      document.getElementById("route_data").value = JSON.stringify(geojsonFeature);
    }

    // Cada vez que se haga clic en el mapa, agregar un punto
    map.on('click', function(e) {
      puntos.push(e.latlng);
      // Colocar un marcador en el punto clickeado
      L.marker(e.latlng).addTo(map);
      // Actualizar la polilínea
      polyline.setLatLngs(puntos);
      // Actualizar el campo oculto con el GeoJSON generado
      actualizarGeoJSON();
    });
  </script>
{% endblock %}
