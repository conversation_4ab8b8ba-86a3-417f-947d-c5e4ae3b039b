# app/routes/websocket_events.py
from flask import current_app
from flask_socketio import emit, join_room, leave_room, disconnect
from app import socketio
import time

# Diccionario para almacenar conexiones activas de hijos
# Formato: {child_id: {'sid': session_id, 'timestamp': time}}
active_websocket_connections = {}

@socketio.on('connect')
def handle_connect():
    """Maneja nuevas conexiones WebSocket"""
    current_app.logger.info(f"Nueva conexión WebSocket: {request.sid}")

@socketio.on('disconnect')
def handle_disconnect():
    """Maneja desconexiones WebSocket"""
    # Buscar y remover la conexión del hijo
    for child_id, connection_data in list(active_websocket_connections.items()):
        if connection_data['sid'] == request.sid:
            del active_websocket_connections[child_id]
            current_app.logger.info(f"Hijo {child_id} desconectado del WebSocket")
            break

    current_app.logger.info(f"Conexión WebSocket desconectada: {request.sid}")

@socketio.on('child_register')
def handle_child_register(data):
    """
    Evento para que el hijo se registre en WebSocket
    data = {'child_id': 123}
    """
    try:
        child_id = data.get('child_id')
        if not child_id:
            emit('error', {'message': 'child_id requerido'})
            return

        child_id = int(child_id)

        # Registrar conexión del hijo
        active_websocket_connections[child_id] = {
            'sid': request.sid,
            'timestamp': time.time()
        }

        # Unir al hijo a su sala personal
        join_room(f"child_{child_id}")

        current_app.logger.info(f"✅ Hijo {child_id} registrado en WebSocket (SID: {request.sid})")

        # Confirmar registro
        emit('registration_confirmed', {
            'status': 'success',
            'message': 'Registrado correctamente en WebSocket',
            'child_id': child_id
        })

    except Exception as e:
        current_app.logger.error(f"Error en child_register: {str(e)}")
        emit('error', {'message': 'Error en registro'})

@socketio.on('parent_register')
def handle_parent_register(data):
    """
    Evento para que el padre se registre en WebSocket para recibir actualizaciones
    data = {'child_id': 123}
    """
    try:
        child_id = data.get('child_id')
        if not child_id:
            emit('error', {'message': 'child_id requerido'})
            return

        child_id = int(child_id)

        # Unir al padre a la sala para recibir actualizaciones de este hijo
        join_room(f"parent_of_child_{child_id}")

        current_app.logger.info(f"✅ Padre registrado para recibir actualizaciones de hijo {child_id}")

        # Confirmar registro
        emit('parent_registration_confirmed', {
            'status': 'success',
            'message': 'Registrado para recibir actualizaciones',
            'child_id': child_id
        })

    except Exception as e:
        current_app.logger.error(f"Error en parent_register: {str(e)}")
        emit('error', {'message': 'Error en registro de padre'})

@socketio.on('location_response')
def handle_location_response(data):
    """
    Evento para recibir la ubicación del hijo
    data = {'child_id': 123, 'latitude': -40.123, 'longitude': -62.456, 'accuracy': 10}
    """
    try:
        child_id = data.get('child_id')
        latitude = data.get('latitude')
        longitude = data.get('longitude')
        accuracy = data.get('accuracy', 0)

        current_app.logger.info(f"📍 Ubicación inmediata recibida de hijo {child_id}: {latitude}, {longitude}")

        # Enviar ubicación inmediata a todos los padres conectados de este hijo
        from datetime import datetime
        current_timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Emitir a la sala del padre para actualización inmediata
        socketio.emit('immediate_location_update', {
            'child_id': child_id,
            'latitude': latitude,
            'longitude': longitude,
            'accuracy': accuracy,
            'timestamp': current_timestamp,
            'type': 'immediate'
        }, room=f"parent_of_child_{child_id}")

        # Confirmar al hijo que se recibió
        emit('location_received', {
            'status': 'success',
            'message': 'Ubicación recibida correctamente'
        })

    except Exception as e:
        current_app.logger.error(f"Error en location_response: {str(e)}")
        emit('error', {'message': 'Error procesando ubicación'})

def request_immediate_location_websocket(child_id):
    """
    Función para solicitar ubicación inmediata via WebSocket
    Esta función se llama desde el endpoint REST cuando el padre presiona el botón
    """
    try:
        if child_id in active_websocket_connections:
            connection_data = active_websocket_connections[child_id]
            current_time = time.time()

            # Verificar que la conexión sea reciente (menos de 5 minutos)
            if current_time - connection_data['timestamp'] < 300:
                # Enviar solicitud inmediata al hijo
                socketio.emit('request_immediate_location', {
                    'urgent': True,
                    'timestamp': current_time,
                    'message': 'Padre solicita ubicación inmediata'
                }, room=f"child_{child_id}")

                current_app.logger.info(f"🚨 Solicitud WebSocket enviada a hijo {child_id}")
                return True
            else:
                # Conexión expirada
                del active_websocket_connections[child_id]
                current_app.logger.warning(f"Conexión WebSocket expirada para hijo {child_id}")
                return False
        else:
            current_app.logger.warning(f"Hijo {child_id} no conectado via WebSocket")
            return False

    except Exception as e:
        current_app.logger.error(f"Error enviando solicitud WebSocket: {str(e)}")
        return False

def is_child_connected_websocket(child_id):
    """
    Función para verificar si un hijo está conectado via WebSocket
    """
    if child_id in active_websocket_connections:
        connection_data = active_websocket_connections[child_id]
        current_time = time.time()

        # Verificar que la conexión sea reciente (menos de 5 minutos)
        if current_time - connection_data['timestamp'] < 300:
            return True
        else:
            # Conexión expirada
            del active_websocket_connections[child_id]
            return False

    return False

# Importar request para usar en los eventos
from flask import request
