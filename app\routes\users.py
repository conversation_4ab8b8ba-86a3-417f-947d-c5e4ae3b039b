# app/routes/users.py
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app, g, make_response
from flask_login import login_required, current_user
from app import db, csrf  # <-- Import csrf object from app/__init__.py
from app.models import User, UserLocationLog, Role, Child, Token
from app.forms.user_forms import AddChildForm, EditUserForm, LinkForm
import json
from app.utils.encryption import Encryption
import os, string, secrets
from datetime import datetime, timedelta
from functools import wraps  # Para crear decoradores personalizados

users_bp = Blueprint('users', __name__, template_folder='templates/users')

# Custom decorator for child authentication
def child_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        print("Entrando en el decorador child_required")  # DEBUG
        child_id = request.cookies.get('child_id')
        print(f"Valor de la cookie 'child_id': {child_id}")  # DEBUG
        if not child_id:
            flash('Debes vincular tu dispositivo primero.', 'warning')
            return redirect(url_for('users.vincular_dispositivo'))
        g.child = Child.query.get(child_id)
        print(f"g.child: {g.child}")  # DEBUG
        if not g.child:
            flash('Dispositivo no vinculado o hijo no encontrado.', 'danger')
            return redirect(url_for('users.vincular_dispositivo'))
        return f(*args, **kwargs)
    return decorated_function

# Custom decorator for parent authentication
def parent_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Debes iniciar sesión primero.', 'warning')
            return redirect(url_for('auth.login'))

        if not current_user.has_role('PADRE'):
            flash('Acceso denegado. Solo los padres pueden acceder a esta función.', 'danger')
            return redirect(url_for('users.dashboard'))

        g.parent = current_user
        return f(*args, **kwargs)
    return decorated_function

@users_bp.route('/dashboard')
@login_required
def dashboard():
    print("=" * 60)
    print("🚀 DASHBOARD ACCESS ATTEMPT")
    print("=" * 60)

    try:
        # Log información del usuario actual
        print(f"👤 Usuario: {current_user.username if current_user.is_authenticated else 'No autenticado'}")
        print(f"📧 Email: {current_user.email if current_user.is_authenticated else 'N/A'}")
        print(f"🆔 User ID: {current_user.user_id if current_user.is_authenticated else 'N/A'}")
        print(f"✅ Autenticado: {current_user.is_authenticated}")

        # Log roles del usuario
        try:
            roles = [role.role_name for role in current_user.roles]
            print(f"🎭 Roles: {roles}")
        except Exception as e:
            print(f"❌ Error obteniendo roles: {e}")
            roles = []

        # Procesar según el rol
        if 'PADRE' in roles:
            print("👨‍👧‍👦 Procesando dashboard para PADRE")
            try:
                print("🔍 Obteniendo lista de hijos...")
                children = current_user.children.all()  # Convertir query a lista
                print(f"👶 Hijos encontrados: {len(children) if children else 0}")

                # Log detalles de cada hijo
                for i, child in enumerate(children):
                    print(f"   👶 Hijo {i+1}: {child.first_name} {child.last_name} (ID: {child.child_id})")

            except Exception as e:
                print(f"❌ Error obteniendo hijos: {e}")
                print(f"❌ Tipo de error: {type(e).__name__}")
                import traceback
                print(f"❌ Traceback: {traceback.format_exc()}")
                children = []

            print("📄 Renderizando template para PADRE...")
            return render_template('users/dashboard.html', children=children)

        elif 'SUPERVISOR' in roles:
            print("👮‍♂️ Procesando dashboard para SUPERVISOR")
            return render_template('users/dashboard.html', supervisor=True)

        elif 'ADMIN' in roles:
            print("👑 Procesando dashboard para ADMIN")
            return render_template('users/dashboard.html', admin=True)

        elif 'HIJO' in roles:
            print("👶 Procesando dashboard para HIJO")
            return render_template('users/dashboard.html')

        else:
            print("❓ Usuario sin rol específico")
            return render_template('users/dashboard.html')

    except Exception as e:
        print("=" * 60)
        print("💥 ERROR CRÍTICO EN DASHBOARD")
        print("=" * 60)
        print(f"❌ Error: {e}")
        print(f"❌ Tipo: {type(e).__name__}")
        import traceback
        print(f"❌ Traceback completo:")
        print(traceback.format_exc())
        print("=" * 60)

        current_app.logger.error(f"Error crítico en dashboard: {e}")
        current_app.logger.error(f"Traceback: {traceback.format_exc()}")

        # Intentar renderizar template básico
        try:
            return render_template('users/dashboard.html')
        except Exception as template_error:
            print(f"💥 Error también en template: {template_error}")
            return f"Error interno del servidor: {e}", 500

# Route to add child
@users_bp.route('/agregar_hijo', methods=['GET', 'POST'])
@login_required
def agregar_hijo():
    form = AddChildForm()
    if form.validate_on_submit():
        # Crear nuevo hijo
        new_child = Child(
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            parent_id=current_user.user_id  # Asignar el padre actual
        )
        db.session.add(new_child)
        db.session.commit() # Commit to get the child_id

        # Generar el token de vinculación
        token = generate_token(new_child.child_id)

        #ADD - add telefono hijo.
        current_user.telefono_hijo = form.telefono_hijo.data
        db.session.commit()

        return render_template('users/agregar_hijo.html', form=form, token=token)

    return render_template('users/agregar_hijo.html', form=form)


@users_bp.route('/gestionar_hijos')
@login_required
def gestionar_hijos():
    children = current_user.children
    return render_template('users/gestionar_hijos.html', children=children)

@users_bp.route('/crear_supervisor', methods=['GET', 'POST'])
@login_required
def crear_supervisor():
    if not current_user.has_role('ADMIN'):
        flash('Acceso no autorizado.', 'danger')
        return redirect(url_for('users.dashboard'))

    from app.forms.user_forms import CreateSupervisorForm
    form = CreateSupervisorForm()

    if form.validate_on_submit():
        username = form.username.data
        email = form.email.data
        password = form.password.data
        first_name = form.first_name.data
        last_name = form.last_name.data

        if User.query.filter((User.username == username) | (User.email == email)).first():
            flash('El usuario o correo ya existe.', 'danger')
            return redirect(url_for('users.crear_supervisor'))

        new_supervisor = User(
            username=username,
            email=email,
            first_name=first_name,
            last_name=last_name
        )
        new_supervisor.set_password(password)

        supervisor_role = Role.query.filter_by(role_name='SUPERVISOR').first()
        if not supervisor_role:
            flash('El rol SUPERVISOR no está definido en la base de datos.', 'danger')
            return redirect(url_for('users.crear_supervisor'))

        new_supervisor.roles.append(supervisor_role)
        db.session.add(new_supervisor)
        db.session.commit()

        flash('Supervisor creado exitosamente.', 'success')
        return redirect(url_for('users.dashboard'))

    return render_template('users/crear_supervisor.html', form=form)

@users_bp.route('/crear_operador', methods=['GET', 'POST'])
@login_required
def crear_operador():
    if not (current_user.has_role('ADMIN') or current_user.has_role('SUPERVISOR')):
        flash('Acceso no autorizado.', 'danger')
        return redirect(url_for('users.dashboard'))

    from app.forms.user_forms import CreateOperatorForm
    form = CreateOperatorForm()

    if form.validate_on_submit():
        if User.query.filter((User.email == form.email.data) | (User.username == form.username.data)).first():
            flash('El correo o nombre de usuario ya existe.', 'danger')
            return redirect(url_for('users.crear_operador'))

        new_operator = User(
            username=form.username.data,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            email=form.email.data,
            hierarchy=form.hierarchy.data,
            file_number=form.file_number.data
        )
        new_operator.set_password(form.password.data)

        operator_role = Role.query.filter_by(role_name='OPERADOR').first()
        if not operator_role:
            flash('El rol OPERADOR no está definido en la base de datos.', 'danger')
            return redirect(url_for('users.crear_operador'))

        new_operator.roles.append(operator_role)
        db.session.add(new_operator)
        db.session.commit()

        flash('Usuario Operador creado exitosamente.', 'success')
        return redirect(url_for('users.dashboard'))

    return render_template('users/crear_operador.html', form=form)

@users_bp.route('/crear_padre', methods=['GET', 'POST'])
@login_required
def crear_padre():
    print("---- /crear_padre INICIO ----")
    print(f"Current user: {current_user}")
    print(f"Current user is authenticated: {current_user.is_authenticated}")
    if current_user.is_authenticated:
        print(f"Current user roles: {[role.role_name for role in current_user.roles]}")
    else:
        print("Current user has NO roles.")

    if not current_user.is_authenticated or not current_user.has_role('SUPERVISOR'):
        flash('Acceso no autorizado.', 'danger')
        print("Acceso no autorizado.")
        return redirect(url_for('users.dashboard'))

    print("Acceso autorizado (es supervisor).")
    from app.forms.user_forms import CreateParentForm
    form = CreateParentForm()

    if form.validate_on_submit():
        print("Formulario válido.")
        username = form.username.data
        email = form.email.data
        password = form.password.data
        first_name = form.first_name.data
        last_name = form.last_name.data
        phone = form.phone.data

        if User.query.filter((User.username == username) | (User.email == email)).first():
            flash('El usuario o correo ya existe.', 'danger')
            return redirect(url_for('users.crear_padre'))

        new_parent = User(
            username=username,
            email=email,
            first_name=first_name,
            last_name=last_name,
            phone=phone
        )
        new_parent.set_password(password)

        parent_role = Role.query.filter_by(role_name='PADRE').first()
        print(f"Rol PADRE encontrado: {parent_role}")

        if not parent_role:
            flash('El rol PADRE no está definido en la base de datos.', 'danger')
            return redirect(url_for('users.crear_padre'))

        new_parent.roles.append(parent_role)
        db.session.add(new_parent)
        try:
            db.session.commit()
            flash('Usuario Padre creado exitosamente.', 'success')
            return redirect(url_for('users.dashboard'))
        except Exception as e:
            db.session.rollback()
            flash(f'Error al crear el usuario Padre: {e}', 'danger')
            print(f"ERROR al crear padre: {e}")
            return redirect(url_for('users.crear_padre'))
    else:
        print("Formulario NO válido:")
        for field, errors in form.errors.items():
            for error in errors:
                print(f"  - {field}: {error}")


    print("---- /crear_padre FIN ----")
    return render_template('users/crear_padre.html', form=form)

@users_bp.route('/editar_usuario/<int:user_id>', methods=['GET', 'POST'])
@login_required
def editar_usuario(user_id):
    user = User.query.get_or_404(user_id)

    if current_user.has_role('ADMIN'):
        if any(role.role_name == 'HIJO' for role in user.roles):
            flash('No puedes editar a un usuario hijo.', 'danger')
            return redirect(url_for('users.listar_usuarios'))
    elif current_user.has_role('SUPERVISOR'):
        if any(role.role_name == 'HIJO' for role in user.roles):
            flash('No puedes editar a un usuario hijo.', 'danger')
            return redirect(url_for('users.listar_usuarios'))
        if any(role.role_name == 'ADMIN' for role in user.roles):
            flash('No puedes editar a un usuario administrador.', 'danger')
            return redirect(url_for('users.listar_usuarios'))
    elif current_user.has_role('PADRE'):
        if not any(child.child_id == user.user_id for child in current_user.children):
            flash('Solo puedes editar a tus hijos.', 'danger')
            return redirect(url_for('users.dashboard'))
    else:
        flash('No tienes permisos para editar este usuario.', 'danger')
        return redirect(url_for('users.dashboard'))

    from app.forms.user_forms import EditUserForm
    form = EditUserForm(obj=user)

    if form.validate_on_submit():
        user.username = form.username.data
        user.first_name = form.first_name.data
        user.last_name = form.last_name.data
        user.email = form.email.data
        if form.password.data:
            user.set_password(form.password.data)
        db.session.commit()
        flash('Usuario actualizado exitosamente.', 'success')
        return redirect(url_for('users.listar_usuarios'))

    return render_template('users/editar_usuario.html', form=form, user=user)

@users_bp.route('/listar_usuarios')
@login_required
def listar_usuarios():
    if current_user.has_role('ADMIN'):
        users = User.query.filter(~User.roles.any(Role.role_name == 'HIJO')).all()
    elif current_user.has_role('SUPERVISOR'):
        users = User.query.filter(~User.roles.any(Role.role_name.in_(['HIJO', 'ADMIN']))).all()
    else:
        flash('No tienes permiso para acceder a esta lista de usuarios.', 'danger')
        return redirect(url_for('users.dashboard'))

    return render_template('users/listar_usuarios.html', users=users)

@users_bp.route('/eliminar_usuario/<int:user_id>', methods=['POST'])
@login_required
def eliminar_usuario(user_id):
    user = User.query.get_or_404(user_id)

    if user.user_id == current_user.user_id:
        flash('No puedes eliminar tu propio usuario.', 'danger')
        return redirect(url_for('users.dashboard'))

    if current_user.has_role('ADMIN') or current_user.has_role('SUPERVISOR'):
        if current_user.has_role('SUPERVISOR') and any(role.role_name == 'ADMIN' for role in user.roles):
            flash('No puedes eliminar a un usuario administrador.', 'danger')
            return redirect(url_for('users.listar_usuarios'))

        try:
            db.session.delete(user)
            db.session.commit()
            flash('Usuario eliminado exitosamente.', 'success')
        except Exception as e:
            db.session.rollback()
            flash(f'Error al eliminar usuario: {e}', 'danger')
            current_app.logger.error(f"Error al eliminar usuario {user_id}: {e}", exc_info=True)
        return redirect(url_for('users.listar_usuarios'))

    elif current_user.has_role('PADRE'):
        child = Child.query.filter_by(child_id=user.user_id, parent_id=current_user.user_id).first()
        if not child:
            flash('Solo puedes eliminar a tus hijos.', 'danger')
            return redirect(url_for('users.gestionar_hijos'))

        try:
            db.session.delete(child)
            db.session.commit()
            flash('Hijo eliminado exitosamente.', 'success')
        except Exception as e:
            db.session.rollback()
            flash(f'Error al eliminar hijo: {e}', 'danger')
            current_app.logger.error(f"Error al eliminar hijo {user_id}: {e}", exc_info=True)
        return redirect(url_for('users.gestionar_hijos'))

    else:
        flash('No tienes permisos para eliminar usuarios.', 'danger')
        return redirect(url_for('users.dashboard'))


@users_bp.route('/ver_ubicacion_hijo/<int:child_id>')
@login_required
def ver_ubicacion_hijo(child_id):
    if 'PADRE' not in [role.role_name for role in current_user.roles]:
        flash('Acceso denegado. Solo los padres pueden acceder a esta página.', 'danger')
        return redirect(url_for('users.dashboard'))

    child = Child.query.filter_by(child_id=child_id, parent_id=current_user.user_id).first()
    if not child:
        flash('Este hijo no pertenece a tu cuenta.', 'danger')
        return redirect(url_for('users.dashboard'))

    log = UserLocationLog.query.filter_by(child_id=child_id).order_by(UserLocationLog.recorded_at.desc()).first()
    decrypted_location_data = None

    if log:
        fernet_key = os.getenv('FERNET_KEY')
        if not fernet_key:
            flash('Clave de encriptación no configurada.', 'danger')
            return redirect(url_for('users.dashboard'))
        encryption = Encryption(fernet_key)
        try:
            decrypted_location_data = json.loads(encryption.decrypt(log.encrypted_location_data))
        except Exception as e:
            flash(f'Error al desencriptar la ubicación: {e}', 'danger')
            decrypted_location_data = None


    return render_template('users/ver_ubicacion_hijo.html',
                           child=child,
                           location_data=decrypted_location_data,
                           log = log)

@users_bp.route('/log_location', methods=['POST'])
@child_required
def log_location():
    data = request.get_json()
    if not data:
        return jsonify({'message': 'No JSON data received'}), 400

    child_id = g.child.child_id
    latitude = data.get('latitude')
    longitude = data.get('longitude')

    fernet_key = os.getenv('FERNET_KEY')
    if not fernet_key:
        return jsonify({'message': 'Fernet key not configured server-side'}), 500
    encryption = Encryption(fernet_key)

    try:
        log = UserLocationLog(
            child_id=child_id,
            encrypted_location_data=encryption.encrypt(json.dumps({
                'lat': latitude,
                'lng': longitude,
            })),
            recorded_at=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
        db.session.add(log)
        db.session.commit()
        return jsonify({'message': 'Location logged successfully'}), 200
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error logging location for child {child_id}: {e}", exc_info=True)
        return jsonify({'message': 'Error logging location'}), 500

@users_bp.route('/vincular_dispositivo', methods=['GET', 'POST'])
def vincular_dispositivo():
    form = LinkForm()
    if form.validate_on_submit():
        token_str = form.token.data.strip()  # ADDED .strip()
        print(f"Token ingresado (con strip): {token_str}")  # DEBUG
        token = Token.query.filter_by(token=token_str).first()
        print(f"Token encontrado en la BD: {token}")  # DEBUG

        if not token:
            flash('Token inválido.', 'danger')
            return render_template('users/vincular_dispositivo.html', form=form)
        if token.used:
            flash('Este token ya ha sido utilizado.', 'danger')
            return render_template('users/vincular_dispositivo.html', form=form)
        if token.expiration < datetime.now():
            flash('El token ha expirado.', 'danger')
            return render_template('users/vincular_dispositivo.html', form=form)

        token.used = True
        db.session.commit()

        response = make_response(redirect(url_for('users.dashboard_hijo')))
        response.set_cookie(
            'child_id',
            str(token.child_id),
            httponly=True,
            secure=True,
            max_age=60*60*24*30,
            domain=None,  # Use None for the current domain, or specify it.
            path='/'      # Set the path to the root
        )
        print(f"Cookie 'child_id' establecida con valor: {token.child_id}, dominio: {response.headers.get('Set-Cookie')}")  # DEBUG
        return response

    return render_template('users/vincular_dispositivo.html', form=form)


# Coloca esta ruta *antes* de /ver_dashboard_hijo/<int:child_id>
@users_bp.route('/dashboard_hijo')
@child_required  # Use the custom decorator, *NOT* @login_required
def dashboard_hijo():
    return render_template('users/dashboard_hijo.html')  # Use a dedicated template for the child dashboard

@users_bp.route('/ver_dashboard_hijo/<int:child_id>')
@login_required
def ver_dashboard_hijo_redirect(child_id): # CHANGED NAME
    child = Child.query.filter_by(child_id=child_id, parent_id=current_user.user_id).first_or_404()
    response = make_response(redirect(url_for('users.dashboard_hijo')))
    response.set_cookie('child_id', str(child.child_id), httponly=True, secure=True, max_age=60*60*24*30)
    return response

@users_bp.route('/generar_token/<int:child_id>', methods=['POST'])
@login_required
def generar_token_vinculacion(child_id):
    child = Child.query.filter_by(child_id=child_id, parent_id=current_user.user_id).first_or_404()
    token = generate_token(child.child_id)

    # En lugar de redirect, renderizar el dashboard con el token
    try:
        roles = [role.role_name for role in current_user.roles]
        if 'PADRE' in roles:
            children = current_user.children.all()
            return render_template('users/dashboard.html',
                                 children=children,
                                 generated_token=token,
                                 token_child=child)
        else:
            flash(f"Token generado para {child.first_name} {child.last_name}: {token}", "success")
            return redirect(url_for('users.dashboard'))
    except Exception as e:
        flash(f"Token generado para {child.first_name} {child.last_name}: {token}", "success")
        return redirect(url_for('users.dashboard'))

# Helper function to generate tokens
def generate_token(child_id):
    alphabet = string.ascii_letters + string.digits
    while True:
        token = ''.join(secrets.choice(alphabet) for _ in range(16))  # 16 characters
        if not Token.query.filter_by(token=token).first():
            break

    expiration = datetime.now() + timedelta(hours=24)  # 24 hours expiration
    token_obj = Token(token=token, child_id=child_id, expiration=expiration)
    db.session.add(token_obj)
    db.session.commit()
    return token

@users_bp.route('/editar_hijo/<int:child_id>', methods=['GET', 'POST'])
@login_required
def editar_hijo(child_id):
    child = Child.query.filter_by(child_id=child_id, parent_id=current_user.user_id).first_or_404()
    from app.forms.user_forms import EditChildForm
    form = EditChildForm(obj=child)

    if form.validate_on_submit():
        child.first_name = form.first_name.data
        child.last_name = form.last_name.data
        db.session.commit()
        flash('Hijo actualizado exitosamente.', 'success')
        return redirect(url_for('users.gestionar_hijos'))

    return render_template('users/editar_hijo.html', form=form, child=child)

@users_bp.route('/eliminar_hijo/<int:child_id>', methods=['POST'])
@login_required
def eliminar_hijo(child_id):
    child = Child.query.filter_by(child_id=child_id, parent_id=current_user.user_id).first_or_404()
    try:
        db.session.delete(child)
        db.session.commit()
        flash('Hijo eliminado exitosamente.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error al eliminar hijo: {e}', 'danger')
    return redirect(url_for('users.gestionar_hijos'))


# ============================================================================
# ENDPOINT PARA SOLICITUD DE UBICACIÓN INMEDIATA DESDE DASHBOARD DEL PADRE
# ============================================================================

@users_bp.route('/request_child_location/<int:child_id>', methods=['POST'])
@parent_required
def request_child_location(child_id):
    """
    Ruta web para que el padre solicite ubicación inmediata desde su dashboard.
    """
    try:
        # Verificar que el hijo pertenece al padre
        child = Child.query.filter_by(child_id=child_id, parent_id=g.parent.user_id).first()
        if not child:
            return jsonify({'status': 'error', 'message': 'Hijo no encontrado'}), 404

        # Usar sistema de polling (WebSocket deshabilitado temporalmente)
        from app.routes.api import pending_location_requests
        import time

        current_time = time.time()
        pending_location_requests[child_id] = {
            'timestamp': current_time,
            'status': 'pending'
        }

        current_app.logger.info(f"📡 Padre {g.parent.user_id} solicitó ubicación inmediata de hijo {child_id} via polling")
        return jsonify({
            'status': 'success',
            'method': 'polling',
            'message': f'Solicitud enviada a {child.first_name}. Respuesta en 5-30 segundos.'
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error en request_child_location: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Error interno del servidor'}), 500