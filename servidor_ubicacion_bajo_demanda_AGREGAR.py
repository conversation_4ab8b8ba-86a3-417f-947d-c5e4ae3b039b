# ============================================================================
# CÓDIGO PARA AGREGAR AL SERVIDOR FLASK
# ============================================================================

# 1. AGREGAR AL ARCHIVO: app/routes/api.py (AL FINAL DEL ARCHIVO)
# ============================================================================

import time

# Variable global para almacenar solicitudes de ubicación pendientes
pending_location_requests = {}

@api_bp.route('/request_immediate_location', methods=['POST', 'GET'])
def request_immediate_location():
    """
    POST: Padre solicita ubicación inmediata del hijo (desde API)
    GET: Hijo verifica si hay solicitudes pendientes (polling cada 5 segundos)
    """
    try:
        child_id = request.headers.get('X-Child-ID')
        if not child_id:
            return jsonify({'status': 'error', 'message': 'Child ID requerido'}), 400
        
        child_id = int(child_id)
        
        if request.method == 'POST':
            # Padre solicita ubicación (desde API)
            current_time = time.time()
            
            # Evitar spam - máximo una solicitud cada 30 segundos
            if child_id in pending_location_requests:
                last_request = pending_location_requests[child_id]['timestamp']
                if current_time - last_request < 30:
                    return jsonify({
                        'status': 'pending_request',
                        'message': 'Solicitud ya pendiente'
                    }), 200
            
            # Crear nueva solicitud
            pending_location_requests[child_id] = {
                'timestamp': current_time,
                'status': 'pending'
            }
            
            return jsonify({
                'status': 'pending_request',
                'message': 'Solicitud de ubicación creada'
            }), 200
            
        else:  # GET
            # Hijo verifica solicitudes pendientes (polling)
            if child_id in pending_location_requests:
                request_data = pending_location_requests[child_id]
                current_time = time.time()
                
                # Si la solicitud tiene menos de 2 minutos, está pendiente
                if current_time - request_data['timestamp'] < 120:
                    # Marcar como procesada
                    del pending_location_requests[child_id]
                    
                    return jsonify({
                        'status': 'pending_request',
                        'message': 'Ubicación solicitada por padre'
                    }), 200
                else:
                    # Solicitud expirada
                    del pending_location_requests[child_id]
            
            return jsonify({
                'status': 'no_request',
                'message': 'Sin solicitudes pendientes'
            }), 200
            
    except Exception as e:
        current_app.logger.error(f"Error en request_immediate_location: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Error interno del servidor'}), 500


# ============================================================================
# 2. AGREGAR AL ARCHIVO: app/routes/users.py (AL FINAL DEL ARCHIVO)
# ============================================================================

@users_bp.route('/request_child_location/<int:child_id>', methods=['POST'])
@parent_required
def request_child_location(child_id):
    """
    Ruta web para que el padre solicite ubicación inmediata desde su dashboard.
    """
    try:
        # Verificar que el hijo pertenece al padre
        child = Child.query.filter_by(id=child_id, parent_id=g.parent.id).first()
        if not child:
            return jsonify({'status': 'error', 'message': 'Hijo no encontrado'}), 404
        
        # Importar la variable global desde api.py
        from app.routes.api import pending_location_requests
        
        # Crear solicitud pendiente
        current_time = time.time()
        pending_location_requests[child_id] = {
            'timestamp': current_time,
            'status': 'pending'
        }
        
        return jsonify({
            'status': 'success',
            'message': f'Solicitud enviada a {child.name}. La ubicación se actualizará en unos segundos.'
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error en request_child_location: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Error interno del servidor'}), 500


# ============================================================================
# 3. AGREGAR AL TEMPLATE: templates/users/dashboard_padre.html
# ============================================================================

"""
AGREGAR ESTE JAVASCRIPT AL FINAL DEL ARCHIVO dashboard_padre.html (antes de </body>):

<script>
function requestImmediateLocation(childId) {
    console.log('🎯 Solicitando ubicación inmediata para hijo:', childId);
    
    // Mostrar indicador de carga
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '⏳ Solicitando...';
    button.disabled = true;
    
    fetch('/users/request_child_location/' + childId, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showMessage(data.message, 'success');
            
            // Recargar el mapa después de 5 segundos
            setTimeout(function() {
                location.reload();
            }, 5000);
        } else {
            showMessage(data.message || 'Error al solicitar ubicación.', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Error de conexión al solicitar ubicación.', 'error');
    })
    .finally(() => {
        // Restaurar botón
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function showMessage(message, type) {
    // Crear elemento de mensaje
    const messageDiv = document.createElement('div');
    messageDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'}`;
    messageDiv.textContent = message;
    messageDiv.style.position = 'fixed';
    messageDiv.style.top = '20px';
    messageDiv.style.right = '20px';
    messageDiv.style.zIndex = '9999';
    
    document.body.appendChild(messageDiv);
    
    // Remover después de 5 segundos
    setTimeout(() => {
        messageDiv.remove();
    }, 5000);
}
</script>

Y AGREGAR ESTE BOTÓN EN EL HTML DONDE MUESTRAS CADA HIJO:

<button onclick="requestImmediateLocation({{ child.id }})" class="btn btn-primary btn-sm">
    📍 Ver Ubicación Actual
</button>
"""

# ============================================================================
# INSTRUCCIONES DE INSTALACIÓN:
# ============================================================================

"""
1. Copia el código de la sección 1 al final de: app/routes/api.py
2. Copia el código de la sección 2 al final de: app/routes/users.py  
3. Agrega el JavaScript de la sección 3 a: templates/users/dashboard_padre.html
4. Agrega el botón HTML donde muestras la lista de hijos
5. Reinicia el servidor Flask

IMPORTANTE: Asegúrate de agregar estos imports al inicio de api.py si no están:
import time
from flask import current_app
"""
