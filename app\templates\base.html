<!-- app/templates/base.html -->
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}Corredores Seguros{% endblock %}</title>

    {% block head %}
    <!-- Font <PERSON> (solo para iconos) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Enlazamos nuestro CSS principal -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <link rel="icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    <!-- Enlace para mapa -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css"/>
    <script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
    {% endblock %}

    <!-- CSS personalizado para la campanita (sin afectar el resto) -->
    <style>
        /* Estilos específicos para la campanita de notificaciones */
        .notification-bell {
            position: relative;
            display: inline-block;
            margin-left: 15px;
        }

        .notification-bell .bell-icon {
            color: white;
            font-size: 18px;
            cursor: pointer;
            text-decoration: none;
            padding: 8px;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .notification-bell .bell-icon:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            text-decoration: none;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 11px;
            font-weight: bold;
            min-width: 18px;
            text-align: center;
            line-height: 1.2;
        }

        .notifications-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            width: 350px;
            max-height: 400px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .notifications-dropdown.show {
            display: block;
        }

        .dropdown-header {
            padding: 12px 16px;
            border-bottom: 1px solid #eee;
            background-color: #f8f9fa;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-item {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .notification-item:hover {
            background-color: #f8f9fa;
        }

        .notification-item.unread {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }

        .dropdown-footer {
            padding: 8px 16px;
            text-align: center;
            border-top: 1px solid #eee;
            background-color: #f8f9fa;
        }

        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            margin: 0 2px;
        }

        .btn-small:hover {
            background-color: #f0f0f0;
        }

        .btn-primary-small {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }

        .btn-success-small {
            background-color: #28a745;
            color: white;
            border-color: #28a745;
        }

        .btn-danger-small {
            background-color: #dc3545;
            color: white;
            border-color: #dc3545;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <!-- Logo -->
            <div class="logo-container">
                <img src="{{ url_for('static', filename='images/corredores.png') }}"
                     alt="Corredores Seguros"
                     class="logo">
            </div>
            <!-- Navegación -->
            <nav>
                <ul>
                    {% if current_user.is_authenticated %}
                        <li><a href="{{ url_for('main.home') }}">Inicio</a></li>
                        <li><a href="{{ url_for('auth.logout') }}">Cerrar Sesión</a></li>

                        {% if current_user.has_role('ADMIN') %}
                            <li><a href="{{ url_for('users.dashboard') }}">Dashboard Admin</a></li>
                        {% endif %}

                        {% if current_user.has_role('SUPERVISOR') %}
                            <li><a href="{{ url_for('users.dashboard') }}">Dashboard Supervisor</a></li>
                        {% endif %}

                        {% if current_user.has_role('PADRE') %}
                            <!-- Botón de retroceso -->
                            <li><a href="javascript:history.back()" title="Volver atrás"><i class="fas fa-arrow-left"></i></a></li>
                            <!-- Campanita de Notificaciones -->
                            <li class="notification-bell">
                                <a href="#" class="bell-icon" onclick="toggleNotifications(event)" id="notificationsBell">
                                    <i class="fas fa-bell"></i>
                                    <span class="notification-badge" id="notification-badge" style="display: none;">0</span>
                                </a>
                                <div class="notifications-dropdown" id="notificationsDropdown">
                                    <div class="dropdown-header">
                                        <span><i class="fas fa-map-marker-alt" style="margin-right: 8px;"></i>Notificaciones de Zonas</span>
                                        <button class="btn-small btn-primary-small" onclick="requestNotificationPermission()" id="bell-enable-btn">
                                            <i class="fas fa-bell" style="margin-right: 4px;"></i>Activar
                                        </button>
                                    </div>
                                    <div id="notifications-dropdown-content">
                                        <div style="text-align: center; color: #666; padding: 20px;">
                                            <i class="fas fa-spinner fa-spin"></i>
                                            <p style="margin: 8px 0 0 0;">Cargando notificaciones...</p>
                                        </div>
                                    </div>
                                    <div class="dropdown-footer">
                                        <a href="{{ url_for('zone_monitoring.notifications_page') }}" class="btn-small btn-primary-small" style="text-decoration: none; color: white;">
                                            <i class="fas fa-list" style="margin-right: 4px;"></i>Ver todas
                                        </a>
                                        <button class="btn-small" onclick="refreshNotifications()">
                                            <i class="fas fa-sync-alt" style="margin-right: 4px;"></i>Actualizar
                                        </button>
                                        <button class="btn-small btn-success-small" onclick="markAllNotificationsRead()">
                                            <i class="fas fa-check-double" style="margin-right: 4px;"></i>Marcar todas
                                        </button>
                                    </div>
                                </div>
                            </li>
                            {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                              {% for category, message in messages %}
                                <div class="alert alert-{{ category }}">{{ message }}</div>
                              {% endfor %}
                            {% endif %}
                          {% endwith %}
                        {% endif %}

                        {% if current_user.has_role('OPERADOR') %}
                        <li><a href="{{ url_for('users.dashboard') }}">Dashboard Operador</a></li>
                    {% endif %}
                  <!-- Agrega más enlaces según los roles -->
                    {% else %}
                        <li><a href="{{ url_for('auth.login') }}">Iniciar Sesión</a></li>
                        <li><a href="{{ url_for('auth.register') }}">Registrarse</a></li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </header>

    <main>
        {% block content %}
        {% endblock %}
    </main>

    <footer>
        <p>© 2025 Corredores Seguros</p>
    </footer>

    <!-- JavaScript para la campanita -->
    <script>
        function toggleNotifications(event) {
            event.preventDefault();
            const dropdown = document.getElementById('notificationsDropdown');
            dropdown.classList.toggle('show');

            // Cargar notificaciones si el dropdown se abre
            if (dropdown.classList.contains('show')) {
                if (typeof loadZoneNotifications === 'function') {
                    loadZoneNotifications();
                } else {
                    // Función simplificada para páginas que no tienen la función completa
                    loadNotificationsSimple();
                }
            }
        }

        // Función simplificada para cargar notificaciones en el dropdown
        function loadNotificationsSimple() {
            const container = document.getElementById('notifications-dropdown-content');
            if (!container) return;

            // Mostrar loading
            container.innerHTML = `
                <div style="text-align: center; color: #666; padding: 20px;">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p style="margin: 8px 0 0 0;">Cargando notificaciones...</p>
                </div>
            `;

            // Cargar notificaciones
            fetch('/zone_monitoring/recent_notifications')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayNotificationsSimple(data.notifications);
                        updateNotificationBadgeSimple(data.notifications);
                    } else {
                        showNotificationsErrorSimple('Error cargando notificaciones');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotificationsErrorSimple('Error de conexión');
                });
        }

        // Función simplificada para mostrar notificaciones en el dropdown
        function displayNotificationsSimple(notifications) {
            const container = document.getElementById('notifications-dropdown-content');
            if (!container) return;

            if (notifications.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; color: #666; padding: 20px;">
                        <i class="fas fa-check-circle" style="font-size: 2em; margin-bottom: 8px;"></i>
                        <p style="margin: 0;">No hay notificaciones recientes</p>
                    </div>
                `;
                return;
            }

            let html = '';

            // Mostrar máximo 5 notificaciones en el dropdown
            const displayNotifications = notifications.slice(0, 5);

            displayNotifications.forEach((notification, index) => {
                const isUnread = !notification.viewed;
                const typeIcon = getNotificationIconSimple(notification.type);
                const typeColor = getNotificationColorSimple(notification.type);
                const timeAgo = getTimeAgoSimple(notification.created_at);

                html += `
                    <div class="notification-item ${isUnread ? 'unread' : ''}" data-notification-id="${notification.id}">
                        <div style="display: flex; align-items: flex-start;">
                            <div style="margin-right: 8px; margin-top: 2px;">
                                <i class="fas ${typeIcon}" style="color: ${typeColor};"></i>
                            </div>
                            <div style="flex: 1; min-width: 0;">
                                <div style="display: flex; align-items: center; margin-bottom: 4px;">
                                    <strong style="color: ${typeColor}; font-size: 13px;">${notification.child_name}</strong>
                                    ${isUnread ? '<span style="background: #ffc107; color: #000; padding: 1px 4px; border-radius: 3px; font-size: 9px; margin-left: 4px;">NUEVO</span>' : ''}
                                </div>
                                <p style="margin: 0 0 4px 0; font-size: 12px; line-height: 1.3; word-wrap: break-word;">${notification.message}</p>
                                <small style="color: #666; font-size: 10px;">${timeAgo}</small>
                            </div>
                            ${isUnread ? `
                                <div style="margin-left: 4px;">
                                    <button class="btn-small btn-primary-small mark-read-btn" data-notification-id="${notification.id}" onclick="markNotificationReadSimple(${notification.id})">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            });

            if (notifications.length > 5) {
                html += `
                    <div style="text-align: center; padding: 8px;">
                        <a href="/zone_monitoring/notifications" style="color: #007bff; font-size: 11px; text-decoration: none;">
                            <i class="fas fa-list" style="margin-right: 4px;"></i>
                            Ver todas las ${notifications.length} notificaciones
                        </a>
                    </div>
                `;
            }

            container.innerHTML = html;
        }

        // Funciones auxiliares simplificadas
        function getNotificationIconSimple(type) {
            switch(type) {
                case 'zone_early_warning': return 'fa-clock';
                case 'zone_late_arrival': return 'fa-exclamation-triangle';
                case 'zone_missed': return 'fa-times-circle';
                default: return 'fa-bell';
            }
        }

        function getNotificationColorSimple(type) {
            switch(type) {
                case 'zone_early_warning': return '#ffc107';
                case 'zone_late_arrival': return '#dc3545';
                case 'zone_missed': return '#343a40';
                default: return '#17a2b8';
            }
        }

        function getTimeAgoSimple(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMins / 60);
            const diffDays = Math.floor(diffHours / 24);

            if (diffMins < 1) return 'Ahora mismo';
            if (diffMins < 60) return `Hace ${diffMins} minuto${diffMins > 1 ? 's' : ''}`;
            if (diffHours < 24) return `Hace ${diffHours} hora${diffHours > 1 ? 's' : ''}`;
            return `Hace ${diffDays} día${diffDays > 1 ? 's' : ''}`;
        }

        function updateNotificationBadgeSimple(notifications) {
            const unreadCount = notifications.filter(n => !n.viewed).length;
            const badge = document.getElementById('notification-badge');

            if (badge) {
                if (unreadCount > 0) {
                    badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
                    badge.style.display = 'inline';
                } else {
                    badge.style.display = 'none';
                }
            }
        }

        function showNotificationsErrorSimple(message) {
            const container = document.getElementById('notifications-dropdown-content');
            if (container) {
                container.innerHTML = `
                    <div style="text-align: center; color: #dc3545; padding: 20px;">
                        <i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i>
                        <p style="margin: 0; font-size: 12px;">${message}</p>
                    </div>
                `;
            }
        }

        function markNotificationReadSimple(notificationId) {
            fetch(`/zone_monitoring/mark_notification_viewed/${notificationId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content')
                },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Actualizar la UI
                    const element = document.querySelector(`[data-notification-id="${notificationId}"]`);
                    if (element) {
                        element.classList.remove('unread');
                        const badge = element.querySelector('span[style*="background: #ffc107"]');
                        const button = element.querySelector('button');
                        if (badge) badge.remove();
                        if (button) button.parentElement.remove();
                    }

                    // Recargar notificaciones después de 1 segundo
                    setTimeout(() => {
                        if (typeof loadZoneNotifications === 'function') {
                            loadZoneNotifications();
                        } else {
                            loadNotificationsSimple();
                        }
                    }, 1000);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error al marcar como leída');
            });
        }

        // Función global para solicitar permisos de notificación
        function requestNotificationPermission() {
            console.log('🔔 Usuario solicitó permisos de notificación...');

            if (!("Notification" in window)) {
                alert('Tu navegador no soporta notificaciones push');
                return;
            }

            if (Notification.permission === "granted") {
                console.log('✅ Permisos ya concedidos, mostrando notificación de prueba...');
                showTestNotification();
                return;
            }

            Notification.requestPermission().then(function(permission) {
                console.log('🔔 Respuesta del usuario:', permission);

                if (permission === "granted") {
                    showWelcomeNotification();
                    showTestNotification();
                } else {
                    alert('Para recibir notificaciones de campanita, debes permitir las notificaciones en tu navegador.');
                }
            });
        }

        // Función global para mostrar notificación de bienvenida
        function showWelcomeNotification() {
            if (Notification.permission === "granted") {
                new Notification("🔔 Notificaciones Activadas", {
                    body: "Recibirás alertas cuando tus hijos no lleguen a las zonas asignadas",
                    icon: "/static/img/notification-icon.png",
                    tag: "welcome"
                });
            }
        }

        // Función global para mostrar notificación de prueba
        function showTestNotification() {
            if (Notification.permission === "granted") {
                const testNotification = new Notification("🧪 Notificación de Prueba", {
                    body: "¡Las notificaciones están funcionando correctamente!",
                    icon: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzIiIGZpbGw9IiNGRkM5MDAiLz4KPHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxNiIgeT0iMTYiPgo8cGF0aCBkPSJNMTggOEMxOCA2LjQwODcgMTcuMzY4NCA0Ljg4MjU4IDE2LjI0MjYgMy43NTczNkMxNS4xMTc0IDIuNjMyMTQgMTMuNTkxMyAyIDEyIDJDMTAuNDA4NyAyIDguODgyNTggMi42MzIxNCA3Ljc1NzM2IDMuNzU3MzZDNi42MzIxNCA0Ljg4MjU4IDYgNi40MDg3IDYgOEM2IDEwLjEyMTcgNS4xNTY5NiAxMi4xNTY2IDMuNzU3MzYgMTMuNTU1NkMyLjM1Nzc2IDE0Ljk1NSAyIDE2Ljk3ODMgMiAxOUgyMkMyMiAxNi45NzgzIDIxLjY0MjIgMTQuOTU1IDIwLjI0MjYgMTMuNTU1NkMxOC44NDMgMTIuMTU2NiAxOCAxMC4xMjE3IDE4IDhaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTMuNzMgMjFDMTMuNTU0MyAyMS4zMDMxIDEzLjMwMzEgMjEuNTU0MyAxMyAyMS43M0MxMi42OTY5IDIxLjkwNTcgMTIuMzUzNCAyMiAxMiAyMkMxMS42NDY2IDIyIDExLjMwMzEgMjEuOTA1NyAxMSAyMS43M0MxMC42OTY5IDIxLjU1NDMgMTAuNDQ1NyAyMS4zMDMxIDEwLjI3IDIxSDE0LjczSDEzLjczWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cg==",
                    tag: "test",
                    requireInteraction: false
                });

                // Auto-cerrar después de 3 segundos
                setTimeout(() => {
                    testNotification.close();
                }, 3000);
            }
        }

        // Función global para marcar todas las notificaciones como leídas
        function markAllNotificationsRead() {
            // Esta función se define específicamente en cada página que la necesite
            if (typeof markAllAsRead === 'function') {
                markAllAsRead();
            } else {
                console.log('Función markAllAsRead no disponible en esta página');
            }
        }

        // Función global para refrescar notificaciones
        function refreshNotifications() {
            // Esta función se define específicamente en cada página que la necesite
            if (typeof loadZoneNotifications === 'function') {
                loadZoneNotifications();
            } else {
                console.log('Función loadZoneNotifications no disponible en esta página');
            }
        }

        // Cerrar dropdown al hacer clic fuera
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('notificationsDropdown');
            const bell = document.getElementById('notificationsBell');

            if (dropdown && bell && !bell.contains(event.target) && !dropdown.contains(event.target)) {
                dropdown.classList.remove('show');
            }
        });
    </script>

    {% block scripts %}
      {# Se eliminó el uso de {{ super() }} ya que no existe un bloque 'scripts' en la plantilla padre #}
    {% endblock %}

</body>
</html>
