#!/usr/bin/env python3
# app/forms/resolver_incident_form.py

from flask_wtf import FlaskForm
from wtforms import TextAreaField, SubmitField
from wtforms.validators import DataRequired, Length

class ResolverIncidentForm(FlaskForm):
    # Campo para ingresar la resolución (máximo 300 caracteres)
    resolution = TextAreaField(
        'Resolución',
        validators=[
            DataRequired(message="La resolución es obligatoria."),
            Length(max=300, message="La resolución debe tener máximo 300 caracteres.")
        ]
    )
    submit = SubmitField('Resolver Incidente')
