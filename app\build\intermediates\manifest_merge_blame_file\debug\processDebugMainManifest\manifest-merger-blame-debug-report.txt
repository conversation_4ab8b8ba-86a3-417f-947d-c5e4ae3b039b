1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.corredores"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permisos de ubicación -->
12    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
12-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
13-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:7:5-81
13-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:7:22-78
14    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
14-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:8:5-85
14-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:8:22-82
15
16    <!-- Permisos de red -->
17    <uses-permission android:name="android.permission.INTERNET" />
17-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:11:5-67
17-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:11:22-64
18    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
18-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:12:5-79
18-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:12:22-76
19
20    <!-- Permisos para servicios en segundo plano -->
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
21-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:15:5-77
21-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:15:22-74
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
22-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:16:5-86
22-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:16:22-83
23    <uses-permission android:name="android.permission.WAKE_LOCK" />
23-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:17:5-68
23-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:17:22-65
24    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
24-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:18:5-95
24-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:18:22-92
25
26    <!-- Permisos para mantener la app activa -->
27    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
27-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:21:5-81
27-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:21:22-78
28    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
28-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:23:5-77
28-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:23:22-74
29    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
29-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:26:5-82
29-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:26:22-79
30    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
30-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
30-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
31    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
31-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
31-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
32    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
32-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
32-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
33    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
33-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
33-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
34
35    <permission
35-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
36        android:name="com.example.corredores.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
36-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
37        android:protectionLevel="signature" />
37-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
38
39    <uses-permission android:name="com.example.corredores.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
39-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
39-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
40
41    <application
41-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:23:5-95:19
42        android:allowBackup="true"
42-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:24:9-35
43        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
43-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
44        android:dataExtractionRules="@xml/data_extraction_rules"
44-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:25:9-65
45        android:debuggable="true"
46        android:extractNativeLibs="false"
47        android:fullBackupContent="@xml/backup_rules"
47-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:26:9-54
48        android:icon="@mipmap/ic_launcher"
48-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:27:9-43
49        android:label="@string/app_name"
49-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:28:9-41
50        android:roundIcon="@mipmap/ic_launcher_round"
50-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:29:9-54
51        android:supportsRtl="true"
51-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:30:9-35
52        android:theme="@style/Theme.Corredores" >
52-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:31:9-48
53
54        <!-- Actividad de inicio (Splash) -->
55        <activity
55-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:35:9-43:20
56            android:name="com.example.corredores.SplashActivity"
56-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:36:13-43
57            android:exported="true"
57-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:37:13-36
58            android:theme="@style/Theme.Corredores" >
58-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:38:13-52
59            <intent-filter>
59-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:39:13-42:29
60                <action android:name="android.intent.action.MAIN" />
60-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:40:17-69
60-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:40:25-66
61
62                <category android:name="android.intent.category.LAUNCHER" />
62-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:41:17-77
62-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:41:27-74
63            </intent-filter>
64        </activity>
65
66        <!-- Selección de perfil -->
67        <activity
67-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:46:9-48:40
68            android:name="com.example.corredores.ProfileSelectionActivity"
68-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:47:13-53
69            android:exported="false" />
69-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:48:13-37
70
71        <!-- Login para padres -->
72        <activity
72-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:51:9-53:40
73            android:name="com.example.corredores.LoginActivity"
73-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:52:13-42
74            android:exported="false" />
74-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:53:13-37
75
76        <!-- Token para hijos -->
77        <activity
77-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:56:9-58:40
78            android:name="com.example.corredores.TokenActivity"
78-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:57:13-42
79            android:exported="false" />
79-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:58:13-37
80
81        <!-- Actividad principal -->
82        <activity
82-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:61:9-63:40
83            android:name="com.example.corredores.MainActivity"
83-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:62:13-41
84            android:exported="false" />
84-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:63:13-37
85
86        <!-- Servicio de ubicación -->
87        <service
87-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:66:9-70:56
88            android:name="com.example.corredores.services.LocationService"
88-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:67:13-53
89            android:enabled="true"
89-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:68:13-35
90            android:exported="false"
90-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:69:13-37
91            android:foregroundServiceType="location" />
91-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:70:13-53
92
93        <!-- Servicio de Firebase Cloud Messaging para notificaciones push -->
94        <service
94-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:73:9-80:19
95            android:name="com.example.corredores.services.MyFirebaseMessagingService"
95-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:74:13-64
96            android:enabled="true"
96-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:75:13-35
97            android:exported="false" >
97-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:76:13-37
98            <intent-filter>
98-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:77:13-79:29
99                <action android:name="com.google.firebase.MESSAGING_EVENT" />
99-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:78:17-78
99-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:78:25-75
100            </intent-filter>
101        </service>
102
103        <!-- Receptor para reinicio del sistema -->
104        <receiver
104-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:83:9-93:20
105            android:name="com.example.corredores.receivers.BootReceiver"
105-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:84:13-51
106            android:enabled="true"
106-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:85:13-35
107            android:exported="true" >
107-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:86:13-36
108            <intent-filter android:priority="1000" >
108-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:87:13-92:29
108-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:87:28-51
109                <action android:name="android.intent.action.BOOT_COMPLETED" />
109-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:88:17-79
109-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:88:25-76
110                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
110-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:89:17-84
110-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:89:25-81
111                <action android:name="android.intent.action.PACKAGE_REPLACED" />
111-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:90:17-81
111-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:90:25-78
112
113                <data android:scheme="package" />
113-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:91:17-50
113-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:91:23-47
114            </intent-filter>
115        </receiver>
116
117        <service
117-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:23:9-29:19
118            android:name="com.google.firebase.components.ComponentDiscoveryService"
118-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:24:13-84
119            android:directBootAware="true"
119-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
120            android:exported="false" >
120-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:25:13-37
121            <meta-data
121-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:26:13-28:85
122                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar"
122-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:27:17-129
123                android:value="com.google.firebase.components.ComponentRegistrar" />
123-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:28:17-82
124            <meta-data
124-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:57:13-59:85
125                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
125-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:58:17-122
126                android:value="com.google.firebase.components.ComponentRegistrar" />
126-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:59:17-82
127            <meta-data
127-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:60:13-62:85
128                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
128-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:61:17-119
129                android:value="com.google.firebase.components.ComponentRegistrar" />
129-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:62:17-82
130            <meta-data
130-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b42534c1ac90d64a4f8f7e11550c6cfd\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
131                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
131-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b42534c1ac90d64a4f8f7e11550c6cfd\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
132                android:value="com.google.firebase.components.ComponentRegistrar" />
132-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b42534c1ac90d64a4f8f7e11550c6cfd\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
133            <meta-data
133-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
134                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
134-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
136            <meta-data
136-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
137                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
137-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
139            <meta-data
139-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
140                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
140-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
142            <meta-data
142-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb3e29ff08c07d3daf20d3f651cc8eee\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
143                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
143-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb3e29ff08c07d3daf20d3f651cc8eee\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb3e29ff08c07d3daf20d3f651cc8eee\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
145            <meta-data
145-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
146                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
146-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
148            <meta-data
148-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79130a54814058327ed4bab63fb1123c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
149                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
149-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79130a54814058327ed4bab63fb1123c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79130a54814058327ed4bab63fb1123c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
151        </service>
152
153        <receiver
153-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:29:9-40:20
154            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
154-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:30:13-78
155            android:exported="true"
155-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:31:13-36
156            android:permission="com.google.android.c2dm.permission.SEND" >
156-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:32:13-73
157            <intent-filter>
157-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:33:13-35:29
158                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
158-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:34:17-81
158-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:34:25-78
159            </intent-filter>
160
161            <meta-data
161-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:37:13-39:40
162                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
162-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:38:17-92
163                android:value="true" />
163-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:39:17-37
164        </receiver>
165        <!--
166             FirebaseMessagingService performs security checks at runtime,
167             but set to not exported to explicitly avoid allowing another app to call it.
168        -->
169        <service
169-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:46:9-53:19
170            android:name="com.google.firebase.messaging.FirebaseMessagingService"
170-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:47:13-82
171            android:directBootAware="true"
171-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:48:13-43
172            android:exported="false" >
172-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:49:13-37
173            <intent-filter android:priority="-500" >
173-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:77:13-79:29
174                <action android:name="com.google.firebase.MESSAGING_EVENT" />
174-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:78:17-78
174-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:78:25-75
175            </intent-filter>
176        </service>
177
178        <activity
178-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee22392c0511e6aba80092990f3de4a\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
179            android:name="com.google.android.gms.common.api.GoogleApiActivity"
179-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee22392c0511e6aba80092990f3de4a\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
180            android:exported="false"
180-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee22392c0511e6aba80092990f3de4a\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
181            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
181-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee22392c0511e6aba80092990f3de4a\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
182
183        <property
183-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
184            android:name="android.adservices.AD_SERVICES_CONFIG"
184-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
185            android:resource="@xml/ga_ad_services_config" />
185-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
186
187        <provider
187-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
188            android:name="com.google.firebase.provider.FirebaseInitProvider"
188-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
189            android:authorities="com.example.corredores.firebaseinitprovider"
189-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
190            android:directBootAware="true"
190-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
191            android:exported="false"
191-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
192            android:initOrder="100" />
192-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
193        <provider
193-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
194            android:name="androidx.startup.InitializationProvider"
194-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
195            android:authorities="com.example.corredores.androidx-startup"
195-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
196            android:exported="false" >
196-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
197            <meta-data
197-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
198                android:name="androidx.emoji2.text.EmojiCompatInitializer"
198-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
199                android:value="androidx.startup" />
199-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
200            <meta-data
200-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
201                android:name="androidx.work.WorkManagerInitializer"
201-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
202                android:value="androidx.startup" />
202-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
203            <meta-data
203-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101cca20c1e85df7a5f9f46711f93017\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
204                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
204-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101cca20c1e85df7a5f9f46711f93017\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
205                android:value="androidx.startup" />
205-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101cca20c1e85df7a5f9f46711f93017\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
206            <meta-data
206-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
207                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
207-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
208                android:value="androidx.startup" />
208-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
209        </provider>
210
211        <service
211-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
212            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
212-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
213            android:directBootAware="false"
213-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
214            android:enabled="@bool/enable_system_alarm_service_default"
214-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
215            android:exported="false" />
215-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
216        <service
216-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
217            android:name="androidx.work.impl.background.systemjob.SystemJobService"
217-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
218            android:directBootAware="false"
218-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
219            android:enabled="@bool/enable_system_job_service_default"
219-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
220            android:exported="true"
220-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
221            android:permission="android.permission.BIND_JOB_SERVICE" />
221-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
222        <service
222-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
223            android:name="androidx.work.impl.foreground.SystemForegroundService"
223-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
224            android:directBootAware="false"
224-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
225            android:enabled="@bool/enable_system_foreground_service_default"
225-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
226            android:exported="false" />
226-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
227
228        <receiver
228-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
229            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
229-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
230            android:directBootAware="false"
230-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
231            android:enabled="true"
231-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
232            android:exported="false" />
232-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
233        <receiver
233-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
234            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
234-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
235            android:directBootAware="false"
235-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
236            android:enabled="false"
236-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
237            android:exported="false" >
237-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
238            <intent-filter>
238-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
239                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
239-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
239-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
240                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
240-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
240-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
241            </intent-filter>
242        </receiver>
243        <receiver
243-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
244            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
244-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
245            android:directBootAware="false"
245-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
246            android:enabled="false"
246-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
247            android:exported="false" >
247-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
248            <intent-filter>
248-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
249                <action android:name="android.intent.action.BATTERY_OKAY" />
249-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
249-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
250                <action android:name="android.intent.action.BATTERY_LOW" />
250-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
250-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
251            </intent-filter>
252        </receiver>
253        <receiver
253-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
254            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
254-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
255            android:directBootAware="false"
255-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
256            android:enabled="false"
256-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
257            android:exported="false" >
257-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
258            <intent-filter>
258-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
259                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
259-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
259-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
260                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
260-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
260-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
261            </intent-filter>
262        </receiver>
263        <receiver
263-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
264            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
264-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
265            android:directBootAware="false"
265-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
266            android:enabled="false"
266-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
267            android:exported="false" >
267-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
268            <intent-filter>
268-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
269                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
269-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
269-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
270            </intent-filter>
271        </receiver>
272        <receiver
272-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
273            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
273-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
274            android:directBootAware="false"
274-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
275            android:enabled="false"
275-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
276            android:exported="false" >
276-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
277            <intent-filter>
277-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
278                <action android:name="android.intent.action.BOOT_COMPLETED" />
278-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:88:17-79
278-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:88:25-76
279                <action android:name="android.intent.action.TIME_SET" />
279-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
279-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
280                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
280-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
280-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
281            </intent-filter>
282        </receiver>
283        <receiver
283-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
284            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
284-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
285            android:directBootAware="false"
285-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
286            android:enabled="@bool/enable_system_alarm_service_default"
286-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
287            android:exported="false" >
287-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
288            <intent-filter>
288-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
289                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
289-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
289-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
290            </intent-filter>
291        </receiver>
292        <receiver
292-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
293            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
293-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
294            android:directBootAware="false"
294-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
295            android:enabled="true"
295-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
296            android:exported="true"
296-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
297            android:permission="android.permission.DUMP" >
297-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
298            <intent-filter>
298-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
299                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
299-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
299-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
300            </intent-filter>
301        </receiver>
302        <receiver
302-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
303            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
303-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
304            android:enabled="true"
304-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
305            android:exported="false" >
305-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
306        </receiver>
307
308        <service
308-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
309            android:name="com.google.android.gms.measurement.AppMeasurementService"
309-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
310            android:enabled="true"
310-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
311            android:exported="false" />
311-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
312        <service
312-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
313            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
313-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
314            android:enabled="true"
314-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
315            android:exported="false"
315-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
316            android:permission="android.permission.BIND_JOB_SERVICE" />
316-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
317
318        <uses-library
318-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f85ba276f1370d20237343255144de\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
319            android:name="android.ext.adservices"
319-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f85ba276f1370d20237343255144de\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
320            android:required="false" />
320-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f85ba276f1370d20237343255144de\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
321
322        <meta-data
322-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea606996c8ba6337c1770e421181b43\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
323            android:name="com.google.android.gms.version"
323-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea606996c8ba6337c1770e421181b43\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
324            android:value="@integer/google_play_services_version" />
324-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea606996c8ba6337c1770e421181b43\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
325
326        <receiver
326-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
327            android:name="androidx.profileinstaller.ProfileInstallReceiver"
327-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
328            android:directBootAware="false"
328-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
329            android:enabled="true"
329-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
330            android:exported="true"
330-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
331            android:permission="android.permission.DUMP" >
331-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
332            <intent-filter>
332-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
333                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
333-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
333-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
334            </intent-filter>
335            <intent-filter>
335-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
336                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
336-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
336-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
337            </intent-filter>
338            <intent-filter>
338-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
339                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
339-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
339-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
340            </intent-filter>
341            <intent-filter>
341-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
342                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
342-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
342-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
343            </intent-filter>
344        </receiver>
345
346        <service
346-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
347            android:name="androidx.room.MultiInstanceInvalidationService"
347-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
348            android:directBootAware="true"
348-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
349            android:exported="false" />
349-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
350        <service
350-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
351            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
351-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
352            android:exported="false" >
352-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
353            <meta-data
353-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
354                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
354-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
355                android:value="cct" />
355-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
356        </service>
357        <service
357-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
358            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
358-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
359            android:exported="false"
359-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
360            android:permission="android.permission.BIND_JOB_SERVICE" >
360-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
361        </service>
362
363        <receiver
363-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
364            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
364-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
365            android:exported="false" />
365-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
366    </application>
367
368</manifest>
