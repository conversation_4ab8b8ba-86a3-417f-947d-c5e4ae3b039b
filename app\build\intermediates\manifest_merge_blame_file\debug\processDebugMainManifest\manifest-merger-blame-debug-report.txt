1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.corredores"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permisos de ubicación -->
12    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
12-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
13-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:7:5-81
13-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:7:22-78
14    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
14-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:8:5-85
14-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:8:22-82
15
16    <!-- Permisos de red -->
17    <uses-permission android:name="android.permission.INTERNET" />
17-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:11:5-67
17-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:11:22-64
18    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
18-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:12:5-79
18-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:12:22-76
19
20    <!-- Permisos para servicios en segundo plano -->
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
21-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:15:5-77
21-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:15:22-74
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
22-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:16:5-86
22-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:16:22-83
23    <uses-permission android:name="android.permission.WAKE_LOCK" />
23-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:17:5-68
23-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:17:22-65
24    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
24-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:18:5-95
24-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:18:22-92
25
26    <!-- Permisos para mantener la app activa -->
27    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
27-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:21:5-81
27-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:21:22-78
28    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
28-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:23:5-77
28-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:23:22-74
29    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
29-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:26:5-82
29-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:26:22-79
30    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
30-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
30-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
31    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
31-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
31-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
32    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
32-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
32-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
33    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
33-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
33-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
34
35    <permission
35-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
36        android:name="com.example.corredores.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
36-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
37        android:protectionLevel="signature" />
37-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
38
39    <uses-permission android:name="com.example.corredores.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
39-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
39-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
40
41    <application
41-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:23:5-100:19
42        android:allowBackup="true"
42-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:24:9-35
43        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
43-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
44        android:dataExtractionRules="@xml/data_extraction_rules"
44-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:25:9-65
45        android:debuggable="true"
46        android:extractNativeLibs="false"
47        android:fullBackupContent="@xml/backup_rules"
47-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:26:9-54
48        android:icon="@mipmap/ic_launcher"
48-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:27:9-43
49        android:label="@string/app_name"
49-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:28:9-41
50        android:roundIcon="@mipmap/ic_launcher_round"
50-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:29:9-54
51        android:supportsRtl="true"
51-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:30:9-35
52        android:testOnly="true"
53        android:theme="@style/Theme.Corredores" >
53-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:31:9-48
54
55        <!-- Actividad de inicio (Splash) -->
56        <activity
56-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:35:9-43:20
57            android:name="com.example.corredores.SplashActivity"
57-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:36:13-43
58            android:exported="true"
58-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:37:13-36
59            android:theme="@style/Theme.Corredores" >
59-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:38:13-52
60            <intent-filter>
60-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:39:13-42:29
61                <action android:name="android.intent.action.MAIN" />
61-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:40:17-69
61-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:40:25-66
62
63                <category android:name="android.intent.category.LAUNCHER" />
63-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:41:17-77
63-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:41:27-74
64            </intent-filter>
65        </activity>
66
67        <!-- Selección de perfil -->
68        <activity
68-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:46:9-48:40
69            android:name="com.example.corredores.ProfileSelectionActivity"
69-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:47:13-53
70            android:exported="false" />
70-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:48:13-37
71
72        <!-- Login para padres -->
73        <activity
73-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:51:9-53:40
74            android:name="com.example.corredores.LoginActivity"
74-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:52:13-42
75            android:exported="false" />
75-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:53:13-37
76
77        <!-- Token para hijos -->
78        <activity
78-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:56:9-58:40
79            android:name="com.example.corredores.TokenActivity"
79-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:57:13-42
80            android:exported="false" />
80-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:58:13-37
81
82        <!-- Actividad principal -->
83        <activity
83-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:61:9-63:40
84            android:name="com.example.corredores.MainActivity"
84-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:62:13-41
85            android:exported="false" />
85-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:63:13-37
86
87        <!-- Dashboard para hijos -->
88        <activity
88-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:66:9-68:40
89            android:name="com.example.corredores.DashboardHijoActivity"
89-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:67:13-50
90            android:exported="false" />
90-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:68:13-37
91
92        <!-- Servicio de ubicación -->
93        <service
93-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:71:9-75:56
94            android:name="com.example.corredores.services.LocationService"
94-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:72:13-53
95            android:enabled="true"
95-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:73:13-35
96            android:exported="false"
96-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:74:13-37
97            android:foregroundServiceType="location" />
97-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:75:13-53
98
99        <!-- Servicio de Firebase Cloud Messaging para notificaciones push -->
100        <service
100-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:78:9-85:19
101            android:name="com.example.corredores.services.MyFirebaseMessagingService"
101-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:79:13-64
102            android:enabled="true"
102-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:80:13-35
103            android:exported="false" >
103-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:81:13-37
104            <intent-filter>
104-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:82:13-84:29
105                <action android:name="com.google.firebase.MESSAGING_EVENT" />
105-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:83:17-78
105-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:83:25-75
106            </intent-filter>
107        </service>
108
109        <!-- Receptor para reinicio del sistema -->
110        <receiver
110-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:88:9-98:20
111            android:name="com.example.corredores.receivers.BootReceiver"
111-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:89:13-51
112            android:enabled="true"
112-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:90:13-35
113            android:exported="true" >
113-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:91:13-36
114            <intent-filter android:priority="1000" >
114-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:92:13-97:29
114-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:92:28-51
115                <action android:name="android.intent.action.BOOT_COMPLETED" />
115-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:93:17-79
115-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:93:25-76
116                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
116-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:94:17-84
116-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:94:25-81
117                <action android:name="android.intent.action.PACKAGE_REPLACED" />
117-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:95:17-81
117-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:95:25-78
118
119                <data android:scheme="package" />
119-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:96:17-50
119-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:96:23-47
120            </intent-filter>
121        </receiver>
122
123        <service
123-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:23:9-29:19
124            android:name="com.google.firebase.components.ComponentDiscoveryService"
124-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:24:13-84
125            android:directBootAware="true"
125-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
126            android:exported="false" >
126-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:25:13-37
127            <meta-data
127-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:26:13-28:85
128                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar"
128-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:27:17-129
129                android:value="com.google.firebase.components.ComponentRegistrar" />
129-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:28:17-82
130            <meta-data
130-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:57:13-59:85
131                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
131-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:58:17-122
132                android:value="com.google.firebase.components.ComponentRegistrar" />
132-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:59:17-82
133            <meta-data
133-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:60:13-62:85
134                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
134-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:61:17-119
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:62:17-82
136            <meta-data
136-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b42534c1ac90d64a4f8f7e11550c6cfd\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
137                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
137-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b42534c1ac90d64a4f8f7e11550c6cfd\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b42534c1ac90d64a4f8f7e11550c6cfd\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
139            <meta-data
139-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
140                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
140-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
142            <meta-data
142-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
143                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
143-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
145            <meta-data
145-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
146                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
146-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
148            <meta-data
148-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb3e29ff08c07d3daf20d3f651cc8eee\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
149                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
149-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb3e29ff08c07d3daf20d3f651cc8eee\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb3e29ff08c07d3daf20d3f651cc8eee\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
151            <meta-data
151-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
152                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
152-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
154            <meta-data
154-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79130a54814058327ed4bab63fb1123c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
155                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
155-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79130a54814058327ed4bab63fb1123c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
156                android:value="com.google.firebase.components.ComponentRegistrar" />
156-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79130a54814058327ed4bab63fb1123c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
157        </service>
158
159        <receiver
159-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:29:9-40:20
160            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
160-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:30:13-78
161            android:exported="true"
161-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:31:13-36
162            android:permission="com.google.android.c2dm.permission.SEND" >
162-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:32:13-73
163            <intent-filter>
163-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:33:13-35:29
164                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
164-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:34:17-81
164-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:34:25-78
165            </intent-filter>
166
167            <meta-data
167-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:37:13-39:40
168                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
168-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:38:17-92
169                android:value="true" />
169-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:39:17-37
170        </receiver>
171        <!--
172             FirebaseMessagingService performs security checks at runtime,
173             but set to not exported to explicitly avoid allowing another app to call it.
174        -->
175        <service
175-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:46:9-53:19
176            android:name="com.google.firebase.messaging.FirebaseMessagingService"
176-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:47:13-82
177            android:directBootAware="true"
177-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:48:13-43
178            android:exported="false" >
178-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:49:13-37
179            <intent-filter android:priority="-500" >
179-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:82:13-84:29
180                <action android:name="com.google.firebase.MESSAGING_EVENT" />
180-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:83:17-78
180-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:83:25-75
181            </intent-filter>
182        </service>
183
184        <activity
184-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee22392c0511e6aba80092990f3de4a\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
185            android:name="com.google.android.gms.common.api.GoogleApiActivity"
185-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee22392c0511e6aba80092990f3de4a\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
186            android:exported="false"
186-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee22392c0511e6aba80092990f3de4a\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
187            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
187-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee22392c0511e6aba80092990f3de4a\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
188
189        <property
189-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
190            android:name="android.adservices.AD_SERVICES_CONFIG"
190-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
191            android:resource="@xml/ga_ad_services_config" />
191-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
192
193        <provider
193-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
194            android:name="com.google.firebase.provider.FirebaseInitProvider"
194-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
195            android:authorities="com.example.corredores.firebaseinitprovider"
195-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
196            android:directBootAware="true"
196-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
197            android:exported="false"
197-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
198            android:initOrder="100" />
198-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
199        <provider
199-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
200            android:name="androidx.startup.InitializationProvider"
200-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
201            android:authorities="com.example.corredores.androidx-startup"
201-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
202            android:exported="false" >
202-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
203            <meta-data
203-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
204                android:name="androidx.emoji2.text.EmojiCompatInitializer"
204-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
205                android:value="androidx.startup" />
205-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
206            <meta-data
206-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
207                android:name="androidx.work.WorkManagerInitializer"
207-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
208                android:value="androidx.startup" />
208-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
209            <meta-data
209-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101cca20c1e85df7a5f9f46711f93017\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
210                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
210-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101cca20c1e85df7a5f9f46711f93017\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
211                android:value="androidx.startup" />
211-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101cca20c1e85df7a5f9f46711f93017\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
212            <meta-data
212-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
213                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
213-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
214                android:value="androidx.startup" />
214-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
215        </provider>
216
217        <service
217-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
218            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
218-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
219            android:directBootAware="false"
219-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
220            android:enabled="@bool/enable_system_alarm_service_default"
220-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
221            android:exported="false" />
221-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
222        <service
222-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
223            android:name="androidx.work.impl.background.systemjob.SystemJobService"
223-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
224            android:directBootAware="false"
224-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
225            android:enabled="@bool/enable_system_job_service_default"
225-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
226            android:exported="true"
226-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
227            android:permission="android.permission.BIND_JOB_SERVICE" />
227-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
228        <service
228-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
229            android:name="androidx.work.impl.foreground.SystemForegroundService"
229-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
230            android:directBootAware="false"
230-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
231            android:enabled="@bool/enable_system_foreground_service_default"
231-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
232            android:exported="false" />
232-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
233
234        <receiver
234-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
235            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
235-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
236            android:directBootAware="false"
236-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
237            android:enabled="true"
237-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
238            android:exported="false" />
238-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
239        <receiver
239-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
240            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
240-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
241            android:directBootAware="false"
241-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
242            android:enabled="false"
242-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
243            android:exported="false" >
243-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
244            <intent-filter>
244-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
245                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
245-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
245-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
246                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
246-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
246-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
247            </intent-filter>
248        </receiver>
249        <receiver
249-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
250            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
250-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
251            android:directBootAware="false"
251-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
252            android:enabled="false"
252-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
253            android:exported="false" >
253-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
254            <intent-filter>
254-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
255                <action android:name="android.intent.action.BATTERY_OKAY" />
255-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
255-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
256                <action android:name="android.intent.action.BATTERY_LOW" />
256-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
256-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
257            </intent-filter>
258        </receiver>
259        <receiver
259-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
260            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
260-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
261            android:directBootAware="false"
261-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
262            android:enabled="false"
262-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
263            android:exported="false" >
263-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
264            <intent-filter>
264-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
265                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
265-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
265-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
266                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
266-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
266-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
267            </intent-filter>
268        </receiver>
269        <receiver
269-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
270            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
270-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
271            android:directBootAware="false"
271-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
272            android:enabled="false"
272-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
273            android:exported="false" >
273-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
274            <intent-filter>
274-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
275                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
275-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
275-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
276            </intent-filter>
277        </receiver>
278        <receiver
278-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
279            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
279-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
280            android:directBootAware="false"
280-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
281            android:enabled="false"
281-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
282            android:exported="false" >
282-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
283            <intent-filter>
283-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
284                <action android:name="android.intent.action.BOOT_COMPLETED" />
284-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:93:17-79
284-->C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:93:25-76
285                <action android:name="android.intent.action.TIME_SET" />
285-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
285-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
286                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
286-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
286-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
287            </intent-filter>
288        </receiver>
289        <receiver
289-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
290            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
290-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
291            android:directBootAware="false"
291-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
292            android:enabled="@bool/enable_system_alarm_service_default"
292-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
293            android:exported="false" >
293-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
294            <intent-filter>
294-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
295                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
295-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
295-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
296            </intent-filter>
297        </receiver>
298        <receiver
298-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
299            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
299-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
300            android:directBootAware="false"
300-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
301            android:enabled="true"
301-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
302            android:exported="true"
302-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
303            android:permission="android.permission.DUMP" >
303-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
304            <intent-filter>
304-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
305                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
305-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
305-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
306            </intent-filter>
307        </receiver>
308        <receiver
308-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
309            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
309-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
310            android:enabled="true"
310-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
311            android:exported="false" >
311-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
312        </receiver>
313
314        <service
314-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
315            android:name="com.google.android.gms.measurement.AppMeasurementService"
315-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
316            android:enabled="true"
316-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
317            android:exported="false" />
317-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
318        <service
318-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
319            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
319-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
320            android:enabled="true"
320-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
321            android:exported="false"
321-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
322            android:permission="android.permission.BIND_JOB_SERVICE" />
322-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
323
324        <uses-library
324-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f85ba276f1370d20237343255144de\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
325            android:name="android.ext.adservices"
325-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f85ba276f1370d20237343255144de\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
326            android:required="false" />
326-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f85ba276f1370d20237343255144de\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
327
328        <meta-data
328-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea606996c8ba6337c1770e421181b43\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
329            android:name="com.google.android.gms.version"
329-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea606996c8ba6337c1770e421181b43\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
330            android:value="@integer/google_play_services_version" />
330-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea606996c8ba6337c1770e421181b43\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
331
332        <receiver
332-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
333            android:name="androidx.profileinstaller.ProfileInstallReceiver"
333-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
334            android:directBootAware="false"
334-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
335            android:enabled="true"
335-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
336            android:exported="true"
336-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
337            android:permission="android.permission.DUMP" >
337-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
338            <intent-filter>
338-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
339                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
339-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
339-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
340            </intent-filter>
341            <intent-filter>
341-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
342                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
342-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
342-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
343            </intent-filter>
344            <intent-filter>
344-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
345                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
345-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
345-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
346            </intent-filter>
347            <intent-filter>
347-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
348                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
348-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
348-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
349            </intent-filter>
350        </receiver>
351
352        <service
352-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
353            android:name="androidx.room.MultiInstanceInvalidationService"
353-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
354            android:directBootAware="true"
354-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
355            android:exported="false" />
355-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
356        <service
356-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
357            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
357-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
358            android:exported="false" >
358-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
359            <meta-data
359-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
360                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
360-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
361                android:value="cct" />
361-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
362        </service>
363        <service
363-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
364            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
364-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
365            android:exported="false"
365-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
366            android:permission="android.permission.BIND_JOB_SERVICE" >
366-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
367        </service>
368
369        <receiver
369-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
370            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
370-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
371            android:exported="false" />
371-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
372    </application>
373
374</manifest>
