# app/routes/incidents.py
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, session, g, current_app
from flask_login import login_required, current_user
from flask_wtf import FlaskForm # Para acceso_operador
from app import db
# --- MODIFICADO: Importar modelos actualizados ---
from app.models import (
    Incident, User, OperatorRequest, AuditLog, SafeZone, ChildZoneAssignment,
    UserLocationLog, IncidentZone, Child, Token
)
# ---------------------------------------------
from app.forms.incident_forms import ReportIncidentForm
from app.utils.encryption import Encryption
from app.forms.resolver_incident_form import ResolverIncidentForm
import os
import json
import logging
from datetime import datetime
# Asegurarse que la importación de child_required sea correcta
from .users import child_required # Asume que está en el mismo directorio

incidents_bp = Blueprint('incidents', __name__) # El prefijo /incidents se define en app/__init__.py
logger = logging.getLogger(__name__)

@incidents_bp.route('/reportar_incidente', methods=['GET', 'POST'])
@login_required
def reportar_incidente():
    form = ReportIncidentForm() # Usa el formulario actualizado
    if 'PADRE' not in [role.role_name for role in current_user.roles]:
        flash('No tienes permiso para reportar incidencias.', 'danger')
        return redirect(url_for('users.dashboard'))

    hijos = current_user.children.all() # Obtener hijos
    if not hijos:
        flash('No tienes hijos registrados para poder reportar una incidencia.', 'warning')
        return redirect(url_for('users.dashboard'))

    # Poblar select de hijos
    form.child_id.choices = [(hijo.child_id, f"{hijo.first_name} {hijo.last_name}") for hijo in hijos]
    form.child_id.choices.insert(0, ('', 'Selecciona un hijo...')) # Añadir opción por defecto

    # Lógica para poblar dinámicamente las zonas seguras basadas en el hijo seleccionado
    # Esto se usa principalmente si el formulario falla la validación y necesita re-renderizarse
    # con el hijo previamente seleccionado y sus zonas cargadas.
    if request.method == 'POST' and form.child_id.data: # Si es POST y hay un child_id
         selected_child_id = form.child_id.data
         try:
            child_id_int = int(selected_child_id)
            assignments = ChildZoneAssignment.query.filter_by(child_id=child_id_int).all() # Usar ChildZoneAssignment
            zone_choices = []
            added_zone_ids = set()
            for assignment in assignments:
                 # Asegurarse que assignment.zone existe y no se añadió ya
                if assignment.zone and assignment.zone.zone_id not in added_zone_ids:
                    zone_choices.append(
                        (assignment.zone.zone_id, assignment.zone.zone_name or f"Zona ID {assignment.zone.zone_id}") # Usar assignment.zone
                    )
                    added_zone_ids.add(assignment.zone.zone_id)
            form.zone_ids.choices = zone_choices # Poblar form.zone_ids
            logger.debug(f"Repoblando zone_ids para hijo {selected_child_id} en POST/Error: {form.zone_ids.choices}")
         except ValueError:
             form.zone_ids.choices = [] # Si child_id no es válido
             logger.warning(f"Valor de child_id inválido '{selected_child_id}' en POST.")
         except Exception as e:
              form.zone_ids.choices = []
              logger.error(f"Error al repoblar zonas para hijo {selected_child_id}: {e}", exc_info=True)

    elif request.method == 'GET':
        form.zone_ids.choices = [] # En GET, el select de zonas empieza vacío
        logger.debug("Renderizando formulario en GET: zone_ids.choices inicializado vacío.")

    if form.validate_on_submit():
        incident_type = form.incident_type.data
        description = form.description.data
        child_id = form.child_id.data
        zone_ids = form.zone_ids.data # Obtener IDs de zonas seleccionadas
        logger.info(f"Formulario validado. Child ID: {child_id}, Zone IDs seleccionados: {zone_ids}")

        # Validar que el hijo pertenezca al padre (extra check)
        try:
            child_id_int = int(child_id)
            if not any(child.child_id == child_id_int for child in hijos):
                flash('El hijo seleccionado no pertenece a tu cuenta.', 'danger')
                # Repoblar choices de hijos
                form.child_id.choices = [(hijo.child_id, f"{hijo.first_name} {hijo.last_name}") for hijo in hijos]
                form.child_id.choices.insert(0, ('', 'Selecciona un hijo...'))
                # Mantener las zonas pobladas si ya se habían cargado
                # (la lógica de arriba ya debería haberlo hecho si child_id estaba presente)
                return render_template('incidents/reportar_incidente.html', form=form)
        except ValueError:
            flash('ID de hijo inválido.', 'danger')
            form.child_id.choices = [(hijo.child_id, f"{hijo.first_name} {hijo.last_name}") for hijo in hijos]
            form.child_id.choices.insert(0, ('', 'Selecciona un hijo...'))
            return render_template('incidents/reportar_incidente.html', form=form)

        # Crear la incidencia
        incident = Incident(
            father_id=current_user.user_id,
            child_id=child_id_int,
            incident_type=incident_type,
            description=description,
            status='open'
        )
        db.session.add(incident)
        try:
             db.session.flush() # Flush para obtener incident.incident_id antes del commit completo
             incident_id_new = incident.incident_id # Guardar el ID

             # Asociar las zonas seleccionadas con la incidencia
             if zone_ids:
                 for zone_id in zone_ids:
                     # Verificar que la zona exista y pertenezca al padre (seguridad)
                     zone_to_link = SafeZone.query.filter_by(zone_id=zone_id, father_id=current_user.user_id).first() # Usar SafeZone
                     if zone_to_link:
                          incident_zone_assoc = IncidentZone(incident_id=incident_id_new, zone_id=zone_id) # Usar IncidentZone
                          db.session.add(incident_zone_assoc)
                     else:
                          logger.warning(f"Intento de asociar zona {zone_id} no válida o no perteneciente al padre {current_user.user_id} con incidente {incident_id_new}")

             # Generar el token para el operador
             incident.generate_token() # El método está en el modelo Incident

             # Crear log de auditoría
             audit_log = AuditLog(
                 user_id=current_user.user_id,
                 action='Report Incident',
                 # --- MODIFICADO: Log menciona Zonas ---
                 details=f'Incidente ID {incident_id_new} reportado para Hijo ID {child_id}. Zonas: {zone_ids}. Token: {incident.token}'
             )
             db.session.add(audit_log)

             # Commit final de todas las operaciones
             db.session.commit()

             flash(f'Incidente reportado exitosamente. Token de acceso: {incident.token}', 'success')
             # Limpiar el formulario para un nuevo reporte
             new_form = ReportIncidentForm(formdata=None) # Crear un formulario limpio
             new_form.child_id.choices = [(hijo.child_id, f"{hijo.first_name} {hijo.last_name}") for hijo in hijos]
             new_form.child_id.choices.insert(0, ('', 'Selecciona un hijo...'))
             new_form.zone_ids.choices = [] # Empezar vacío
             # Pasar el token y el ID a la plantilla para mostrarlo
             return render_template('incidents/reportar_incidente.html', form=new_form, token=incident.token, incident_id=incident_id_new)

        except Exception as e:
             db.session.rollback() # Revertir todo si algo falla
             logger.error(f"Error al guardar incidencia o asociaciones: {e}", exc_info=True)
             flash('Error interno al guardar la incidencia.', 'danger')
             # Re-renderizar el formulario con los datos ingresados (sin limpiar)
             # Asegurarse que las zonas se repoblen si había un child_id seleccionado
             if form.child_id.data:
                # La lógica al inicio del 'if request.method == POST' ya debería haber poblado form.zone_ids.choices
                pass
             else:
                 form.zone_ids.choices = []
             return render_template('incidents/reportar_incidente.html', form=form)

    # Renderizar en GET o si el formulario no es válido
    logger.debug("Renderizando reportar_incidente.html (GET o formulario no válido)")
    # Asegurarse que las choices de hijo estén presentes
    form.child_id.choices = [(hijo.child_id, f"{hijo.first_name} {hijo.last_name}") for hijo in hijos]
    form.child_id.choices.insert(0, ('', 'Selecciona un hijo...'))
    # Si es GET, las zonas deben estar vacías
    if request.method == 'GET':
         form.zone_ids.choices = []
    # Si es un POST inválido, la lógica de arriba ya debería haber poblado las zonas si child_id estaba presente

    return render_template('incidents/reportar_incidente.html', form=form)


@incidents_bp.route('/gestionar_incidentes')
@login_required
def gestionar_incidentes():
    # (Sin cambios relacionados a zonas aquí)
    roles = [role.role_name for role in current_user.roles]
    query = Incident.query

    if 'OPERADOR' in roles:
        # Operador solo ve incidentes abiertos (o quizás asignados a él, si hubiera esa lógica)
        query = query.filter_by(status='open')
    elif 'PADRE' in roles:
        # Padre solo ve sus incidentes
        query = query.filter_by(father_id=current_user.user_id)
    elif not ('SUPERVISOR' in roles or 'ADMIN' in roles):
        # Si no es ninguno de los anteriores, no tiene permiso
        flash('No tienes permiso para gestionar incidencias.', 'danger')
        return redirect(url_for('users.dashboard'))
    # Supervisor y Admin ven todos (implícito si no se aplican otros filtros)

    incidences = query.order_by(Incident.reported_at.desc()).all()
    return render_template('incidents/gestionar_incidentes.html', incidences=incidences, roles=roles)


@incidents_bp.route('/ver_incidente/<int:incident_id>')
@login_required
def ver_incidente(incident_id):
     # (Sin cambios relacionados a zonas aquí, solo muestra info básica del incidente)
    incident = Incident.query.get_or_404(incident_id)
    roles = [role.role_name for role in current_user.roles]

    can_view = False
    if 'PADRE' in roles and incident.father_id == current_user.user_id:
        can_view = True
    elif any(role in roles for role in ['OPERADOR', 'SUPERVISOR', 'ADMIN']):
        can_view = True

    if not can_view:
        flash('No tienes permiso para ver esta incidencia.', 'danger')
        return redirect(url_for('incidents.gestionar_incidentes') if 'PADRE' in roles else url_for('users.dashboard'))

    return render_template('incidents/ver_incidente.html', incident=incident)


@incidents_bp.route('/operador/acceso/<int:incident_id>', methods=['GET', 'POST'])
@login_required
def acceso_operador(incident_id):
     # (Sin cambios relacionados a zonas aquí, solo valida el token)
    if 'OPERADOR' not in [role.role_name for role in current_user.roles]:
        flash('Acceso denegado. Solo los operadores pueden acceder.', 'danger')
        return redirect(url_for('users.dashboard'))

    incident = Incident.query.get_or_404(incident_id)
    form = FlaskForm() # Formulario base para CSRF

    if form.validate_on_submit(): # Procesa en POST
        token = request.form.get('token', '').strip() # Obtener token y limpiar espacios
        if not token:
            flash('Debe ingresar un token.', 'warning')
            return render_template('incidents/acceso_operador.html', incident=incident, form=form)

        # Validar token y expiración usando el método del modelo
        if incident.token == token and incident.is_token_valid():
            session['incident_id'] = incident.incident_id # Guardar ID en sesión para la siguiente vista
            session['incident_access_time'] = datetime.utcnow().isoformat() # Guardar tiempo de acceso (opcional)
            flash('Acceso concedido.', 'success')
            # Redirigir a la visualización de datos
            return redirect(url_for('incidents.visualizar_datos_hijo'))
        else:
            flash('Token inválido o expirado.', 'danger')
            # Loguear intento fallido
            logger.warning(f"Intento fallido de acceso a incidencia {incident_id} por usuario {current_user.user_id} con token '{token}'")
            return render_template('incidents/acceso_operador.html', incident=incident, form=form)

    # Renderizar en GET
    return render_template('incidents/acceso_operador.html', incident=incident, form=form)


@incidents_bp.route('/operador/visualizar')
@login_required
def visualizar_datos_hijo():
    if 'OPERADOR' not in [role.role_name for role in current_user.roles]:
        flash('Acceso denegado.', 'danger')
        return redirect(url_for('users.dashboard'))

    # Verificar que el operador tenga acceso a una incidencia vía sesión
    incident_id = session.get('incident_id')
    if not incident_id:
        flash('Acceso no autorizado. Debes ingresar un token válido primero.', 'warning')
        return redirect(url_for('incidents.gestionar_incidentes'))

    incident = Incident.query.get_or_404(incident_id)

    # Verificar si el token de la sesión sigue siendo válido (seguridad adicional opcional)
    # if not incident.is_token_valid():
    #     flash('El acceso ha expirado. Debes ingresar el token nuevamente.', 'warning')
    #     session.pop('incident_id', None)
    #     session.pop('incident_access_time', None)
    #     return redirect(url_for('incidents.acceso_operador', incident_id=incident_id))

    # --- MODIFICADO: Obtener y desencriptar ZONAS ---
    zones = [iz.zone for iz in incident.incident_zones if iz.zone] # Obtener SafeZone objects
    decrypted_zones_data = []
    fernet_key = current_app.config.get('FERNET_KEY') # Usar current_app.config

    if not fernet_key:
        flash('Error crítico: La clave de encriptación no está configurada en el servidor.', 'danger')
        logger.error("FATAL: FERNET_KEY no configurada para visualizar_datos_hijo.")
        # Podrías redirigir o mostrar un error más genérico
        return redirect(url_for('incidents.gestionar_incidentes'))

    encryption = Encryption(fernet_key)
    for zone in zones:
        try:
            decrypted_zone_json = encryption.decrypt(zone.encrypted_zone_data)
            zone_data = json.loads(decrypted_zone_json) # Parsear el JSON desencriptado

            # Asegurarse que los datos necesarios (lat, lon, radius) estén presentes
            if 'lat' in zone_data and 'lon' in zone_data and 'radius' in zone_data:
                decrypted_zones_data.append({
                    'zone_id': zone.zone_id,
                    'zone_name': zone.zone_name or f"Zona ID {zone.zone_id}",
                    'schedule_start': zone.schedule_start.strftime('%H:%M') if zone.schedule_start else 'N/A',
                    'schedule_end': zone.schedule_end.strftime('%H:%M') if zone.schedule_end else 'N/A',
                    # Pasar los datos clave para el mapa
                    'latitude': zone_data['lat'],
                    'longitude': zone_data['lon'],
                    'radius': zone_data['radius']
                })
            else:
                 logger.warning(f"Datos incompletos (lat/lon/radius) en zona encriptada ID {zone.zone_id} para incidente {incident_id}")
                 flash(f'Advertencia: Datos incompletos para la zona "{zone.zone_name or zone.zone_id}".', 'warning')

        except json.JSONDecodeError:
             logger.error(f"Error de JSON al parsear datos desencriptados de zona {zone.zone_id} para incidente {incident_id}", exc_info=True)
             flash(f'Error al procesar los datos de la zona "{zone.zone_name or zone.zone_id}".', 'danger')
        except Exception as e:
            logger.error(f"Error general desencriptando/procesando zona {zone.zone_id} para incidente {incident_id}: {e}", exc_info=True)
            flash(f'Error al cargar la zona "{zone.zone_name or zone.zone_id}".', 'danger')
    # ----------------------------------------------------

    # --- Obtener y desencriptar Logs de Ubicación (lógica sin cambios) ---
    logs = UserLocationLog.query.filter_by(child_id=incident.child_id).order_by(UserLocationLog.recorded_at.desc()).all()
    decrypted_logs = []
    last_location = None
    for log in logs:
        try:
            decrypted_location_str = encryption.decrypt(log.encrypted_location_data)
            decrypted_location_info = json.loads(decrypted_location_str)

            # Asumimos que el JSON desencriptado tiene 'lat' y 'lng'
            if 'lat' in decrypted_location_info and 'lng' in decrypted_location_info:
                 log_data = {
                    'recorded_at': log.recorded_at.strftime('%Y-%m-%d %H:%M:%S') if log.recorded_at else 'N/A',
                    'location_data': decrypted_location_info # Pasar el dict completo
                 }
                 decrypted_logs.append(log_data)
                 # Guardar la última ubicación válida encontrada (la primera en la lista ordenada por desc)
                 if last_location is None:
                     last_location = decrypted_location_info
            else:
                logger.warning(f"Log de ubicación inválido (sin lat/lng) ID {log.location_id} para hijo {incident.child_id} en incidente {incident_id}")
        except Exception as e:
            logger.error(f"Error desencriptando/parseando log {log.location_id} para hijo {incident.child_id} en incidente {incident_id}: {e}", exc_info=True)
            # No mostrar flash por cada log fallido, podría ser mucho spam
    # ----------------------------------------------------------------

    # --- MODIFICADO: Pasar variables actualizadas a la plantilla ---
    return render_template('incidents/visualizar_datos_hijo.html',
                           zones=zones, # Pasar la lista de objetos SafeZone (opcional, si la plantilla los usa)
                           decrypted_zones_data=decrypted_zones_data, # Pasar datos desencriptados de zonas para el mapa
                           logs=decrypted_logs, # Pasar logs desencriptados para la lista y el mapa
                           incident=incident,
                           last_location=last_location # Pasar la última ubicación para un marcador especial
                           )
    # -----------------------------------------------------------


# --- MODIFICADO: Renombrada la función y actualizada la lógica ---
@incidents_bp.route('/get_child_zones/<int:child_id>') # URL actualizada
@login_required
def get_child_zones(child_id): # Nombre actualizado
    # Verificar que el usuario sea un padre
    if 'PADRE' not in [role.role_name for role in current_user.roles]:
        logger.warning(f"Intento no autorizado de acceso a get_child_zones por usuario {current_user.user_id} (no es PADRE)")
        return jsonify({'error': 'Acceso no autorizado'}), 403

    # Verificar que el hijo pertenezca al padre actual
    child = Child.query.filter_by(child_id=child_id, parent_id=current_user.user_id).first()
    if not child:
         logger.warning(f"Padre {current_user.user_id} intentó acceder a zonas de hijo {child_id} que no le pertenece o no existe.")
         return jsonify({'error': 'Hijo no encontrado o no pertenece a tu cuenta'}), 404

    # Consultar las asignaciones de ZONAS para ese hijo
    assignments = ChildZoneAssignment.query.filter_by(child_id=child_id).all() # Usar ChildZoneAssignment

    zone_choices = []
    added_zone_ids = set()
    for assignment in assignments:
         # Asegurarse que la zona exista y no se haya añadido ya
         if assignment.zone and assignment.zone.zone_id not in added_zone_ids: # Acceder a assignment.zone
            zone_choices.append({
                'zone_id': assignment.zone.zone_id, # Devolver zone_id
                'zone_name': assignment.zone.zone_name or f"Zona ID {assignment.zone.zone_id}" # Devolver zone_name
            })
            added_zone_ids.add(assignment.zone.zone_id)

    logger.info(f"Padre {current_user.user_id} solicitó zonas para hijo {child_id}. Zonas encontradas: {len(zone_choices)}")
    # Devolver el JSON con la clave 'zones'
    return jsonify({'zones': zone_choices})
# -----------------------------------------------------------------


@incidents_bp.route('/log_location', methods=['POST'])
@child_required # Asegura que g.child esté disponible
def log_location():
    # (Sin cambios necesarios aquí por la modificación ruta->zona)
    child_id = g.child.child_id
    data = request.get_json()
    if not data:
        logger.warning(f"Intento de log_location (web) sin datos JSON para hijo {child_id}")
        return jsonify({'error': 'Se esperaba contenido JSON'}), 400

    latitude = data.get('latitude')
    longitude = data.get('longitude')
    timestamp_str = data.get('timestamp') # Timestamp opcional desde el cliente

    if not all([latitude is not None, longitude is not None]):
        logger.warning(f"Datos faltantes (lat/lng) en log_location (web) para hijo {child_id}. Datos: {data}")
        return jsonify({'error': 'Faltan latitud o longitud'}), 400

    try:
        lat_float = float(latitude)
        lon_float = float(longitude)
        # Intentar parsear timestamp si viene, si no, usar hora del servidor
        if timestamp_str:
            try:
                 # Manejar formato ISO con Z (UTC) o con offset
                 if isinstance(timestamp_str, str):
                     if timestamp_str.endswith('Z'):
                         timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                     else:
                         timestamp = datetime.fromisoformat(timestamp_str)
                 else: # Si no es string, usar hora actual
                      logger.warning(f"Timestamp no es string '{timestamp_str}' recibido de hijo {child_id}. Usando hora del servidor.")
                      timestamp = datetime.utcnow()
            except ValueError:
                 logger.warning(f"Timestamp inválido '{timestamp_str}' recibido de hijo {child_id}. Usando hora del servidor.")
                 timestamp = datetime.utcnow()
        else:
            timestamp = datetime.utcnow() # Hora del servidor si no se envió timestamp

        # Crear payload para encriptar
        location_payload = {
            'lat': lat_float,
            'lng': lon_float,
            'source': 'web_dashboard' # Indicar origen
        }
        fernet_key = current_app.config.get('FERNET_KEY')
        if not fernet_key:
            logger.error("CRITICO: FERNET_KEY no configurada para log_location (web).")
            return jsonify({'error': 'Error de configuración del servidor'}), 500

        encryption = Encryption(fernet_key)
        encrypted_location_data = encryption.encrypt(json.dumps(location_payload))

        # Guardar en BD
        log_entry = UserLocationLog(
            child_id=child_id,
            encrypted_location_data=encrypted_location_data,
            recorded_at=timestamp # Usar el timestamp determinado (cliente o servidor)
        )
        db.session.add(log_entry)
        db.session.commit()

        logger.info(f"Ubicación (web) registrada para hijo {child_id}: Lat {lat_float}, Lng {lon_float} at {timestamp}")
        return jsonify({'status': 'success', 'message': 'Ubicación registrada'}), 201 # Created

    except ValueError as ve: # Error en float()
        logger.error(f"ValueError en log_location (web) para hijo {child_id}: {ve}. Datos: {data}", exc_info=True)
        db.session.rollback()
        return jsonify({'error': 'Formato de datos inválido (lat/lng deben ser números)'}), 400
    except Exception as e:
        logger.error(f"Error inesperado en log_location (web) para hijo {child_id}: {e}", exc_info=True)
        db.session.rollback()
        return jsonify({'error': 'Error interno al registrar la ubicación'}), 500


@incidents_bp.route('/resolver_incidente/<int:incident_id>', methods=['GET', 'POST'])
@login_required
def resolver_incidente(incident_id):
    # (Sin cambios necesarios aquí por la modificación ruta->zona)
    incident = Incident.query.get_or_404(incident_id)

    # Solo operadores pueden resolver
    if 'OPERADOR' not in [role.role_name for role in current_user.roles]:
        flash('No tienes permiso para resolver incidencias.', 'danger')
        return redirect(url_for('incidents.gestionar_incidentes'))

    # No resolver si ya está resuelta/cerrada
    if incident.status != 'open':
         flash('Esta incidencia ya ha sido resuelta o cerrada.', 'warning')
         return redirect(url_for('incidents.gestionar_incidentes'))

    form = ResolverIncidentForm()

    if form.validate_on_submit():
        incident.resolution = form.resolution.data
        incident.status = 'resolved' # Cambiar estado
        incident.resolved_at = datetime.now() # Marcar hora de resolución
        incident.operator_id = current_user.user_id # Registrar quién resolvió
        incident.invalidate_token() # Invalidar el token de acceso

        try:
            # Guardar cambios en la incidencia
            db.session.commit()

            # Crear log de auditoría para la resolución
            audit_log = AuditLog(
                user_id=current_user.user_id,
                action='Resolve Incident', # Acción específica
                details=f'Incidente ID {incident.incident_id} resuelto. Resolución: {form.resolution.data}'
            )
            db.session.add(audit_log)
            db.session.commit() # Guardar log

            flash(f'Incidencia {incident_id} resuelta exitosamente.', 'success')
            # Limpiar la sesión si el operador estaba viendo esta incidencia
            if 'incident_id' in session and session['incident_id'] == incident_id:
                 session.pop('incident_id', None)
                 session.pop('incident_access_time', None) # Limpiar también el tiempo si se guardó
            return redirect(url_for('incidents.gestionar_incidentes')) # Volver a la lista

        except Exception as e:
             db.session.rollback() # Revertir si falla el commit
             logger.error(f"Error al guardar resolución para incidente {incident_id}: {e}", exc_info=True)
             flash('Error al guardar la resolución.', 'danger')
             # Re-renderizar el formulario sin redirigir
             # return render_template('incidents/resolver_incidente.html', form=form, incident=incident)

    # Renderizar en GET o si el formulario no es válido
    return render_template('incidents/resolver_incidente.html', form=form, incident=incident)


@incidents_bp.route('/auditar_incidentes')
@login_required
def auditar_incidentes():
    # (Sin cambios necesarios aquí por la modificación ruta->zona)
    if not any(role.role_name in ['SUPERVISOR', 'ADMIN'] for role in current_user.roles):
        flash('No tienes permiso para auditar incidencias.', 'danger')
        return redirect(url_for('users.dashboard'))

    # Ordenar por fecha de resolución si existe, si no por fecha de reporte (descendente)
    incidents = Incident.query.order_by(
        db.case(
            (Incident.resolved_at != None, Incident.resolved_at), # Si resolved_at no es null, usarlo
            else_=Incident.reported_at # Si no, usar reported_at
        ).desc() # Orden descendente (más recientes primero)
    ).all()

    # Podrías aquí también consultar AuditLog si necesitas más detalle que el que está en Incident
    # audit_logs = AuditLog.query.filter(AuditLog.action.like('%Incident%')).order_by(AuditLog.created_at.desc()).all()

    return render_template('incidents/auditar_incidentes.html', incidents=incidents)