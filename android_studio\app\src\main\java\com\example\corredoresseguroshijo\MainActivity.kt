package com.example.corredores

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.util.Log // Para logging (mensajes en Logcat)
import android.view.View
import android.view.inputmethod.InputMethodManager // Para ocultar teclado
import android.widget.Button
import android.widget.EditText
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast // Para mensajes flotantes rápidos al usuario
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.google.gson.Gson // Importar Gson explícitamente para parsear errores
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
// Importaciones centralizadas desde el paquete 'network'
import com.example.corredoresseguroshijo.network.ApiService         // Interfaz centralizada
import com.example.corredoresseguroshijo.network.RetrofitClient     // Cliente Retrofit centralizado
import com.example.corredoresseguroshijo.network.LinkRequest        // DTOs desde paquete network (CORRECTO)
import com.example.corredoresseguroshijo.network.LinkResponse       // DTOs desde paquete network (CORRECTO)
import com.example.corredoresseguroshijo.network.ApiErrorResponse   // DTOs desde paquete network (CORRECTO)

import java.io.IOException // Para capturar errores de red específicos


/**
 * Actividad principal dual que maneja tanto padres como hijos
 * según el tipo de usuario seleccionado
 */
class MainActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "MainActivity"
    }

    private lateinit var preferenceManager: PreferenceManager


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        preferenceManager = PreferenceManager(this)

        // Verificar que el usuario haya seleccionado un perfil
        if (!preferenceManager.hasSelectedProfile()) {
            redirectToProfileSelection()
            return
        }

        // Configurar vista según el tipo de usuario
        when (preferenceManager.getUserType()) {
            PreferenceManager.USER_TYPE_PARENT -> {
                if (preferenceManager.isLoggedIn()) {
                    setupParentView()
                } else {
                    redirectToLogin()
                }
            }
            PreferenceManager.USER_TYPE_CHILD -> {
                setupChildView()
            }
            else -> {
                redirectToProfileSelection()
            }
        }
    }

    private fun setupParentView() {
        setContentView(R.layout.activity_main_parent)

        val webView = findViewById<WebView>(R.id.webview_parent)
        val btnLogout = findViewById<Button>(R.id.btn_logout)

        // Configurar WebView para dashboard del padre
        setupWebView(webView, "https://patagoniaservers.com.ar:5004/users/dashboard_padre")

        // Configurar botón de logout
        btnLogout.setOnClickListener {
            showLogoutConfirmation()
        }

        // Inicializar FCM para notificaciones push
        initializeFCM()
    }

    private fun setupChildView() {
        setContentView(R.layout.activity_main_child)

        val webView = findViewById<WebView>(R.id.webview_child)

        // Verificar vinculación
        if (preferenceManager.getChildId() == -1L) {
            redirectToToken()
            return
        }

        // Configurar WebView para dashboard del hijo
        setupWebView(webView, "https://patagoniaservers.com.ar:5004/users/dashboard_hijo")

        // Inicializar servicio de ubicación
        startLocationService()
    }

    private fun setupWebView(webView: WebView, url: String) {
        webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            allowFileAccess = true
            allowContentAccess = true
        }

        webView.webViewClient = WebViewClient()

        // Agregar headers de autenticación si es necesario
        val headers = mutableMapOf<String, String>()

        when (preferenceManager.getUserType()) {
            PreferenceManager.USER_TYPE_PARENT -> {
                preferenceManager.getAuthToken()?.let { token ->
                    headers["Authorization"] = "Bearer $token"
                }
            }
            PreferenceManager.USER_TYPE_CHILD -> {
                val childId = preferenceManager.getChildId()
                if (childId != -1L) {
                    headers["Child-ID"] = childId.toString()
                }
            }
        }

        webView.loadUrl(url, headers)
    }

    private fun initializeFCM() {
        lifecycleScope.launch {
            try {
                val fcmManager = FCMManager(this@MainActivity)
                fcmManager.initializeForParent()
                Log.d(TAG, "FCM inicializado para padre")
            } catch (e: Exception) {
                Log.e(TAG, "Error inicializando FCM", e)
            }
        }
    }

    private fun startLocationService() {
        val intent = Intent(this, LocationService::class.java)
        startForegroundService(intent)
        Log.d(TAG, "Servicio de ubicación iniciado para hijo")
    }

    private fun showLogoutConfirmation() {
        AlertDialog.Builder(this)
            .setTitle("Cerrar Sesión")
            .setMessage("¿Está seguro que desea cerrar sesión?")
            .setPositiveButton("Sí") { _, _ ->
                performLogout()
            }
            .setNegativeButton("No", null)
            .show()
    }


    private fun performLogout() {
        lifecycleScope.launch {
            try {
                // Desregistrar FCM
                val fcmManager = FCMManager(this@MainActivity)
                fcmManager.unregisterToken()

                // Limpiar datos locales
                preferenceManager.logout()

                Log.d(TAG, "Logout completado")

                // Redirigir a selección de perfil
                redirectToProfileSelection()

            } catch (e: Exception) {
                Log.e(TAG, "Error durante logout", e)
            }
        }
    }

    // Métodos de redirección
    private fun redirectToProfileSelection() {
        val intent = Intent(this, ProfileSelectionActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    private fun redirectToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    private fun redirectToToken() {
        val intent = Intent(this, TokenActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

}