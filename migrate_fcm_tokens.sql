-- Migración para agregar tabla de tokens FCM
-- Ejecutar este script en la base de datos MySQL

-- <PERSON>rear tabla para tokens FCM
CREATE TABLE IF NOT EXISTS fcm_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    device_type VARCHAR(20) NOT NULL DEFAULT 'android',
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_used DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- Clave foránea hacia la tabla users
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    
    -- Índices para mejorar rendimiento
    INDEX idx_user_id (user_id),
    INDEX idx_device_type (device_type),
    INDEX idx_is_active (is_active),
    INDEX idx_user_device (user_id, device_type)
);

-- Comentarios para documentar la tabla
ALTER TABLE fcm_tokens COMMENT = 'Tokens FCM para notificaciones push a dispositivos móviles';

-- Verificar que la tabla se creó correctamente
DESCRIBE fcm_tokens;

-- Mostrar información sobre la tabla
SHOW CREATE TABLE fcm_tokens;
