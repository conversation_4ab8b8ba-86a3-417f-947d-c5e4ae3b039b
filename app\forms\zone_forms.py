# app/forms/zone_forms.py

from flask_wtf import FlaskForm
# Importar HiddenField para las coordenadas
from wtforms import StringField, TimeField, SelectField, SubmitField, HiddenField
# Importar validadores necesarios
from wtforms.validators import DataRequired, Length

# Formulario para crear una nueva zona segura (punto + radio fijo)
class CreateZoneForm(FlaskForm):
    zone_name = StringField('Nombre de la Zona', validators=[DataRequired(), Length(max=100)])
    schedule_start = TimeField('Hora de Inicio', format='%H:%M', validators=[DataRequired()])
    schedule_end = TimeField('Hora de Fin', format='%H:%M', validators=[DataRequired()])

    # Campos ocultos para almacenar las coordenadas del centro de la zona
    # Estos campos serán poblados por JavaScript cuando el usuario haga clic en el mapa
    latitude = HiddenField('Latitud', validators=[DataRequired(message="Debe seleccionar un punto en el mapa.")])
    longitude = HiddenField('Longitud', validators=[DataRequired(message="Debe seleccionar un punto en el mapa.")])

    # Campo para seleccionar el hijo; se llenará dinámicamente en la vista
    child_id = SelectField('Asignar al Hijo', coerce=int, validators=[DataRequired(message="Debe seleccionar un hijo.")])
    submit = SubmitField('Crear Zona Segura')

# Formulario para editar una zona segura existente
class EditZoneForm(FlaskForm):
    zone_name = StringField('Nombre de la Zona', validators=[DataRequired(), Length(max=100)])
    schedule_start = TimeField('Hora de Inicio', format='%H:%M', validators=[DataRequired()])
    schedule_end = TimeField('Hora de Fin', format='%H:%M', validators=[DataRequired()])

    # Campos ocultos para almacenar las coordenadas del centro de la zona
    latitude = HiddenField('Latitud', validators=[DataRequired(message="Debe seleccionar un punto en el mapa.")])
    longitude = HiddenField('Longitud', validators=[DataRequired(message="Debe seleccionar un punto en el mapa.")])

    # Campo para seleccionar el hijo; se llenará dinámicamente en la vista
    child_id = SelectField('Asignar al Hijo', coerce=int, validators=[DataRequired(message="Debe seleccionar un hijo.")])
    submit = SubmitField('Actualizar Zona Segura')

# Formulario simplificado para la acción de asignar en la lista (puede que solo necesite el botón)
# La lógica de selección del hijo se manejaría en la plantilla con un <select> separado.
class AssignZoneForm(FlaskForm):
    # Este formulario podría ser tan simple como solo el botón si la selección
    # del hijo se hace fuera del form en la plantilla (como en tu ver_rutas.html original).
    # Si necesitas pasar datos adicionales (como child_id) podrías añadirlos aquí
    # o más probablemente, obtenerlos de request.form en la vista.
    submit = SubmitField('Asignar a Hijo Seleccionado')