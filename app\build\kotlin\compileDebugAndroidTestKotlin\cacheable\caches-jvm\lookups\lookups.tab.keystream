  
targetContext android.app.Instrumentation  packageName android.content.Context  
AndroidJUnit4 androidx.test.ext.junit.runners  InstrumentationRegistry androidx.test.platform.app  getInstrumentation 2androidx.test.platform.app.InstrumentationRegistry  
AndroidJUnit4 com.example.corredores  ExampleInstrumentedTest com.example.corredores  InstrumentationRegistry com.example.corredores  RunWith com.example.corredores  Test com.example.corredores  assertEquals com.example.corredores  InstrumentationRegistry .com.example.corredores.ExampleInstrumentedTest  assertEquals .com.example.corredores.ExampleInstrumentedTest  
AndroidJUnit4 	org.junit  InstrumentationRegistry 	org.junit  RunWith 	org.junit  Test 	org.junit  assertEquals 	org.junit  assertEquals org.junit.Assert  RunWith org.junit.runner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 