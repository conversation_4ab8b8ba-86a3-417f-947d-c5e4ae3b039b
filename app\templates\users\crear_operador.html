<!-- app/templates/users/crear_operador.html -->
{% extends 'base.html' %}

{% block content %}
  <h1>Crear Usuario Operador</h1>
  <form method="POST" action="{{ url_for('users.crear_operador') }}">
    {{ form.hidden_tag() }}

    <div class="form-group">
      <label for="username">Usuario</label>
      {{ form.username(class="form-control", placeholder="Ingrese nombre de usuario") }}
    </div>
    
    <!-- Campo para el nombre -->
    <div class="form-group">
        <label for="first_name">Nombre</label>
        {{ form.first_name(class="form-control", placeholder="Ingrese nombre") }}
        {% for error in form.first_name.errors %}
          <span class="text-danger">{{ error }}</span>
        {% endfor %}
    </div>

    <!-- Campo para el apellido -->
    <div class="form-group">
        <label for="last_name">Apellido</label>
        {{ form.last_name(class="form-control", placeholder="Ingrese apellido") }}
        {% for error in form.last_name.errors %}
          <span class="text-danger">{{ error }}</span>
        {% endfor %}
    </div>

    <!-- Campo para la jerarquía -->
    <div class="form-group">
        <label for="hierarchy">Jerarquía</label>
        {{ form.hierarchy(class="form-control") }}
        {% for error in form.hierarchy.errors %}
          <span class="text-danger">{{ error }}</span>
        {% endfor %}
    </div>

    <!-- Campo para el legajo -->
    <div class="form-group">
        <label for="file_number">Legajo</label>
        {{ form.file_number(class="form-control", placeholder="Ingrese legajo") }}
        {% for error in form.file_number.errors %}
          <span class="text-danger">{{ error }}</span>
        {% endfor %}
    </div>

    <!-- Campo para el correo electrónico -->
    <div class="form-group">
        <label for="email">Correo Electrónico</label>
        {{ form.email(class="form-control", placeholder="Ingrese correo electrónico") }}
        {% for error in form.email.errors %}
          <span class="text-danger">{{ error }}</span>
        {% endfor %}
    </div>

    <!-- Campo para la contraseña -->
    <div class="form-group">
        <label for="password">Contraseña</label>
        {{ form.password(class="form-control", placeholder="Ingrese contraseña") }}
        {% for error in form.password.errors %}
          <span class="text-danger">{{ error }}</span>
        {% endfor %}
    </div>

    <!-- Campo para confirmar la contraseña -->
    <div class="form-group">
        <label for="confirm_password">Confirmar Contraseña</label>
        {{ form.confirm_password(class="form-control", placeholder="Repita la contraseña") }}
        {% for error in form.confirm_password.errors %}
          <span class="text-danger">{{ error }}</span>
        {% endfor %}
    </div>

    <!-- Botón de envío -->
    <button type="submit" class="btn btn-primary">{{ form.submit.label.text }}</button>
  </form>
{% endblock %}
