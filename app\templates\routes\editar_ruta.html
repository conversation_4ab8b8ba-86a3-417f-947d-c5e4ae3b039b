<!-- app/templates/routes/editar_ruta.html -->
{% extends 'base.html' %}

{% block content %}
  <h2>Editar Ruta</h2>
  <form method="POST">
    {{ form.hidden_tag() }}
    <div>
      <label for="route_name">Nombre de la Ruta:</label>
      {{ form.route_name(size=40) }}
    </div>
    <div>
      <label for="schedule_start">Hora de Inicio:</label>
      {{ form.schedule_start() }}
    </div>
    <div>
      <label for="schedule_end">Hora de Fin:</label>
      {{ form.schedule_end() }}
    </div>
    <div>
      <label for="route_data">Datos de la Ruta (GeoJSON):</label>
      <!-- <PERSON><PERSON><PERSON> mantener este textarea visible o hacerlo hidden si solo se utiliza para almacenar el GeoJSON -->
      {{ form.route_data(rows=5, cols=50, id="route_data") }}
    </div>
    <div>
      <label for="child_id">Asignar a Hijo:</label>
      {{ form.child_id() }}
    </div>
    <div>
      {{ form.submit() }}
    </div>
  </form>
  
  <!-- Contenedor del mapa -->
  <div id="map" style="height: 400px; margin-top: 15px;"></div>
  
  <a href="{{ url_for('routes.ver_rutas') }}" class="btn btn-primary btn-sm"> Volver a Mis Rutas</a>
  
  <!-- Script para inicializar el mapa y cargar el GeoJSON -->
  <script>
    // Se asume que Leaflet ya está cargado
    // Centrar el mapa en Viedma, Río Negro (coordenadas aproximadas)
    var map = L.map('map').setView([-40.8136, -62.9936], 13);
    
    // Capa de OpenStreetMap
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      maxZoom: 19,
      attribution: 'Map data © <a href="https://openstreetmap.org">OpenStreetMap</a> contributors'
    }).addTo(map);
    
    // Obtener el GeoJSON desde el valor del textarea
    // Se utiliza JSON.parse para convertir la cadena en un objeto JavaScript
    var geojsonText = document.getElementById('route_data').value;
    try {
      var geojsonData = JSON.parse(geojsonText);
      // Crear una capa GeoJSON y agregarla al mapa
      var geojsonLayer = L.geoJSON(geojsonData, {
        style: {
          color: 'red',
          weight: 3,
          opacity: 0.8
        }
      }).addTo(map);
      
      // Ajustar la vista del mapa para mostrar toda la ruta
      if (geojsonLayer.getBounds().isValid()) {
        map.fitBounds(geojsonLayer.getBounds());
      }
    } catch (e) {
      console.error("Error al parsear el GeoJSON: ", e);
    }
  </script>
{% endblock %}
