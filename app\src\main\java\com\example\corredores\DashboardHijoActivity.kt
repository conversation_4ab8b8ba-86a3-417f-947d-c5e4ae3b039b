package com.example.corredores

import android.os.Bundle
import android.webkit.CookieManager
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.appcompat.app.AppCompatActivity

class DashboardHijoActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main_child)

        val webView = findViewById<WebView>(R.id.webview_dashboard_child)

        // Configurar WebView básico
        webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            loadWithOverviewMode = true
            useWideViewPort = true
            allowFileAccess = true
            allowContentAccess = true
        }

        // Habilitar cookies
        CookieManager.getInstance().setAcceptCookie(true)
        CookieManager.getInstance().setAcceptThirdPartyCookies(webView, true)

        // WebViewClient básico
        webView.webViewClient = WebViewClient()

        // Cargar página de vinculación de dispositivo
        val url = "https://patagoniaservers.com.ar:5004/users/vincular_dispositivo"
        webView.loadUrl(url)
    }
}
