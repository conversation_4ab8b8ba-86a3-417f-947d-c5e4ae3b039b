<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <!-- El atributo 'package' NO debe estar aquí -->

    <!-- ================== Permisos ================== -->
    <!-- Acceso a Internet (para API) -->
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- Estado de la Red (opcional, para verificar conexión) -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- Ubicación Precisa (recomendado para rastreo) -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <!-- Ubicación Aproximada (se pide junto o como fallback si FINE es denegado) -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- Servicio en Primer Plano (General - API 28+) -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <!-- Servicio en Primer Plano de tipo Ubicación (Específico - API 29+) -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />

    <!-- Notificaciones (necesario para Foreground Service - API 33+) -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <!-- Ubicación en Segundo Plano (NECESARIO para seguimiento continuo) -->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

    <!-- Permisos adicionales para estabilidad -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <!-- Bluetooth (para evitar advertencias) -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <!-- ================== Aplicación ================== -->
    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.CorredoresSegurosHijo"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:targetApi="34">
        <!-- android:name=".MyApplication": Registra la clase Application personalizada -->

        <!-- Actividad Principal (Vinculación) - Punto de Entrada -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Actividad del Dashboard -->
        <activity
            android:name=".DashboardActivity"
            android:exported="false" />
            <!-- No necesita ser lanzada por otras apps -->

        <!-- Servicio de Ubicación en Primer Plano -->
        <service
            android:name=".services.LocationForegroundService"
            android:foregroundServiceType="location"
            android:exported="false" />
            <!-- No necesita ser lanzado por otras apps -->

        <!-- Puedes añadir otros componentes aquí (BroadcastReceivers, ContentProviders) si los necesitas -->

    </application>

</manifest>
