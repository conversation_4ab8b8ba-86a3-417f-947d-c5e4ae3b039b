package com.example.corredores

import android.content.Intent
import android.os.Bundle
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity
import com.example.corredores.utils.PreferenceManager

/**
 * Actividad para seleccionar el perfil de usuario (Padre o Hijo)
 * Esta selección es irreversible - solo se puede cambiar desinstalando la app
 */
class ProfileSelectionActivity : AppCompatActivity() {

    private lateinit var preferenceManager: PreferenceManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_profile_selection)

        preferenceManager = PreferenceManager(this)

        // Verificar si ya se seleccionó un perfil
        if (preferenceManager.hasSelectedProfile()) {
            redirectToNextActivity()
            return
        }

        setupButtons()
    }

    private fun setupButtons() {
        val btnParent = findViewById<Button>(R.id.btn_parent)
        val btnChild = findViewById<Button>(R.id.btn_child)

        btnParent.setOnClickListener {
            selectProfile(PreferenceManager.USER_TYPE_PARENT)
        }

        btnChild.setOnClickListener {
            selectProfile(PreferenceManager.USER_TYPE_CHILD)
        }
    }

    private fun selectProfile(userType: String) {
        // Guardar selección de forma permanente
        preferenceManager.setUserType(userType)

        // Redirigir según el tipo seleccionado
        val intent = when (userType) {
            PreferenceManager.USER_TYPE_PARENT -> {
                Intent(this, LoginActivity::class.java)
            }
            PreferenceManager.USER_TYPE_CHILD -> {
                Intent(this, TokenActivity::class.java)
            }
            else -> {
                Intent(this, MainActivity::class.java)
            }
        }

        startActivity(intent)
        finish()
    }

    private fun redirectToNextActivity() {
        val userType = preferenceManager.getUserType()
        val intent = when (userType) {
            PreferenceManager.USER_TYPE_PARENT -> {
                if (preferenceManager.isLoggedIn()) {
                    Intent(this, MainActivity::class.java)
                } else {
                    Intent(this, LoginActivity::class.java)
                }
            }
            PreferenceManager.USER_TYPE_CHILD -> {
                Intent(this, MainActivity::class.java)
            }
            else -> {
                return // No redirigir si no hay tipo válido
            }
        }

        startActivity(intent)
        finish()
    }

    override fun onBackPressed() {
        // Deshabilitar botón de retroceso para evitar salir sin seleccionar
        // No hacer nada - forzar selección
    }
}
