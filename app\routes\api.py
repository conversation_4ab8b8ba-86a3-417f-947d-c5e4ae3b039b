# app/routes/api.py
from flask import Blueprint, jsonify, request, g, current_app
from flask_login import login_required, current_user
from app import db # No necesitas csrf aquí porque lo eximimos en __init__.py
from app.models import Token, Child, UserLocationLog, ChildNotification
from app.utils.encryption import Encryption
# Importa el decorador desde donde lo hayas creado (ej. app/utils/decorators.py)
from app.utils.decorators import api_child_required
import os
import json
import time
from datetime import datetime
from math import radians, cos, sin, asin, sqrt

# Define el Blueprint con prefijo /api
api_bp = Blueprint('api', __name__, url_prefix='/api')

# --- Funciones auxiliares ---
def haversine_distance(lat1, lon1, lat2, lon2):
    """
    Calcula la distancia entre dos puntos en la Tierra usando la fórmula de Haversine.
    Retorna la distancia en metros.
    """
    # Convertir grados a radianes
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])

    # Diferencias
    dlat = lat2 - lat1
    dlon = lon2 - lon1

    # Fórmula de Haversine
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a))

    # Radio de la Tierra en metros
    r = 6371000

    return c * r

def check_location_jump(child_id, new_lat, new_lon, new_timestamp):
    """
    Verifica si hay un salto significativo en la ubicación que podría indicar
    una desconexión o pérdida de señal.
    """
    try:
        # Obtener la última ubicación registrada
        last_log = UserLocationLog.query.filter_by(child_id=child_id)\
                                       .order_by(UserLocationLog.recorded_at.desc())\
                                       .first()

        if not last_log:
            return False  # Primera ubicación, no hay salto

        # Desencriptar la última ubicación
        fernet_key = current_app.config.get('FERNET_KEY')
        if not fernet_key:
            return False

        encryption = Encryption(fernet_key)
        last_location_json = encryption.decrypt(last_log.encrypted_location_data)
        last_location_data = json.loads(last_location_json)

        last_lat = last_location_data.get('lat')
        last_lng = last_location_data.get('lng')

        if not last_lat or not last_lng:
            return False

        # Calcular distancia entre ubicaciones
        distance = haversine_distance(last_lat, last_lng, new_lat, new_lon)

        # Calcular tiempo transcurrido
        time_diff = (new_timestamp - last_log.recorded_at).total_seconds()

        # Definir umbrales para detectar saltos
        MAX_SPEED_MPS = 50  # 50 m/s = 180 km/h (velocidad máxima razonable)
        MIN_TIME_GAP = 300  # 5 minutos sin actualizaciones

        # Detectar salto si:
        # 1. La velocidad implícita es demasiado alta
        # 2. Hay un gran gap de tiempo sin actualizaciones
        if time_diff > 0:
            implied_speed = distance / time_diff
            if implied_speed > MAX_SPEED_MPS or time_diff > MIN_TIME_GAP:
                current_app.logger.warning(
                    f"Salto detectado para hijo {child_id}: "
                    f"Distancia: {distance:.2f}m, Tiempo: {time_diff:.2f}s, "
                    f"Velocidad: {implied_speed:.2f}m/s"
                )
                return True

        return False

    except Exception as e:
        current_app.logger.error(f"Error verificando salto de ubicación: {e}")
        return False

# --- Endpoints para la Aplicación Android del Hijo ---

@api_bp.route('/ping', methods=['GET'])
def ping():
    """ Endpoint de prueba para verificar que la API está activa. """
    return jsonify({'status': 'success', 'message': 'API pong!'})


@api_bp.route('/link_device', methods=['POST'])
def link_device():
    """
    Endpoint para vincular un dispositivo Android usando un token.
    Espera un JSON en el cuerpo con {'token': 'TOKEN_STRING'}.
    Valida el token y, si es correcto, lo marca como usado y
    devuelve el child_id asociado para que la app Android lo guarde.
    """
    if not request.is_json:
        return jsonify({'status': 'error', 'message': 'Se esperaba contenido JSON'}), 400

    data = request.get_json()
    token_str = data.get('token')

    if not token_str:
        return jsonify({'status': 'error', 'message': 'Falta el campo "token" en el JSON'}), 400

    # Buscamos el token quitando posibles espacios en blanco
    token = Token.query.filter_by(token=token_str.strip()).first()

    # Validaciones del token
    if not token:
        return jsonify({'status': 'error', 'message': 'Token inválido'}), 404 # Not Found
    if token.used:
        # Devolver un código específico como 410 Gone puede ser útil
        return jsonify({'status': 'error', 'message': 'Token ya utilizado'}), 410 # Gone
    if token.expiration < datetime.now():
        return jsonify({'status': 'error', 'message': 'Token expirado'}), 410 # Gone

    try:
        # Marcar el token como usado
        token.used = True
        # Opcional: Podrías guardar información del dispositivo si la app la envía
        # device_info = data.get('device_info', None)
        # if device_info:
        #     token.device_info = json.dumps(device_info) # Guardar como JSON string

        db.session.commit()

        # Devolvemos el child_id para que la app lo almacene
        current_app.logger.info(f"Dispositivo vinculado exitosamente para child_id: {token.child_id}")
        return jsonify({
            'status': 'success',
            'message': 'Dispositivo vinculado exitosamente.',
            'child_id': token.child_id
        }), 200 # OK

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error al marcar token {token_str} como usado: {e}", exc_info=True)
        return jsonify({'status': 'error', 'message': 'Error interno del servidor al procesar el token'}), 500


@api_bp.route('/log_location', methods=['POST'])
@api_child_required # Protege la ruta y establece g.child
def log_location_api():
    """
    Endpoint API para que el hijo (autenticado por X-Child-ID) registre su ubicación.
    Espera un JSON en el cuerpo con:
    {
        'latitude': float,
        'longitude': float,
        'timestamp': 'ISO_8601_FORMAT_STRING' // Ej: "2023-10-27T10:30:00Z" o "2023-10-27T07:30:00-03:00"
        'accuracy': float (opcional)
    }
    """
    # El decorador @api_child_required ya verificó la cabecera X-Child-ID
    # y tenemos el objeto Child en g.child

    if not request.is_json:
        return jsonify({'status': 'error', 'message': 'Se esperaba contenido JSON'}), 400

    data = request.get_json()
    latitude = data.get('latitude')
    longitude = data.get('longitude')
    timestamp_str = data.get('timestamp') # Timestamp enviado desde Android
    accuracy = data.get('accuracy') # Exactitud opcional

    # Validar que los datos esenciales estén presentes
    if not all([latitude is not None, longitude is not None, timestamp_str]):
        return jsonify({'status': 'error', 'message': 'Faltan campos requeridos (latitude, longitude, timestamp)'}), 400

    try:
        # Convertir a los tipos correctos y parsear timestamp
        lat = float(latitude)
        lon = float(longitude)
        acc = float(accuracy) if accuracy is not None else None

        # Parsear timestamp ISO 8601 correctamente
        # Python < 3.11 no soporta 'Z' directamente en fromisoformat
        if timestamp_str.endswith('Z'):
            timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        else:
            # Intentar parsear directamente (manejará offsets como -03:00)
            timestamp = datetime.fromisoformat(timestamp_str)

        # Preparar datos para encriptar
        location_payload = {'lat': lat, 'lng': lon}
        if acc is not None:
            location_payload['accuracy'] = acc

        # Encriptar usando la clave Fernet de la configuración
        fernet_key = current_app.config.get('FERNET_KEY')
        if not fernet_key:
             current_app.logger.error("CRÍTICO: FERNET_KEY no está configurada en el servidor!")
             # No devolver detalles internos del error al cliente
             return jsonify({'status': 'error', 'message': 'Error de configuración del servidor'}), 500

        encryption = Encryption(fernet_key)
        encrypted_data = encryption.encrypt(json.dumps(location_payload))

        # Verificar si hay un salto significativo en la ubicación
        should_interpolate = check_location_jump(g.child.child_id, lat, lon, timestamp)

        # Crear y guardar el log en la base de datos
        log_entry = UserLocationLog(
            child_id=g.child.child_id, # Obtenido del decorador
            encrypted_location_data=encrypted_data,
            recorded_at=timestamp # Usamos el timestamp proporcionado por el dispositivo
        )
        db.session.add(log_entry)

        # Si hay un salto significativo, marcar para análisis posterior
        if should_interpolate:
            current_app.logger.warning(f"Salto de ubicación detectado para hijo {g.child.child_id}: posible desconexión")

        db.session.commit()

        current_app.logger.info(f"API: Ubicación registrada para hijo {g.child.child_id}")
        return jsonify({'status': 'success', 'message': 'Ubicación registrada'}), 201 # Created

    except ValueError as ve:
        # Error al convertir lat/lon/accuracy a float o al parsear fecha
        current_app.logger.warning(f"API: Error de formato en datos de ubicación para hijo {g.child.child_id}: {ve}")
        return jsonify({'status': 'error', 'message': f'Formato de datos inválido: {ve}'}), 400
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"API: Error al registrar ubicación para hijo {g.child.child_id}: {e}", exc_info=True)
        return jsonify({'status': 'error', 'message': 'Error interno al registrar la ubicación'}), 500


@api_bp.route('/send_notification', methods=['POST'])
@api_child_required # Protege la ruta y establece g.child
def send_notification_api():
    """
    Endpoint API para que el hijo (autenticado por X-Child-ID) envíe una notificación al padre.
    Espera un JSON en el cuerpo con:
    {
        'notification_type': 'string' // Ej: 'estoy_bien', 'ayuda', 'demorado', 'otro'
        'message_text': 'string' (opcional)
    }
    """
    # g.child está disponible gracias al decorador

    if not request.is_json:
        return jsonify({'status': 'error', 'message': 'Se esperaba contenido JSON'}), 400

    data = request.get_json()
    notification_type = data.get('notification_type')
    message_text = data.get('message_text') # Mensaje opcional desde la app

    # Validar el tipo de notificación
    allowed_types = ['estoy_bien', 'ayuda', 'demorado', 'otro'] # Tipos predefinidos
    if not notification_type or notification_type not in allowed_types:
        return jsonify({
            'status': 'error',
            'message': f'Falta el campo "notification_type" o es inválido. Permitidos: {", ".join(allowed_types)}'
        }), 400

    # Usar el mensaje proporcionado o generar uno por defecto
    if not message_text:
        message_text = f'Notificación automática: {notification_type.replace("_", " ").capitalize()}'

    try:
        # Crear la notificación en la base de datos
        notification = ChildNotification(
            child_id=g.child.child_id,
            father_id=g.child.parent_id, # El parent_id está en el modelo Child
            notification_type=notification_type,
            message_text=message_text
        )
        db.session.add(notification)
        db.session.commit()

        current_app.logger.info(f"API: Notificación '{notification_type}' enviada por hijo {g.child.child_id} a padre {g.child.parent_id}")

        # --- Punto de extensión futuro: Enviar notificación Push al Padre ---
        # Aquí iría la lógica para buscar el dispositivo del padre (si se registra)
        # y enviar una notificación push usando un servicio como Firebase Cloud Messaging (FCM).
        # Ejemplo conceptual:
        # fcm_token = get_father_device_token(g.child.parent_id)
        # if fcm_token:
        #     send_push_notification(fcm_token, title=f"Notificación de {g.child.first_name}", body=message_text)
        # -------------------------------------------------------------------

        return jsonify({'status': 'success', 'message': 'Notificación enviada'}), 201 # Created

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"API: Error al enviar notificación para hijo {g.child.child_id}: {e}", exc_info=True)
        return jsonify({'status': 'error', 'message': 'Error interno al enviar la notificación'}), 500


@api_bp.route('/get_route/<int:child_id>', methods=['GET'])
def get_interpolated_route(child_id):
    """
    Endpoint para obtener la ruta de un hijo con interpolación inteligente.
    Detecta saltos y los marca como líneas discontinuas.
    """
    try:
        # Obtener todas las ubicaciones del hijo ordenadas por tiempo
        logs = UserLocationLog.query.filter_by(child_id=child_id)\
                                   .order_by(UserLocationLog.recorded_at.asc())\
                                   .all()

        if not logs:
            return jsonify({'status': 'success', 'route': [], 'message': 'No hay ubicaciones registradas'}), 200

        # Desencriptar y procesar ubicaciones
        fernet_key = current_app.config.get('FERNET_KEY')
        if not fernet_key:
            return jsonify({'status': 'error', 'message': 'Error de configuración del servidor'}), 500

        encryption = Encryption(fernet_key)
        route_points = []

        for i, log in enumerate(logs):
            try:
                # Desencriptar ubicación
                location_json = encryption.decrypt(log.encrypted_location_data)
                location_data = json.loads(location_json)

                lat = location_data.get('lat')
                lng = location_data.get('lng')
                accuracy = location_data.get('accuracy')

                if lat is None or lng is None:
                    continue

                # Determinar si hay un salto desde el punto anterior
                is_jump = False
                if i > 0:
                    prev_log = logs[i-1]
                    prev_location_json = encryption.decrypt(prev_log.encrypted_location_data)
                    prev_location_data = json.loads(prev_location_json)

                    prev_lat = prev_location_data.get('lat')
                    prev_lng = prev_location_data.get('lng')

                    if prev_lat and prev_lng:
                        distance = haversine_distance(prev_lat, prev_lng, lat, lng)
                        time_diff = (log.recorded_at - prev_log.recorded_at).total_seconds()

                        if time_diff > 0:
                            implied_speed = distance / time_diff
                            # Marcar como salto si la velocidad es mayor a 50 m/s o hay más de 5 minutos de gap
                            if implied_speed > 50 or time_diff > 300:
                                is_jump = True

                route_points.append({
                    'lat': lat,
                    'lng': lng,
                    'timestamp': log.recorded_at.isoformat(),
                    'accuracy': accuracy,
                    'is_jump': is_jump  # Indica si este punto representa un salto
                })

            except Exception as e:
                current_app.logger.error(f"Error procesando ubicación {log.location_id}: {e}")
                continue

        return jsonify({
            'status': 'success',
            'route': route_points,
            'total_points': len(route_points)
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error obteniendo ruta para hijo {child_id}: {e}", exc_info=True)
        return jsonify({'status': 'error', 'message': 'Error interno al obtener la ruta'}), 500


# ============================================================================
# SISTEMA DE UBICACIÓN BAJO DEMANDA MEJORADO
# ============================================================================

# Variable global para almacenar solicitudes de ubicación pendientes
pending_location_requests = {}

# Variable global para almacenar conexiones activas de hijos
active_child_connections = {}

@api_bp.route('/request_immediate_location', methods=['POST', 'GET'])
def request_immediate_location():
    """
    POST: Padre solicita ubicación inmediata del hijo (desde API)
    GET: Hijo verifica si hay solicitudes pendientes (polling cada 5 segundos)
    """
    try:
        child_id = request.headers.get('X-Child-ID')
        if not child_id:
            return jsonify({'status': 'error', 'message': 'Child ID requerido'}), 400

        child_id = int(child_id)

        if request.method == 'POST':
            # Padre solicita ubicación (desde API)
            current_time = time.time()

            # Evitar spam - máximo una solicitud cada 30 segundos
            if child_id in pending_location_requests:
                last_request = pending_location_requests[child_id]['timestamp']
                if current_time - last_request < 30:
                    return jsonify({
                        'status': 'pending_request',
                        'message': 'Solicitud ya pendiente'
                    }), 200

            # Crear nueva solicitud
            pending_location_requests[child_id] = {
                'timestamp': current_time,
                'status': 'pending'
            }

            current_app.logger.info(f"Solicitud de ubicación inmediata creada para hijo {child_id}")

            return jsonify({
                'status': 'pending_request',
                'message': 'Solicitud de ubicación creada'
            }), 200

        else:  # GET
            # Hijo verifica solicitudes pendientes (polling)
            if child_id in pending_location_requests:
                request_data = pending_location_requests[child_id]
                current_time = time.time()

                # Si la solicitud tiene menos de 2 minutos, está pendiente
                if current_time - request_data['timestamp'] < 120:
                    # Marcar como procesada
                    del pending_location_requests[child_id]

                    current_app.logger.info(f"🚨 Hijo {child_id} verificó solicitudes pendientes - SOLICITUD ENCONTRADA")

                    return jsonify({
                        'status': 'pending_request',
                        'message': 'Ubicación solicitada por padre',
                        'urgent': True,
                        'next_poll_interval': 2  # Polling cada 2 segundos para próximas solicitudes
                    }), 200
                else:
                    # Solicitud expirada
                    del pending_location_requests[child_id]

            return jsonify({
                'status': 'no_request',
                'message': 'Sin solicitudes pendientes',
                'next_poll_interval': 30  # Polling normal cada 30 segundos
            }), 200

    except Exception as e:
        current_app.logger.error(f"Error en request_immediate_location: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Error interno del servidor'}), 500


@api_bp.route('/register_connection', methods=['POST'])
def register_child_connection():
    """
    Endpoint para que el hijo registre que está conectado y listo para recibir solicitudes.
    Esto permite notificación inmediata en lugar de polling.
    """
    try:
        child_id = request.headers.get('X-Child-ID')
        if not child_id:
            return jsonify({'status': 'error', 'message': 'Child ID requerido'}), 400

        child_id = int(child_id)
        current_time = time.time()

        # Registrar conexión activa
        active_child_connections[child_id] = {
            'timestamp': current_time,
            'status': 'connected'
        }

        current_app.logger.info(f"✅ Hijo {child_id} registró conexión activa")

        return jsonify({
            'status': 'success',
            'message': 'Conexión registrada',
            'polling_interval': 30  # Polling normal cada 30 segundos
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error en register_connection: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Error interno del servidor'}), 500


@api_bp.route('/check_connection_status/<int:child_id>', methods=['GET'])
def check_child_connection_status(child_id):
    """
    Endpoint para verificar si un hijo está conectado y puede recibir solicitudes inmediatas.
    """
    try:
        if child_id in active_child_connections:
            connection_data = active_child_connections[child_id]
            current_time = time.time()

            # Si la conexión es reciente (menos de 2 minutos), está activa
            if current_time - connection_data['timestamp'] < 120:
                return jsonify({
                    'status': 'connected',
                    'message': 'Hijo conectado y disponible'
                }), 200
            else:
                # Conexión expirada
                del active_child_connections[child_id]

        return jsonify({
            'status': 'disconnected',
            'message': 'Hijo no conectado'
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error en check_connection_status: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Error interno del servidor'}), 500

@api_bp.route('/check_location_update/<int:child_id>', methods=['GET'])
def check_location_update(child_id):
    """
    Endpoint para verificar si hay una nueva ubicación disponible para un hijo
    después de una solicitud de ubicación inmediata.
    """
    try:
        from app.models import UserLocationLog
        from datetime import datetime, timedelta

        # Verificar si hay una solicitud pendiente para este hijo
        if child_id in pending_location_requests:
            request_data = pending_location_requests[child_id]
            request_timestamp = request_data['timestamp']

            # Buscar ubicaciones enviadas después de la solicitud
            request_datetime = datetime.fromtimestamp(request_timestamp)

            recent_location = UserLocationLog.query.filter(
                UserLocationLog.child_id == child_id,
                UserLocationLog.timestamp > request_datetime
            ).order_by(UserLocationLog.timestamp.desc()).first()

            if recent_location:
                # Nueva ubicación encontrada - limpiar solicitud pendiente
                del pending_location_requests[child_id]
                current_app.logger.info(f"✅ Nueva ubicación detectada para hijo {child_id}")

                return jsonify({
                    'status': 'updated',
                    'message': 'Nueva ubicación disponible',
                    'timestamp': recent_location.timestamp.strftime('%Y-%m-%d %H:%M:%S')
                }), 200
            else:
                # Aún no hay nueva ubicación
                return jsonify({
                    'status': 'waiting',
                    'message': 'Esperando nueva ubicación'
                }), 200
        else:
            # No hay solicitud pendiente
            return jsonify({
                'status': 'no_request',
                'message': 'No hay solicitud pendiente'
            }), 200

    except Exception as e:
        current_app.logger.error(f"Error en check_location_update: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Error interno del servidor'}), 500


@api_bp.route('/register_fcm_token', methods=['POST'])
@login_required
def register_fcm_token():
    """
    Endpoint para registrar tokens FCM de dispositivos móviles.
    Solo para usuarios con rol PADRE.

    Espera JSON:
    {
        "fcm_token": "string",
        "device_type": "android|ios|web"
    }
    """
    # Verificar que el usuario sea un padre
    if not current_user.has_role('PADRE'):
        return jsonify({'status': 'error', 'message': 'Solo los padres pueden registrar tokens FCM'}), 403

    if not request.is_json:
        return jsonify({'status': 'error', 'message': 'Se esperaba contenido JSON'}), 400

    data = request.get_json()
    fcm_token = data.get('fcm_token')
    device_type = data.get('device_type', 'android')

    if not fcm_token:
        return jsonify({'status': 'error', 'message': 'Token FCM es requerido'}), 400

    try:
        from app.models import FCMToken
        from datetime import datetime

        # Buscar si ya existe un token para este usuario y dispositivo
        existing_token = FCMToken.query.filter_by(
            user_id=current_user.user_id,
            device_type=device_type
        ).first()

        if existing_token:
            # Actualizar token existente
            existing_token.token = fcm_token
            existing_token.is_active = True
            existing_token.last_used = datetime.utcnow()
            existing_token.updated_at = datetime.utcnow()

            current_app.logger.info(f"Token FCM actualizado para padre {current_user.user_id}")
        else:
            # Crear nuevo token
            new_token = FCMToken(
                user_id=current_user.user_id,
                token=fcm_token,
                device_type=device_type,
                is_active=True
            )
            db.session.add(new_token)

            current_app.logger.info(f"Nuevo token FCM registrado para padre {current_user.user_id}")

        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': 'Token FCM registrado exitosamente'
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error registrando token FCM: {e}")
        return jsonify({'status': 'error', 'message': 'Error interno del servidor'}), 500


@api_bp.route('/unregister_fcm_token', methods=['DELETE'])
@login_required
def unregister_fcm_token():
    """
    Endpoint para desregistrar tokens FCM (al hacer logout).
    Solo para usuarios con rol PADRE.
    """
    # Verificar que el usuario sea un padre
    if not current_user.has_role('PADRE'):
        return jsonify({'status': 'error', 'message': 'Solo los padres pueden desregistrar tokens FCM'}), 403

    try:
        from app.models import FCMToken
        from datetime import datetime

        # Desactivar todos los tokens del usuario
        tokens = FCMToken.query.filter_by(user_id=current_user.user_id).all()

        for token in tokens:
            token.is_active = False
            token.updated_at = datetime.utcnow()

        db.session.commit()

        current_app.logger.info(f"Tokens FCM desregistrados para padre {current_user.user_id}")

        return jsonify({
            'status': 'success',
            'message': 'Tokens FCM desregistrados exitosamente'
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error desregistrando tokens FCM: {e}")
        return jsonify({'status': 'error', 'message': 'Error interno del servidor'}), 500