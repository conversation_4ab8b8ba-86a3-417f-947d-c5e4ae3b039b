# 📱 Corredores Seguros - Aplicación Android (Hijo)

## 📋 Descripción

Aplicación Android para el sistema **Corredores Seguros** que permite el seguimiento de ubicación en tiempo real de hijos por parte de sus padres. Esta aplicación se instala en el dispositivo del hijo y se comunica con el servidor Flask para enviar ubicaciones y recibir notificaciones.

## 🏗️ Arquitectura

### **Componentes Principales:**

- **MainActivity**: Pantalla de vinculación con token
- **DashboardActivity**: Dashboard principal del hijo
- **LocationForegroundService**: Servicio de seguimiento de ubicación en segundo plano
- **WebSocketManager**: Gestión de comunicación en tiempo real
- **RetrofitClient**: Cliente HTTP para API REST
- **PreferenceManager**: Gestión de preferencias locales

### **Estructura del Proyecto:**

```
app/
├── src/main/
│   ├── java/com/example/corredoresseguroshijo/
│   │   ├── MainActivity.kt                    # Pantalla de vinculación
│   │   ├── DashboardActivity.kt              # Dashboard principal
│   │   ├── MyApplication.kt                  # Configuración global
│   │   ├── network/                          # Capa de red
│   │   │   ├── ApiService.kt                 # Interfaz API REST
│   │   │   ├── RetrofitClient.kt             # Cliente HTTP
│   │   │   └── WebSocketManager.kt           # WebSocket manager
│   │   ├── services/                         # Servicios
│   │   │   └── LocationForegroundService.kt  # Servicio de ubicación
│   │   └── utils/                            # Utilidades
│   │       └── PreferenceManager.kt          # Gestión de preferencias
│   ├── AndroidManifest.xml                   # Configuración de la app
│   └── res/                                  # Recursos (layouts, strings, etc.)
├── build.gradle.kts                          # Configuración del módulo
└── proguard-rules.pro                        # Reglas de ofuscación
```

## 🔧 Configuración

### **Dependencias Principales:**

- **Retrofit 2.9.0**: Cliente HTTP para API REST
- **Gson 2.10.1**: Serialización/deserialización JSON
- **Google Play Services Location 21.2.0**: Servicios de ubicación
- **Socket.IO Client 2.0.1**: WebSocket para tiempo real
- **Kotlin Coroutines 1.7.3**: Programación asíncrona
- **AndroidX Lifecycle 2.8.1**: Gestión del ciclo de vida

### **Permisos Requeridos:**

- `ACCESS_FINE_LOCATION`: Ubicación precisa
- `ACCESS_COARSE_LOCATION`: Ubicación aproximada
- `ACCESS_BACKGROUND_LOCATION`: Ubicación en segundo plano
- `FOREGROUND_SERVICE`: Servicios en primer plano
- `FOREGROUND_SERVICE_LOCATION`: Servicios de ubicación
- `POST_NOTIFICATIONS`: Notificaciones (Android 13+)
- `INTERNET`: Acceso a internet
- `WAKE_LOCK`: Mantener dispositivo activo

## 🚀 Funcionalidades

### **✅ Implementadas:**

1. **Vinculación con Token**
   - Pantalla de ingreso de token
   - Validación con servidor Flask
   - Almacenamiento seguro de credenciales

2. **Seguimiento de Ubicación**
   - Servicio en primer plano continuo
   - Envío automático cada 10 segundos
   - Optimización de batería
   - Detección de movimiento significativo

3. **Comunicación en Tiempo Real**
   - WebSocket para notificaciones inmediatas
   - Solicitudes de ubicación urgente
   - Reconexión automática

4. **Gestión de Permisos**
   - Solicitud progresiva de permisos
   - Manejo de permisos denegados
   - Optimización de batería

### **🔄 Flujo de la Aplicación:**

1. **Inicio**: Verificación de vinculación existente
2. **Vinculación**: Ingreso de token y validación
3. **Dashboard**: Pantalla principal con estado
4. **Servicio**: Inicio automático de seguimiento
5. **Comunicación**: WebSocket + API REST

## 🔗 Integración con Servidor

### **Endpoints Utilizados:**

- `POST /api/link_device`: Vinculación con token
- `POST /api/log_location`: Envío de ubicación
- `GET /api/request_immediate_location`: Verificar solicitudes
- `POST /api/register_connection`: Registrar conexión

### **WebSocket Events:**

- `child_register`: Registro del hijo
- `request_immediate_location`: Solicitud urgente
- `location_response`: Respuesta de ubicación

## 📱 Instalación y Uso

### **Requisitos:**

- Android 7.0 (API 24) o superior
- Servicios de Google Play
- Conexión a internet
- GPS habilitado

### **Pasos de Instalación:**

1. Instalar APK en dispositivo del hijo
2. Conceder permisos de ubicación
3. Ingresar token proporcionado por el padre
4. Confirmar vinculación exitosa
5. El seguimiento inicia automáticamente

## 🔒 Seguridad

- **Comunicación HTTPS**: Todas las comunicaciones encriptadas
- **Tokens únicos**: Cada vinculación usa token único
- **Almacenamiento seguro**: Credenciales en SharedPreferences
- **Validación servidor**: Verificación constante de autenticidad

## 🔋 Optimización

### **Batería:**

- Servicio en primer plano eficiente
- Detección de movimiento inteligente
- Intervalos adaptativos
- Solicitud de exención de optimización

### **Red:**

- Reconexión automática
- Fallback HTTP si WebSocket falla
- Compresión de datos
- Manejo de errores robusto

## 🐛 Debugging

### **Logs Importantes:**

- `MainActivity`: Proceso de vinculación
- `LocationService`: Estado del seguimiento
- `WebSocketManager`: Comunicación tiempo real
- `RetrofitClient`: Llamadas HTTP

### **Estados del Servicio:**

- `Inactivo`: Servicio detenido
- `Activando`: Iniciando seguimiento
- `Activo`: Enviando ubicaciones
- `Error`: Problema detectado

## 📊 Monitoreo

La aplicación proporciona feedback visual del estado:

- **Notificación persistente**: Estado del servicio
- **Dashboard**: Información de conexión
- **Logs detallados**: Para debugging

## 🔄 Actualizaciones

Para actualizar la aplicación:

1. Compilar nueva versión
2. Incrementar `versionCode` y `versionName`
3. Distribuir APK actualizado
4. La configuración se mantiene automáticamente

---

**Desarrollado para el Sistema Corredores Seguros**  
*Seguimiento familiar seguro y confiable* 🛡️
