<!-- app/templates/incidents/reportar_incidente.html -->
{% extends 'base.html' %}

{% block content %}
<h2>Reportar Incidencia</h2>

{# El formulario Flask-WTF genera automáticamente el token CSRF si está habilitado #}
<form method="post">
    {{ form.hidden_tag() }}
    <p>
        {{ form.incident_type.label }}<br>
        {# Añadido 'form-control' para consistencia, si usas Bootstrap #}
        {{ form.incident_type(class="form-control") }}
        {% for error in form.incident_type.errors %}
            {# Usar clase text-danger para errores si usas Bootstrap #}
            <span class="text-danger">[{{ error }}]</span>
        {% endfor %}
    </p>
    <p>
        {{ form.description.label }}<br>
        {{ form.description(rows=5, class="form-control") }}
        {% for error in form.description.errors %}
            <span class="text-danger">[{{ error }}]</span>
        {% endfor %}
    </p>

    <p>
        {{ form.child_id.label }}<br>
        {# Asegurarse que este select tenga id='child_id' para que el JS lo encuentre #}
        {# Añadir opción por defecto "-- Selecciona --" es buena práctica #}
        {{ form.child_id(id='child_id', class="form-control") }}
        {% for error in form.child_id.errors %}
            <span class="text-danger">[{{ error }}]</span>
        {% endfor %}
    </p>

    <p>
        {# --- MODIFICADO: Usar form.zone_ids --- #}
        {{ form.zone_ids.label }}<br>
        {# --- MODIFICADO: Actualizar id y name --- #}
        <select multiple id="zone_ids" name="zone_ids" required class="form-control" size="5"> {# Aumentado size para mejor UI #}
            {# Las opciones se llenarán dinámicamente aquí por JavaScript #}
            <option disabled>Selecciona un hijo primero</option> {# Placeholder inicial #}
        </select>
        {% for error in form.zone_ids.errors %}
            <span class="text-danger">[{{ error }}]</span>
        {% endfor %}
        {# ------------------------------------ #}
    </p>

    <p style="margin-top: 15px;">
        {# Usar clases btn btn-primary si usas Bootstrap #}
        {{ form.submit(class="btn btn-primary") }}
    </p>
</form>

{# Mostrar el token Y el incident ID si existen después de enviar #}
{% if token and incident_id %}
  {# Usar clases de alerta de Bootstrap si están disponibles #}
  <div class="alert alert-success mt-4"> {# mt-4 para margen superior #}
    <p>Incidente reportado exitosamente. El token de acceso para el operador es:</p>
    <p><strong>{{ token }}</strong></p>
    <p><strong>ID de Incidencia:</strong> {{ incident_id }}</p>
    <p class="mb-0"><em>Guarda esta información en un lugar seguro.</em></p> {# mb-0 para quitar margen inferior #}
  </div>
{% endif %}

{% endblock %}


{% block scripts %}
{# {{ super() }} #} {# Descomenta si base.html tiene un bloque scripts que quieras incluir #}
<script>
    // Esperar a que el DOM esté completamente cargado
    document.addEventListener('DOMContentLoaded', function() {
        const childSelect = document.getElementById('child_id');
        // --- MODIFICADO: Referencia al select de zonas ---
        const zoneSelect = document.getElementById('zone_ids');
        // ---------------------------------------------

        // Verificar si ambos elementos select existen para evitar errores
        if (childSelect && zoneSelect) {
            childSelect.addEventListener('change', function() {
                const childId = this.value; // Obtener ID del hijo seleccionado

                // Limpiar opciones existentes de zonas
                zoneSelect.innerHTML = '';

                // Añadir opción temporal de "Cargando..."
                const loadingOption = document.createElement('option');
                loadingOption.disabled = true;
                // --- MODIFICADO: Texto de carga ---
                loadingOption.text = 'Cargando zonas...';
                // ---------------------------------
                zoneSelect.appendChild(loadingOption);

                // Proceder solo si se seleccionó un hijo válido (no la opción por defecto vacía)
                if (childId && childId !== '') {
                     // --- MODIFICADO: URL y log ---
                    console.log(`Hijo seleccionado ID: ${childId}. Haciendo fetch a /incidents/get_child_zones/${childId}`);
                    fetch(`/incidents/get_child_zones/${childId}`) // Llamar a la nueva ruta
                    // -----------------------------
                        .then(response => {
                            console.log("Respuesta recibida del fetch:", response);
                            if (!response.ok) {
                                // Mejor manejo de errores HTTP
                                return response.text().then(text => {
                                     throw new Error(`Error ${response.status}: ${response.statusText}. Respuesta: ${text}`);
                                });
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log("Datos JSON recibidos:", data);
                            zoneSelect.innerHTML = ''; // Limpiar "Cargando..."

                            // --- MODIFICADO: Esperar data.zones ---
                            if (data.zones && Array.isArray(data.zones)) {
                                if (data.zones.length === 0) {
                                    const option = document.createElement('option');
                                    option.disabled = true;
                                    // --- MODIFICADO: Mensaje si no hay zonas ---
                                    option.text = 'No hay zonas seguras asignadas';
                                    // --------------------------------------
                                    zoneSelect.appendChild(option);
                                } else {
                                    // --- MODIFICADO: Iterar sobre zonas ---
                                    data.zones.forEach(zone => {
                                        const option = document.createElement('option');
                                        option.value = zone.zone_id; // Usar zone_id
                                        // Usar nombre de zona o ID si el nombre falta
                                        option.text = zone.zone_name || `Zona ${zone.zone_id}`; // Usar zone_name
                                        zoneSelect.appendChild(option);
                                    });
                                    // ------------------------------------
                                }
                            // -----------------------------------
                            } else if (data.error) {
                                console.error('Error del servidor:', data.error);
                                const option = document.createElement('option');
                                option.disabled = true;
                                option.text = 'Error: ' + data.error;
                                zoneSelect.appendChild(option);
                            } else {
                                console.error('Formato de respuesta inesperado:', data);
                                const option = document.createElement('option');
                                option.disabled = true;
                                option.text = 'Error: Respuesta inesperada';
                                zoneSelect.appendChild(option);
                            }
                        })
                        .catch(error => {
                             // --- MODIFICADO: Mensajes de error ---
                            console.error('Error durante el fetch de zonas:', error);
                            zoneSelect.innerHTML = '';
                            const option = document.createElement('option');
                            option.disabled = true;
                            option.text = 'Error al cargar zonas';
                            zoneSelect.appendChild(option);
                            // Considera mostrar un mensaje más amigable que alert()
                            flashMessage('Error de conexión al obtener las zonas. Inténtalo más tarde.', 'danger');
                             // -----------------------------------
                        });
                } else {
                    // No hay hijo seleccionado (o se seleccionó la opción vacía)
                    zoneSelect.innerHTML = '';
                    const option = document.createElement('option');
                    option.disabled = true;
                    // --- MODIFICADO: Placeholder ---
                    option.text = 'Selecciona un hijo primero';
                    // ----------------------------
                    zoneSelect.appendChild(option);
                    console.log("Ningún hijo seleccionado, selector de zonas limpiado.");
                }
            });

            // --- Disparar evento 'change' al cargar si hay hijo preseleccionado ---
            // (Misma lógica, solo los logs cambian un poco)
             if (childSelect.value && childSelect.value !== '') {
                 console.log("Disparando evento 'change' inicial para hijo preseleccionado:", childSelect.value);
                 childSelect.dispatchEvent(new Event('change'));
             } else {
                 // Asegurarse de que el placeholder inicial se muestre si no hay selección
                 zoneSelect.innerHTML = '';
                 const option = document.createElement('option');
                 option.disabled = true;
                 option.text = 'Selecciona un hijo primero';
                 zoneSelect.appendChild(option);
                 console.log("Carga inicial sin hijo seleccionado.");
             }

        } else {
            // Error si los elementos select no se encuentran en el DOM
             // --- MODIFICADO: Mensaje de error ---
            console.error("Error: No se encontraron los elementos select #child_id o #zone_ids en el DOM.");
             // -----------------------------------
        }

        // Función auxiliar para mostrar mensajes flash (si usas un sistema similar)
        function flashMessage(message, category = 'info') {
            // Implementa cómo mostrarías un mensaje flash desde JS
            // Podría ser creando un div, usando una librería, etc.
            console.warn(`FLASH (${category}): ${message}`); // Log como fallback
            // Ejemplo simple (requiere un div con id="flash-container" en base.html):
            // const flashContainer = document.getElementById('flash-container');
            // if (flashContainer) {
            //     const div = document.createElement('div');
            //     div.className = `alert alert-${category}`;
            //     div.textContent = message;
            //     flashContainer.appendChild(div);
            //     setTimeout(() => div.remove(), 5000); // Autocerrar después de 5s
            // }
        }

    }); // Fin de DOMContentLoaded
</script>
{% endblock %}