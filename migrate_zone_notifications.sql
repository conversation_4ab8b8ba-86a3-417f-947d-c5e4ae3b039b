-- migrate_zone_notifications.sql
-- Migración para agregar sistema de notificaciones de zonas

USE corredores_seguros;

-- 1. Agregar columnas a child_zone_assignments
ALTER TABLE child_zone_assignments 
ADD COLUMN early_warning_minutes INT DEFAULT 5 COMMENT 'Minutos antes para alerta temprana',
ADD COLUMN notifications_enabled BOOLEAN DEFAULT TRUE COMMENT 'Si las notificaciones están habilitadas';

-- 2. <PERSON><PERSON><PERSON> tabla zone_compliance_logs
CREATE TABLE zone_compliance_logs (
    log_id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    assignment_id BIGINT UNSIGNED NOT NULL,
    child_id BIGINT UNSIGNED NOT NULL,
    zone_id BIGINT UNSIGNED NOT NULL,
    
    -- Información del día y horario
    compliance_date DATE NOT NULL COMMENT 'Fecha del cumplimiento',
    expected_time TIME NOT NULL COMMENT 'Hora esperada de llegada',
    actual_arrival_time TIME NULL COMMENT 'Hora real de llegada (null si no llegó)',
    
    -- Estado del cumplimiento
    status VARCHAR(50) DEFAULT 'pending' COMMENT 'pending, arrived, late, missed',
    minutes_late INT DEFAULT 0 COMMENT 'Minutos de retraso',
    
    -- Notificaciones enviadas
    early_warning_sent BOOLEAN DEFAULT FALSE COMMENT 'Si se envió alerta temprana',
    late_notification_sent BOOLEAN DEFAULT FALSE COMMENT 'Si se envió notificación de llegada tardía',
    missed_notification_sent BOOLEAN DEFAULT FALSE COMMENT 'Si se envió notificación de zona perdida',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Claves foráneas
    FOREIGN KEY (assignment_id) REFERENCES child_zone_assignments(assignment_id) ON DELETE CASCADE,
    FOREIGN KEY (child_id) REFERENCES children(child_id) ON DELETE CASCADE,
    FOREIGN KEY (zone_id) REFERENCES safe_zones(zone_id) ON DELETE CASCADE,
    
    -- Índices para optimizar consultas
    INDEX idx_compliance_date (compliance_date),
    INDEX idx_child_zone_date (child_id, zone_id, compliance_date),
    INDEX idx_status (status),
    INDEX idx_assignment_date (assignment_id, compliance_date),
    
    -- Constraint único para evitar duplicados
    UNIQUE KEY unique_assignment_date (assignment_id, compliance_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Logs de cumplimiento de zonas por día';

-- 3. Verificar que las tablas se crearon correctamente
SHOW COLUMNS FROM child_zone_assignments;
SHOW COLUMNS FROM zone_compliance_logs;

-- 4. Mostrar mensaje de éxito
SELECT 'Migración completada exitosamente' AS status;
