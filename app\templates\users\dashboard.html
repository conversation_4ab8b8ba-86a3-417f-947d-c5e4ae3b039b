<!-- app/templates/users/dashboard.html -->
{% extends 'base.html' %}

{% block content %}
  {# Añadir un contenedor y centrar el contenido para mejor estética #}
  <div class="container text-center mt-4">
      <h1>Dashboard de Usuario</h1>

      {# Mensajes Flash (Buena práctica tenerlos cerca del top) #}
      {% with messages = get_flashed_messages(with_categories=true) %}
          {% if messages %}
              {% for category, message in messages %}
                  <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                      {{ message }}
                      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                  </div>
              {% endfor %}
          {% endif %}
      {% endwith %}


      {% if current_user.has_role('PADRE') %}
          {# --- Sección PADRE --- #}
          <p>Bienvenido, Padre <strong>{{ current_user.first_name }} {{ current_user.last_name }}</strong>!</p>

          {# Grupo de botones principales para el padre #}
          <div class="d-grid gap-2 d-md-flex justify-content-md-center mb-4">
              <a href="{{ url_for('users.agregar_hijo') }}" class="btn btn-primary">Agregar Hijo</a>
              {# --- MODIFICADO: Enlaces a Zonas --- #}
              <a href="{{ url_for('zones.ver_zonas') }}" class="btn btn-info">Ver Zonas Seguras</a>
              <a href="{{ url_for('zones.crear_zona') }}" class="btn btn-success">Crear Zona Segura</a>
              {# ---------------------------------- #}
              {# --- Enlaces a Rutas (temporalmente deshabilitados) --- #}
              {# <a href="{{ url_for('routes.ver_rutas') }}" class="btn btn-primary">Ver Rutas</a> #}
              {# <a href="{{ url_for('routes.crear_ruta') }}" class="btn btn-success">Crear Ruta</a> #}
              {# ---------------------------------- #}
              <a href="{{ url_for('incidents.reportar_incidente') }}" class="btn btn-warning">Reportar Incidente</a>
              <a href="{{ url_for('notifications.ver_notificaciones') }}" class="btn btn-secondary">Ver Notificaciones</a>
              <a href="{{ url_for('users.gestionar_hijos') }}" class="btn btn-outline-primary">Gestionar Hijos</a>
          </div>


          {# Tabla de administración de hijos #}
          <h3 class="mt-5">Administrar Mis Hijos</h3>
          {% if children %} {# Verificar si hay hijos antes de mostrar tabla #}
              <div class="table-responsive"> {# Hacer tabla responsive #}
                  <table class="table table-bordered table-hover align-middle"> {# Clases Bootstrap para estilo y alineación vertical #}
                      <thead class="table-light"> {# Fondo claro para cabecera #}
                          <tr>
                              <th>Nombre del Hijo</th>
                              <th>Ver Ubicación Actual</th>
                              <th>Token Vinculación</th>
                              <th>Ver Escritorio Hijo</th>
                          </tr>
                      </thead>
                      <tbody>
                          {% for child in children %}
                              <tr>
                                  <td>{{ child.first_name }} {{ child.last_name }}</td>
                                  <td>
                                      <button onclick="requestImmediateLocation({{ child.child_id }})" class="btn btn-primary btn-sm">
                                          📍 Ver Ubicación Actual
                                      </button>
                                  </td>
                                  <td>
                                      {# Formulario para generar token #}
                                      <form method="POST" action="{{ url_for('users.generar_token_vinculacion', child_id=child.child_id) }}" class="d-inline"> {# d-inline para que no ocupe toda la línea #}
                                          {# CSRF Token #}
                                          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                          <button type="submit" class="btn btn-warning btn-sm">Generar Nuevo</button>
                                      </form>
                                  </td>
                                  <td>
                                      <a href="{{ url_for('users.ver_dashboard_hijo_redirect', child_id=child.child_id) }}" class="btn btn-success btn-sm">Acceder</a>
                                  </td>
                              </tr>
                          {% endfor %}
                      </tbody>
                  </table>
              </div>
          {% else %}
              <p class="text-muted">Aún no has agregado ningún hijo.</p>
          {% endif %}

          {# Sección de notificaciones movida a la campanita en el header #}

          {# Display del token generado #}
          {% if generated_token and token_child %}
          <div class="alert alert-success border-0 shadow-sm mt-4" id="tokenAlert">
            <div class="d-flex align-items-center mb-3">
              <i class="fas fa-key text-success me-2" style="font-size: 1.5rem;"></i>
              <h5 class="mb-0 text-success">¡Token generado para {{ token_child.first_name }} {{ token_child.last_name }}!</h5>
              <button type="button" class="btn-close ms-auto" aria-label="Close" onclick="document.getElementById('tokenAlert').style.display='none'"></button>
            </div>

            <p class="mb-3"><strong>Nuevo token de vinculación:</strong></p>

            <!-- Token Display Card -->
            <div class="card bg-light border-success mb-3">
              <div class="card-body p-3">
                <div class="input-group">
                  <input type="text"
                         id="dashboardTokenDisplay"
                         value="{{ generated_token }}"
                         readonly
                         class="form-control form-control-lg text-center fw-bold"
                         style="font-family: 'Courier New', monospace; font-size: 1.2rem; letter-spacing: 2px; background-color: #f8f9fa; color: #198754;">
                  <button id="dashboardCopyButton"
                          class="btn btn-success btn-lg"
                          type="button"
                          style="min-width: 100px;">
                    <i class="fas fa-copy me-1"></i>Copiar
                  </button>
                </div>
              </div>
            </div>

            <!-- Instructions -->
            <div class="alert alert-info border-0 mb-0">
              <div class="d-flex align-items-start">
                <i class="fas fa-info-circle text-info me-2 mt-1"></i>
                <div>
                  <strong>Instrucciones:</strong>
                  <ul class="mb-0 mt-2">
                    <li>Comparte este token con <strong>{{ token_child.first_name }}</strong></li>
                    <li>El token expira en <strong>24 horas</strong></li>
                    <li>Debe ingresarlo en la aplicación móvil</li>
                    <li>Una vez usado, se invalidará automáticamente</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <script>
            document.getElementById('dashboardCopyButton').addEventListener('click', function() {
              var tokenInput = document.getElementById('dashboardTokenDisplay');
              var copyButton = document.getElementById('dashboardCopyButton');

              // Seleccionar y copiar el texto
              tokenInput.select();
              tokenInput.setSelectionRange(0, 99999); // Para móviles

              try {
                // Método moderno
                navigator.clipboard.writeText(tokenInput.value).then(function() {
                  // Feedback visual exitoso
                  copyButton.innerHTML = '<i class="fas fa-check me-1"></i>¡Copiado!';
                  copyButton.classList.remove('btn-success');
                  copyButton.classList.add('btn-primary');

                  // Restaurar después de 3 segundos
                  setTimeout(function() {
                    copyButton.innerHTML = '<i class="fas fa-copy me-1"></i>Copiar';
                    copyButton.classList.remove('btn-primary');
                    copyButton.classList.add('btn-success');
                  }, 3000);
                });
              } catch (err) {
                // Fallback para navegadores antiguos
                document.execCommand('copy');
                copyButton.innerHTML = '<i class="fas fa-check me-1"></i>¡Copiado!';
                copyButton.classList.remove('btn-success');
                copyButton.classList.add('btn-primary');

                setTimeout(function() {
                  copyButton.innerHTML = '<i class="fas fa-copy me-1"></i>Copiar';
                  copyButton.classList.remove('btn-primary');
                  copyButton.classList.add('btn-success');
                }, 3000);
              }
            });
          </script>
          {% endif %}

      {% elif current_user.has_role('OPERADOR') %}
          {# --- Sección OPERADOR --- #}
          <p>Bienvenido, Operador <strong>{{ current_user.first_name }} {{ current_user.last_name }}</strong>!</p>
          <a href="{{ url_for('incidents.gestionar_incidentes') }}" class="btn btn-primary">Gestionar Incidencias Abiertas</a>

      {% elif current_user.has_role('ADMIN') %}
          {# --- Sección ADMIN --- #}
          <p>Bienvenido, Administrador <strong>{{ current_user.first_name }} {{ current_user.last_name }}</strong>!</p>
           <div class="d-grid gap-2 d-md-flex justify-content-md-center mb-4">
               <a href="{{ url_for('users.crear_supervisor') }}" class="btn btn-primary">Crear Supervisor</a>
               <a href="{{ url_for('users.crear_operador') }}" class="btn btn-info">Crear Operador</a>
               <a href="{{ url_for('users.listar_usuarios') }}" class="btn btn-secondary">Listar/Editar Usuarios</a>
               {# Podrías añadir enlace a auditoría de incidencias si admin también audita #}
               {# <a href="{{ url_for('incidents.auditar_incidentes') }}" class="btn btn-warning">Auditar Incidencias</a> #}
           </div>

      {% elif current_user.has_role('SUPERVISOR') %}
          {# --- Sección SUPERVISOR --- #}
          <p>Bienvenido, Supervisor <strong>{{ current_user.first_name }} {{ current_user.last_name }}</strong>!</p>
           <div class="d-grid gap-2 d-md-flex justify-content-md-center mb-4">
               <a href="{{ url_for('users.crear_padre') }}" class="btn btn-primary">Crear Padre</a>
               <a href="{{ url_for('users.crear_operador') }}" class="btn btn-info">Crear Operador</a>
               <a href="{{ url_for('incidents.auditar_incidentes') }}" class="btn btn-warning">Auditar Incidencias</a>
               <a href="{{ url_for('users.listar_usuarios') }}" class="btn btn-secondary">Listar/Editar Usuarios</a>
           </div>

      {# --- NOTA SOBRE ROL 'HIJO' --- #}
      {# El rol 'HIJO' generalmente no debería tener acceso a este dashboard web. #}
      {# Su interfaz debería ser la app móvil o la vista 'dashboard_hijo.html' #}
      {# a la que se accede mediante cookie/token. Dejo el bloque por si tenías #}
      {# una razón específica, pero considera mover esta lógica. #}
      {% elif current_user.has_role('HIJO') %}
          {# --- Sección HIJO (Considerar mover a dashboard_hijo.html) --- #}
          <p>Bienvenido, <strong>{{ current_user.first_name }} {{ current_user.last_name }}</strong>!</p>
          {# Este dashboard es para el padre/admin/etc. El hijo usa dashboard_hijo.html #}
          {# La lógica JS de GPS y Notificaciones debe ir en dashboard_hijo.html #}
          <p class="text-muted">Accede a tu escritorio desde el enlace proporcionado por tu padre/tutor.</p>
          {#<div id="gps-status">GPS Logging: Inactive</div>
          <div class="hijo-notifications">
              <h3>Enviar Notificación a Padre</h3>
              <button onclick="sendNotification('estoy_bien')" class="btn btn-success btn-sm">Estoy Bien</button>
              <button onclick="sendNotification('ayuda')" class="btn btn-warning btn-sm">Ayuda</button>
              <button onclick="sendNotification('demorado')" class="btn btn-info btn-sm">Demorado</button>
              <button onclick="sendNotification('otro')" class="btn btn-secondary btn-sm">Otro</button>
              <div id="notification-status"></div>
          </div>#}

      {% else %}
          {# --- Usuario sin rol específico asignado (si es posible) --- #}
          <p>Bienvenido, <strong>{{ current_user.first_name }} {{ current_user.last_name }}</strong>!</p>
          <p>No tienes acciones específicas asignadas.</p>
      {% endif %}
  </div> {# Fin del contenedor #}
{% endblock %}


{# --- Bloque de Scripts --- #}
{# El script para el rol HIJO se ha comentado aquí porque pertenece #}
{# idealmente a dashboard_hijo.html para evitar cargar JS innecesario #}
{# en los dashboards de otros roles. #}
{% block scripts %}
  {# {{ super() }} #} {# Si tienes scripts en base.html #}

  {# {% if current_user.has_role('HIJO') %}
      <script>
          // PEGAR AQUÍ el script de geolocalización y notificación que estaba antes
          // Pero idealmente, este script debería estar SOLO en dashboard_hijo.html
      </script>
  {% endif %} #}

  {# Añadir script para cerrar alertas de Bootstrap si se usan #}
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script> {# Ejemplo con Bootstrap 5 #}

  {# JavaScript simplificado para evitar errores #}
  <script>
  function requestImmediateLocation(childId) {
      console.log('🎯 Solicitando ubicación inmediata para hijo:', childId);

      // Ir directamente al endpoint simple
      window.location.href = '/users/ver_ubicacion_hijo/' + childId;
  }

  // --- Funciones para Notificaciones de Zonas ---

  function loadZoneNotifications() {
      fetch('/zone_monitoring/recent_notifications')
          .then(response => response.json())
          .then(data => {
              if (data.success) {
                  // Verificar nuevas notificaciones antes de mostrar
                  checkForNewNotifications(data.notifications);

                  displayNotificationsInDropdown(data.notifications);
                  updateNotificationBadge(data.notifications);
              } else {
                  showNotificationsError('Error cargando notificaciones');
              }
          })
          .catch(error => {
              console.error('Error:', error);
              showNotificationsError('Error de conexión');
          });
  }

  function displayNotificationsInDropdown(notifications) {
      const container = document.getElementById('notifications-dropdown-content');

      if (notifications.length === 0) {
          container.innerHTML = `
              <div style="text-align: center; color: #666; padding: 20px;">
                  <i class="fas fa-check-circle" style="font-size: 2em; margin-bottom: 8px;"></i>
                  <p style="margin: 0;">No hay notificaciones recientes</p>
              </div>
          `;
          return;
      }

      let html = '';

      // Mostrar máximo 5 notificaciones en el dropdown
      const displayNotifications = notifications.slice(0, 5);

      displayNotifications.forEach((notification, index) => {
          const isUnread = !notification.viewed;
          const typeIcon = getNotificationIcon(notification.type);
          const typeColor = getNotificationColor(notification.type);
          const timeAgo = getTimeAgo(notification.created_at);

          html += `
              <div class="notification-item ${isUnread ? 'unread' : ''}" data-notification-id="${notification.id}">
                  <div style="display: flex; align-items: flex-start;">
                      <div style="margin-right: 8px; margin-top: 2px;">
                          <i class="fas ${typeIcon}" style="color: ${getTypeColorHex(typeColor)};"></i>
                      </div>
                      <div style="flex: 1; min-width: 0;">
                          <div style="display: flex; align-items: center; margin-bottom: 4px;">
                              <strong style="color: ${getTypeColorHex(typeColor)}; font-size: 13px;">${notification.child_name}</strong>
                              ${isUnread ? '<span style="background: #ffc107; color: #000; padding: 1px 4px; border-radius: 3px; font-size: 9px; margin-left: 4px;">NUEVO</span>' : ''}
                          </div>
                          <p style="margin: 0 0 4px 0; font-size: 12px; line-height: 1.3; word-wrap: break-word;">${notification.message}</p>
                          <small style="color: #666; font-size: 10px;">${timeAgo}</small>
                      </div>
                      ${isUnread ? `
                          <div style="margin-left: 4px;">
                              <button class="btn-small btn-primary-small mark-read-btn" data-notification-id="${notification.id}">
                                  <i class="fas fa-check"></i>
                              </button>
                          </div>
                      ` : ''}
                  </div>
              </div>
          `;
      });

      if (notifications.length > 5) {
          html += `
              <div style="text-align: center; padding: 8px;">
                  <a href="/zone_monitoring/notifications" style="color: #007bff; font-size: 11px; text-decoration: none;">
                      <i class="fas fa-list" style="margin-right: 4px;"></i>
                      Ver todas las ${notifications.length} notificaciones
                  </a>
              </div>
          `;
      }

      container.innerHTML = html;

      // Agregar event listeners a los botones de marcar como leído
      console.log('🔗 Agregando event listeners a botones del dropdown...');
      const markReadButtons = container.querySelectorAll('.mark-read-btn');
      console.log(`📋 Encontrados ${markReadButtons.length} botones de marcar como leído`);

      markReadButtons.forEach(button => {
          const notificationId = button.getAttribute('data-notification-id');
          console.log(`🔗 Agregando listener para notificación ${notificationId}`);

          button.addEventListener('click', function(e) {
              e.preventDefault();
              e.stopPropagation();
              console.log(`🖱️ Click detectado en botón para notificación ${notificationId}`);
              markNotificationRead(parseInt(notificationId));
          });
      });
  }

  function getNotificationIcon(type) {
      switch(type) {
          case 'zone_early_warning': return 'fa-clock';
          case 'zone_late_arrival': return 'fa-exclamation-triangle';
          case 'zone_missed': return 'fa-times-circle';
          default: return 'fa-bell';
      }
  }

  function getNotificationColor(type) {
      switch(type) {
          case 'zone_early_warning': return 'warning';
          case 'zone_late_arrival': return 'danger';
          case 'zone_missed': return 'danger';
          default: return 'info';
      }
  }

  function getTypeColorHex(colorName) {
      switch(colorName) {
          case 'warning': return '#ffc107';
          case 'danger': return '#dc3545';
          case 'dark': return '#343a40';
          case 'info': return '#17a2b8';
          default: return '#6c757d';
      }
  }

  function getTimeAgo(dateString) {
      const date = new Date(dateString);
      const now = new Date();
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMins / 60);
      const diffDays = Math.floor(diffHours / 24);

      if (diffMins < 1) return 'Ahora mismo';
      if (diffMins < 60) return `Hace ${diffMins} minuto${diffMins > 1 ? 's' : ''}`;
      if (diffHours < 24) return `Hace ${diffHours} hora${diffHours > 1 ? 's' : ''}`;
      return `Hace ${diffDays} día${diffDays > 1 ? 's' : ''}`;
  }

  function updateNotificationBadge(notifications) {
      const unreadCount = notifications.filter(n => !n.viewed).length;
      const badge = document.getElementById('notification-badge');

      if (unreadCount > 0) {
          badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
          badge.style.display = 'inline';
      } else {
          badge.style.display = 'none';
      }

      // Actualizar estado del botón de activar campanita
      updateBellButtonStatus();
  }

  function showNotificationsError(message) {
      const container = document.getElementById('notifications-dropdown-content');
      container.innerHTML = `
          <div style="text-align: center; color: #dc3545; padding: 20px;">
              <i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i>
              <p style="margin: 0; font-size: 12px;">${message}</p>
          </div>
      `;
  }

  function refreshNotifications() {
      const container = document.getElementById('notifications-dropdown-content');
      container.innerHTML = `
          <div style="text-align: center; padding: 20px;">
              <i class="fas fa-spinner fa-spin" style="color: #007bff; margin-bottom: 8px;"></i>
              <p style="margin: 0; font-size: 12px; color: #666;">Actualizando...</p>
          </div>
      `;
      loadZoneNotifications();
  }

  function markAllNotificationsRead() {
      // Obtener todas las notificaciones no leídas del dropdown
      const unreadButtons = document.querySelectorAll('#notifications-dropdown-content .mark-read-btn');

      unreadButtons.forEach(button => {
          const notificationId = button.getAttribute('data-notification-id');
          markNotificationRead(parseInt(notificationId));
      });
  }

  function updateBellButtonStatus() {
      const button = document.getElementById('bell-enable-btn');
      if (!button) return;

      switch(Notification.permission) {
          case 'granted':
              button.innerHTML = '<i class="fas fa-bell" style="margin-right: 4px;"></i>Probar';
              button.className = 'btn-small btn-success-small';
              button.disabled = false;
              break;
          case 'denied':
              button.innerHTML = '<i class="fas fa-bell-slash" style="margin-right: 4px;"></i>Bloqueadas';
              button.className = 'btn-small btn-danger-small';
              button.disabled = true;
              break;
          default:
              button.innerHTML = '<i class="fas fa-bell" style="margin-right: 4px;"></i>Activar';
              button.className = 'btn-small btn-primary-small';
              button.disabled = false;
              break;
      }
  }

  function markNotificationRead(notificationId) {
      console.log('🔔 FUNCIÓN markNotificationRead EJECUTADA');
      console.log('🆔 Notification ID recibido:', notificationId);
      console.log('🔧 Tipo de notificationId:', typeof notificationId);

      // Verificar que el ID es válido
      if (!notificationId || isNaN(notificationId)) {
          console.error('❌ ID de notificación inválido:', notificationId);
          alert('Error: ID de notificación inválido');
          return;
      }

      try {
          // Obtener CSRF token
          const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content') ||
                           document.querySelector('input[name=csrf_token]')?.value;

          console.log('🔑 CSRF Token encontrado:', csrfToken ? 'Sí' : 'No');
          console.log('📋 Iniciando petición para marcar notificación como leída...');

      const headers = {
          'Content-Type': 'application/json',
      };

      // Agregar CSRF token si está disponible
      if (csrfToken) {
          headers['X-CSRFToken'] = csrfToken;
      }

      fetch(`/zone_monitoring/mark_notification_viewed/${notificationId}`, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify({})
      })
      .then(response => {
          console.log('Response status:', response.status);
          console.log('Response headers:', response.headers);
          return response.json();
      })
      .then(data => {
          console.log('Response data:', data);
          if (data.success) {
              console.log('Notificación marcada exitosamente, actualizando UI...');

              // Actualizar la UI inmediatamente
              const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
              if (notificationElement) {
                  console.log('Elemento encontrado, removiendo clases y botones...');

                  // Remover clases de no leído
                  notificationElement.classList.remove('bg-light', 'border-start', 'border-warning', 'border-3');

                  // Remover badge "NUEVO"
                  const newBadge = notificationElement.querySelector('.badge.bg-warning');
                  if (newBadge) {
                      console.log('Removiendo badge NUEVO');
                      newBadge.remove();
                  }

                  // Remover botón de marcar como leído
                  const readButton = notificationElement.querySelector('button');
                  if (readButton) {
                      console.log('Removiendo botón de marcar como leído');
                      readButton.remove();
                  }

                  console.log('UI actualizada para notificación', notificationId);
              } else {
                  console.log('No se encontró el elemento de notificación');
              }

              // Recargar todas las notificaciones después de 1 segundo para asegurar sincronización
              setTimeout(() => {
                  console.log('Recargando notificaciones para sincronizar...');
                  refreshNotifications();
              }, 1000);

          } else {
              console.error('Error del servidor:', data.error);
          }
      })
      .catch(error => {
          console.error('💥 Error en fetch:', error);
          alert('Error de conexión: ' + error.message);
      });

      } catch (error) {
          console.error('💥 Error general en markNotificationRead:', error);
          alert('Error inesperado: ' + error.message);
      }
  }



  // Sistema de notificaciones del navegador
  let lastNotificationCount = 0;
  let notificationSound = null;

  function initBrowserNotifications() {
      console.log('🔔 Inicializando notificaciones del navegador...');

      // Verificar soporte para notificaciones
      if (!("Notification" in window)) {
          console.log('❌ Este navegador no soporta notificaciones');
          updateNotificationStatus('unsupported');
          return false;
      }

      // Actualizar estado inicial
      updateNotificationStatus(Notification.permission);

      // No solicitar permisos automáticamente, esperar a que el usuario haga clic
      if (Notification.permission === "granted") {
          console.log('✅ Permisos de notificación ya concedidos');
          showWelcomeNotification();
      }

      // Crear sonido de notificación
      try {
          // Sonido más simple y compatible
          notificationSound = new Audio();
          notificationSound.src = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
          notificationSound.volume = 0.5;
          console.log('🔊 Sonido de notificación cargado');
      } catch (e) {
          console.log('⚠️ No se pudo cargar el sonido de notificación:', e);
      }

      return Notification.permission === "granted";
  }

  function requestNotificationPermission() {
      console.log('🔔 Usuario solicitó permisos de notificación...');

      if (!("Notification" in window)) {
          alert('Tu navegador no soporta notificaciones push');
          return;
      }

      if (Notification.permission === "granted") {
          console.log('✅ Permisos ya concedidos, mostrando notificación de prueba...');
          showTestNotification();
          return;
      }

      Notification.requestPermission().then(function(permission) {
          console.log('🔔 Respuesta del usuario:', permission);
          updateNotificationStatus(permission);

          if (permission === "granted") {
              showWelcomeNotification();
              showTestNotification();
          } else {
              alert('Para recibir notificaciones de campanita, debes permitir las notificaciones en tu navegador.');
          }
      });
  }

  function updateNotificationStatus(permission) {
      // Esta función ya no es necesaria porque el botón está en el dropdown
      // Actualizar el botón del dropdown
      updateBellButtonStatus();
  }

  function showTestNotification() {
      if (Notification.permission === "granted") {
          const testNotification = new Notification("🧪 Notificación de Prueba", {
              body: "¡Las notificaciones están funcionando correctamente!",
              icon: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzIiIGZpbGw9IiNGRkM5MDAiLz4KPHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxNiIgeT0iMTYiPgo8cGF0aCBkPSJNMTggOEMxOCA2LjQwODcgMTcuMzY4NCA0Ljg4MjU4IDE2LjI0MjYgMy43NTczNkMxNS4xMTc0IDIuNjMyMTQgMTMuNTkxMyAyIDEyIDJDMTAuNDA4NyAyIDguODgyNTggMi42MzIxNCA3Ljc1NzM2IDMuNzU3MzZDNi42MzIxNCA0Ljg4MjU4IDYgNi40MDg3IDYgOEM2IDEwLjEyMTcgNS4xNTY5NiAxMi4xNTY2IDMuNzU3MzYgMTMuNTU1NkMyLjM1Nzc2IDE0Ljk1NSAyIDE2Ljk3ODMgMiAxOUgyMkMyMiAxNi45NzgzIDIxLjY0MjIgMTQuOTU1IDIwLjI0MjYgMTMuNTU1NkMxOC44NDMgMTIuMTU2NiAxOCAxMC4xMjE3IDE4IDhaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTMuNzMgMjFDMTMuNTU0MyAyMS4zMDMxIDEzLjMwMzEgMjEuNTU0MyAxMyAyMS43M0MxMi42OTY5IDIxLjkwNTcgMTIuMzUzNCAyMiAxMiAyMkMxMS42NDY2IDIyIDExLjMwMzEgMjEuOTA1NyAxMSAyMS43M0MxMC42OTY5IDIxLjU1NDMgMTAuNDQ1NyAyMS4zMDMxIDEwLjI3IDIxSDE0LjczSDEzLjczWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cg==",
              tag: "test",
              requireInteraction: false
          });

          // Reproducir sonido
          if (notificationSound) {
              notificationSound.play().catch(e => console.log('No se pudo reproducir sonido de prueba'));
          }

          // Auto-cerrar después de 3 segundos
          setTimeout(() => {
              testNotification.close();
          }, 3000);
      }
  }

  function showWelcomeNotification() {
      if (Notification.permission === "granted") {
          new Notification("🔔 Notificaciones Activadas", {
              body: "Recibirás alertas cuando tus hijos no lleguen a las zonas asignadas",
              icon: "/static/img/notification-icon.png",
              tag: "welcome"
          });
      }
  }

  function showZoneNotification(notification) {
      if (Notification.permission !== "granted") return;

      let title = "🚨 Alerta de Zona";
      let icon = "/static/img/zone-alert.png";

      // Personalizar según el tipo
      switch(notification.type) {
          case 'zone_early_warning':
              title = "⚠️ Alerta Temprana";
              icon = "/static/img/warning-icon.png";
              break;
          case 'zone_late_arrival':
              title = "🚨 Llegada Tardía";
              icon = "/static/img/late-icon.png";
              break;
          case 'zone_missed':
              title = "❌ Zona Perdida";
              icon = "/static/img/missed-icon.png";
              break;
      }

      const browserNotification = new Notification(title, {
          body: notification.message,
          icon: icon,
          tag: `zone-${notification.id}`,
          requireInteraction: true, // Mantener hasta que el usuario interactúe
          actions: [
              {
                  action: 'view',
                  title: 'Ver Detalles'
              },
              {
                  action: 'dismiss',
                  title: 'Descartar'
              }
          ]
      });

      // Reproducir sonido
      if (notificationSound) {
          notificationSound.play().catch(e => console.log('No se pudo reproducir sonido'));
      }

      // Manejar clics en la notificación
      browserNotification.onclick = function() {
          window.focus();
          // Scroll hacia las notificaciones
          document.getElementById('zone-notifications-section')?.scrollIntoView({behavior: 'smooth'});
          browserNotification.close();
      };

      // Auto-cerrar después de 10 segundos si no es crítica
      if (notification.type === 'zone_early_warning') {
          setTimeout(() => {
              browserNotification.close();
          }, 10000);
      }
  }

  function checkForNewNotifications(notifications) {
      const unreadNotifications = notifications.filter(n => !n.viewed);
      const currentCount = unreadNotifications.length;

      console.log(`📊 Verificando notificaciones: ${currentCount} no leídas, antes había ${lastNotificationCount}`);

      if (currentCount > lastNotificationCount) {
          // Hay nuevas notificaciones
          const newCount = currentCount - lastNotificationCount;
          console.log(`🆕 Detectadas ${newCount} nuevas notificaciones`);

          // Tomar las más recientes (las primeras en la lista)
          const newNotifications = unreadNotifications.slice(0, newCount);

          newNotifications.forEach(notification => {
              console.log('🔔 Mostrando nueva notificación:', notification.message.substring(0, 50) + '...');
              showZoneNotification(notification);
          });
      }

      lastNotificationCount = currentCount;
  }

  // Función para simular una nueva notificación (para testing)
  function simulateNewNotification() {
      console.log('🧪 Simulando nueva notificación...');
      const testNotification = {
          id: 999,
          type: 'zone_late_arrival',
          message: '🚨 SIMULACIÓN: Tu hijo no ha llegado a la zona asignada',
          child_name: 'Hijo de Prueba'
      };
      showZoneNotification(testNotification);
  }

  // Cargar notificaciones al cargar la página
  document.addEventListener('DOMContentLoaded', function() {
      console.log('🚀 DOM cargado, iniciando sistema de notificaciones...');

      // Inicializar notificaciones del navegador
      initBrowserNotifications();

      // Verificar que las funciones están disponibles
      console.log('✅ markNotificationRead disponible:', typeof markNotificationRead === 'function');
      console.log('✅ loadZoneNotifications disponible:', typeof loadZoneNotifications === 'function');

      // Hacer las funciones disponibles globalmente para debugging
      window.markNotificationRead = markNotificationRead;
      window.testMarkNotification = function(id) {
          console.log('🧪 Test function called with ID:', id);
          markNotificationRead(id);
      };
      window.simulateNewNotification = simulateNewNotification;
      window.requestNotificationPermission = requestNotificationPermission;
      window.showTestNotification = showTestNotification;

      // Función para simular click en botón
      window.simulateButtonClick = function() {
          const firstButton = document.querySelector('.mark-read-btn');
          if (firstButton) {
              const id = firstButton.getAttribute('data-notification-id');
              console.log('🧪 Simulando click en botón con ID:', id);
              firstButton.click();
          } else {
              console.log('❌ No se encontraron botones de marcar como leído');
          }
      };

      loadZoneNotifications();

      // Actualizar cada 30 segundos
      setInterval(loadZoneNotifications, 30000);

      console.log('✅ Sistema de notificaciones inicializado');
  });
  </script>

{% endblock %}