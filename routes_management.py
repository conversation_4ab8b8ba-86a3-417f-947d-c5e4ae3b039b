# app/routes/routes_management.py

import json
from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from app import db
from app.models import Route, ChildRouteAssignment, User, Child
from app.forms.route_forms import C<PERSON>RouteForm, EditRouteForm, AsignarRutaForm
from app.utils.encryption import Encryption

routes_bp = Blueprint('routes', __name__, template_folder='../templates/routes')

@routes_bp.route('/crear_ruta', methods=['GET', 'POST'])
@login_required
def crear_ruta():
    form = CreateRouteForm()
    
    hijos = current_user.children
    if not hijos:
        flash('No tienes hijos registrados para asignar la ruta.', 'warning')
        return redirect(url_for('users.dashboard'))
    
    form.child_id.choices = [(hijo.child_id, f"{hijo.first_name} {hijo.last_name}") for hijo in hijos]

    if form.validate_on_submit():
        route_name = form.route_name.data
        schedule_start = form.schedule_start.data
        schedule_end = form.schedule_end.data
        route_data_raw = request.form.get('route_data', '')

        try:
            geojson = json.loads(route_data_raw)
        except json.JSONDecodeError:
            flash('Los datos de la ruta no son un GeoJSON válido.', 'danger')
            return render_template('routes/crear_ruta.html', form=form)

        fernet_key = current_app.config['FERNET_KEY']
        encryption = Encryption(fernet_key)
        encrypted_route_data = encryption.encrypt(route_data_raw)

        new_route = Route(
            father_id=current_user.user_id,
            route_name=route_name,
            schedule_start=schedule_start,
            schedule_end=schedule_end,
            encrypted_route_data=encrypted_route_data
        )
        db.session.add(new_route)
        db.session.commit()

        assignment = ChildRouteAssignment(
            child_id=form.child_id.data,
            route_id=new_route.route_id,
            status='assigned'
        )
        db.session.add(assignment)
        db.session.commit()

        flash('Ruta creada y asignada exitosamente.', 'success')
        return redirect(url_for('routes.ver_rutas'))

    return render_template('routes/crear_ruta.html', form=form)
    
@routes_bp.route('/ver_rutas')
@login_required
def ver_rutas():
    routes = Route.query.filter_by(father_id=current_user.user_id).all()
    form_asignar = AsignarRutaForm()
    return render_template('routes/ver_rutas.html', routes=routes, form_asignar=form_asignar)

@routes_bp.route('/ruta/<int:route_id>/asignar', methods=['POST'])
@login_required
def asignar_ruta(route_id):
    route = Route.query.get_or_404(route_id)
    if route.father_id != current_user.user_id:
        flash('No tienes permiso para asignar esta ruta.', 'danger')
        return redirect(url_for('routes.ver_rutas'))
    
    child_id_str = request.form.get('child_id')
    if not child_id_str:
        flash('No se seleccionó ningún hijo.', 'danger')
        return redirect(url_for('routes.ver_rutas'))
    
    try:
        child_id = int(child_id_str)
    except ValueError:
        flash('ID de hijo inválido.', 'danger')
        return redirect(url_for('routes.ver_rutas'))

    child = Child.query.get(child_id)
    if not child or child.parent_id != current_user.user_id:
        flash('El hijo seleccionado no pertenece a tu cuenta.', 'danger')
        return redirect(url_for('routes.ver_rutas'))
    
    existing_assignment = ChildRouteAssignment.query.filter_by(child_id=child_id, route_id=route_id).first()
    if existing_assignment:
        flash('Esta ruta ya está asignada a ese hijo.', 'warning')
        return redirect(url_for('routes.ver_rutas'))
    
    assignment = ChildRouteAssignment(
        child_id=child_id,
        route_id=route_id,
        status='assigned'
    )
    db.session.add(assignment)
    db.session.commit()
    
    flash('Ruta asignada exitosamente.', 'success')
    return redirect(url_for('routes.ver_rutas'))

@routes_bp.route('/ruta/<int:route_id>/editar', methods=['GET', 'POST'])
@login_required
def editar_ruta(route_id):
    route = Route.query.get_or_404(route_id)
    if route.father_id != current_user.user_id:
        flash('No tienes permiso para editar esta ruta.', 'danger')
        return redirect(url_for('routes.ver_rutas'))

    hijos = current_user.children
    if not hijos:
        flash('No tienes hijos registrados para asignar la ruta.', 'warning')
        return redirect(url_for('users.dashboard'))

    assignment = ChildRouteAssignment.query.filter_by(route_id=route_id).first()
    form = EditRouteForm()
    form.child_id.choices = [(hijo.child_id, f"{hijo.first_name} {hijo.last_name}") for hijo in hijos]

    if request.method == 'GET':
        form.route_name.data = route.route_name
        form.schedule_start.data = route.schedule_start
        form.schedule_end.data = route.schedule_end
        fernet_key = current_app.config['FERNET_KEY']
        encryption = Encryption(fernet_key)
        decrypted_route_data = encryption.decrypt(route.encrypted_route_data)
        form.route_data.data = decrypted_route_data
        if assignment:
            form.child_id.data = assignment.child_id
        elif hijos:
            form.child_id.data = hijos[0].child_id

    if form.validate_on_submit():
        route.route_name = form.route_name.data
        route.schedule_start = form.schedule_start.data
        route.schedule_end = form.schedule_end.data
        route_data_raw = request.form.get('route_data', '')
        
        fernet_key = current_app.config['FERNET_KEY']
        encryption = Encryption(fernet_key)
        encrypted_route_data = encryption.encrypt(route_data_raw)
        route.encrypted_route_data = encrypted_route_data

        nuevo_child_id = form.child_id.data
        if not any(c.child_id == nuevo_child_id for c in current_user.children):
            flash('El hijo seleccionado no pertenece a tu cuenta.', 'danger')
            return render_template('routes/editar_ruta.html', form=form, route_id=route_id)

        if assignment:
            assignment.child_id = nuevo_child_id
        else:
            assignment = ChildRouteAssignment(
                child_id=nuevo_child_id,
                route_id=route_id,
                status='assigned'
            )
            db.session.add(assignment)

        db.session.commit()
        flash('Ruta actualizada exitosamente.', 'success')
        return redirect(url_for('routes.ver_rutas'))

    return render_template('routes/editar_ruta.html', form=form, route_id=route_id)

@routes_bp.route('/ruta/<int:route_id>/eliminar', methods=['POST'])
@login_required
def eliminar_ruta(route_id):
    route = Route.query.get_or_404(route_id)
    if route.father_id != current_user.user_id:
        flash('No tienes permiso para eliminar esta ruta.', 'danger')
        return redirect(url_for('routes.ver_rutas'))
    
    ChildRouteAssignment.query.filter_by(route_id=route_id).delete()
    db.session.delete(route)
    db.session.commit()
    
    flash('Ruta eliminada exitosamente.', 'success')
    return redirect(url_for('routes.ver_rutas'))
