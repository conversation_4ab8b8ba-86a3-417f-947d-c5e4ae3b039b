  Manifest android  ACCESS_BACKGROUND_LOCATION android.Manifest.permission  ACCESS_COARSE_LOCATION android.Manifest.permission  ACCESS_FINE_LOCATION android.Manifest.permission  ic_dialog_alert android.R.drawable  ic_dialog_info android.R.drawable  Activity android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  Service android.app  AlertDialog android.app.Activity  BatteryOptimizationHelper android.app.Activity  Button android.app.Activity  
CookieManager android.app.Activity  DashboardHijoActivity android.app.Activity  	Exception android.app.Activity  
FCMManager android.app.Activity  Handler android.app.Activity  Intent android.app.Activity  LocationService android.app.Activity  Log android.app.Activity  LoginRequest android.app.Activity  Looper android.app.Activity  MainActivity android.app.Activity  
NetworkClient android.app.Activity  PackageManager android.app.Activity  PermissionHelper android.app.Activity  PreferenceManager android.app.Activity  ProfileSelectionActivity android.app.Activity  R android.app.Activity  String android.app.Activity  System android.app.Activity  Toast android.app.Activity  
TokenActivity android.app.Activity  TokenRequest android.app.Activity  View android.app.Activity  WebAppInterface android.app.Activity  WebSessionRequest android.app.Activity  WebSettings android.app.Activity  WebView android.app.Activity  
WebViewClient android.app.Activity  all android.app.Activity  android android.app.Activity  androidx android.app.Activity  apply android.app.Activity  com android.app.Activity  contains android.app.Activity   createWebSessionAndLoadDashboard android.app.Activity  fillTokenFormAutomatically android.app.Activity  finish android.app.Activity  hasBackgroundLocationPermission android.app.Activity  hasLocationPermissions android.app.Activity  injectDashboardJavaScript android.app.Activity  isEmpty android.app.Activity  isGpsEnabled android.app.Activity  
isNotEmpty android.app.Activity  java android.app.Activity  kotlinx android.app.Activity  launch android.app.Activity  let android.app.Activity  lifecycleScope android.app.Activity  onCreate android.app.Activity  onRequestPermissionsResult android.app.Activity  openAppSettings android.app.Activity  openLocationSettings android.app.Activity  preferenceManager android.app.Activity  #requestBackgroundLocationPermission android.app.Activity  !requestIgnoreBatteryOptimizations android.app.Activity  requestLocationPermissions android.app.Activity  run android.app.Activity  
runOnUiThread android.app.Activity  sendImmediateLocationToServer android.app.Activity  	showError android.app.Activity  showLoading android.app.Activity  
startActivity android.app.Activity  startService android.app.Activity  trim android.app.Activity  
trimIndent android.app.Activity  graphics android.app.Activity.android  webkit android.app.Activity.android  Bitmap %android.app.Activity.android.graphics  GeolocationPermissions #android.app.Activity.android.webkit  WebChromeClient #android.app.Activity.android.webkit  WebResourceError #android.app.Activity.android.webkit  WebResourceRequest #android.app.Activity.android.webkit  Callback :android.app.Activity.android.webkit.GeolocationPermissions  google android.app.Activity.com  android android.app.Activity.com.google  gms 'android.app.Activity.com.google.android  location +android.app.Activity.com.google.android.gms  LocationCallback 4android.app.Activity.com.google.android.gms.location  LocationResult 4android.app.Activity.com.google.android.gms.location  RingtoneManager android.app.NotificationChannel  apply android.app.NotificationChannel  description android.app.NotificationChannel  enableVibration android.app.NotificationChannel  longArrayOf android.app.NotificationChannel  setShowBadge android.app.NotificationChannel  setSound android.app.NotificationChannel  vibrationPattern android.app.NotificationChannel  IMPORTANCE_HIGH android.app.NotificationManager  IMPORTANCE_LOW android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  notify android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  
FLAG_ONE_SHOT android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getActivity android.app.PendingIntent  ActivityCompat android.app.Service  BatteryManager android.app.Service  Build android.app.Service  
CHANNEL_ID android.app.Service  Context android.app.Service  CoroutineScope android.app.Service  Dispatchers android.app.Service  	Exception android.app.Service  FASTEST_UPDATE_INTERVAL android.app.Service  Intent android.app.Service  IntentFilter android.app.Service  LOCATION_UPDATE_INTERVAL android.app.Service  LocationAvailability android.app.Service  LocationCallback android.app.Service  LocationRequest android.app.Service  LocationResult android.app.Service  LocationService android.app.Service  LocationServices android.app.Service  LocationUpdateRequest android.app.Service  Log android.app.Service  Looper android.app.Service  MainActivity android.app.Service  Manifest android.app.Service  NOTIFICATION_ID android.app.Service  
NetworkClient android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationManager android.app.Service  PackageManager android.app.Service  
PendingIntent android.app.Service  PreferenceManager android.app.Service  Priority android.app.Service  R android.app.Service  RingtoneManager android.app.Service  START_NOT_STICKY android.app.Service  START_STICKY android.app.Service  
SupervisorJob android.app.Service  System android.app.Service  TAG android.app.Service  android android.app.Service  apply android.app.Service  cancel android.app.Service  currentPollingInterval android.app.Service  fusedLocationClient android.app.Service  getLastKnownLocationImmediate android.app.Service  
isNotEmpty android.app.Service  
isNullOrEmpty android.app.Service  java android.app.Service  kotlinx android.app.Service  lastUrgentRequest android.app.Service  launch android.app.Service  let android.app.Service  longArrayOf android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  preferenceManager android.app.Service  requestImmediateLocationNow android.app.Service  sendLocationToServer android.app.Service  startForeground android.app.Service  stopSelf android.app.Service  BroadcastReceiver android.content  
ComponentName android.content  Context android.content  Intent android.content  IntentFilter android.content  SharedPreferences android.content  Intent !android.content.BroadcastReceiver  LocationService !android.content.BroadcastReceiver  Log !android.content.BroadcastReceiver  PreferenceManager !android.content.BroadcastReceiver  TAG !android.content.BroadcastReceiver  startService !android.content.BroadcastReceiver  ActivityCompat android.content.Context  AlertDialog android.content.Context  BatteryManager android.content.Context  BatteryOptimizationHelper android.content.Context  Build android.content.Context  Button android.content.Context  
CHANNEL_ID android.content.Context  Context android.content.Context  
CookieManager android.content.Context  CoroutineScope android.content.Context  DashboardHijoActivity android.content.Context  Dispatchers android.content.Context  	Exception android.content.Context  FASTEST_UPDATE_INTERVAL android.content.Context  
FCMManager android.content.Context  Handler android.content.Context  Intent android.content.Context  IntentFilter android.content.Context  LOCATION_SERVICE android.content.Context  LOCATION_UPDATE_INTERVAL android.content.Context  LocationAvailability android.content.Context  LocationCallback android.content.Context  LocationRequest android.content.Context  LocationResult android.content.Context  LocationService android.content.Context  LocationServices android.content.Context  LocationUpdateRequest android.content.Context  Log android.content.Context  LoginRequest android.content.Context  Looper android.content.Context  MODE_PRIVATE android.content.Context  MainActivity android.content.Context  Manifest android.content.Context  NOTIFICATION_ID android.content.Context  NOTIFICATION_SERVICE android.content.Context  
NetworkClient android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  
POWER_SERVICE android.content.Context  PackageManager android.content.Context  
PendingIntent android.content.Context  PermissionHelper android.content.Context  PreferenceManager android.content.Context  Priority android.content.Context  ProfileSelectionActivity android.content.Context  R android.content.Context  RingtoneManager android.content.Context  START_NOT_STICKY android.content.Context  START_STICKY android.content.Context  String android.content.Context  
SupervisorJob android.content.Context  System android.content.Context  TAG android.content.Context  Toast android.content.Context  
TokenActivity android.content.Context  TokenRequest android.content.Context  View android.content.Context  WebAppInterface android.content.Context  WebSessionRequest android.content.Context  WebSettings android.content.Context  WebView android.content.Context  
WebViewClient android.content.Context  all android.content.Context  android android.content.Context  androidx android.content.Context  apply android.content.Context  cancel android.content.Context  com android.content.Context  contains android.content.Context   createWebSessionAndLoadDashboard android.content.Context  currentPollingInterval android.content.Context  fillTokenFormAutomatically android.content.Context  finish android.content.Context  fusedLocationClient android.content.Context  getLastKnownLocationImmediate android.content.Context  getSharedPreferences android.content.Context  	getString android.content.Context  getSystemService android.content.Context  hasBackgroundLocationPermission android.content.Context  hasLocationPermissions android.content.Context  injectDashboardJavaScript android.content.Context  isEmpty android.content.Context  isGpsEnabled android.content.Context  
isNotEmpty android.content.Context  
isNullOrEmpty android.content.Context  java android.content.Context  kotlinx android.content.Context  lastUrgentRequest android.content.Context  launch android.content.Context  let android.content.Context  lifecycleScope android.content.Context  longArrayOf android.content.Context  openAppSettings android.content.Context  openLocationSettings android.content.Context  packageName android.content.Context  preferenceManager android.content.Context  #requestBackgroundLocationPermission android.content.Context  !requestIgnoreBatteryOptimizations android.content.Context  requestImmediateLocationNow android.content.Context  requestLocationPermissions android.content.Context  run android.content.Context  sendImmediateLocationToServer android.content.Context  sendLocationToServer android.content.Context  	showError android.content.Context  showLoading android.content.Context  
startActivity android.content.Context  startForegroundService android.content.Context  startService android.content.Context  stopService android.content.Context  trim android.content.Context  
trimIndent android.content.Context  graphics android.content.Context.android  webkit android.content.Context.android  Bitmap (android.content.Context.android.graphics  GeolocationPermissions &android.content.Context.android.webkit  WebChromeClient &android.content.Context.android.webkit  WebResourceError &android.content.Context.android.webkit  WebResourceRequest &android.content.Context.android.webkit  Callback =android.content.Context.android.webkit.GeolocationPermissions  google android.content.Context.com  android "android.content.Context.com.google  gms *android.content.Context.com.google.android  location .android.content.Context.com.google.android.gms  LocationCallback 7android.content.Context.com.google.android.gms.location  LocationResult 7android.content.Context.com.google.android.gms.location  ActivityCompat android.content.ContextWrapper  AlertDialog android.content.ContextWrapper  BatteryManager android.content.ContextWrapper  BatteryOptimizationHelper android.content.ContextWrapper  Build android.content.ContextWrapper  Button android.content.ContextWrapper  
CHANNEL_ID android.content.ContextWrapper  Context android.content.ContextWrapper  
CookieManager android.content.ContextWrapper  CoroutineScope android.content.ContextWrapper  DashboardHijoActivity android.content.ContextWrapper  Dispatchers android.content.ContextWrapper  	Exception android.content.ContextWrapper  FASTEST_UPDATE_INTERVAL android.content.ContextWrapper  
FCMManager android.content.ContextWrapper  Handler android.content.ContextWrapper  Intent android.content.ContextWrapper  IntentFilter android.content.ContextWrapper  LOCATION_UPDATE_INTERVAL android.content.ContextWrapper  LocationAvailability android.content.ContextWrapper  LocationCallback android.content.ContextWrapper  LocationRequest android.content.ContextWrapper  LocationResult android.content.ContextWrapper  LocationService android.content.ContextWrapper  LocationServices android.content.ContextWrapper  LocationUpdateRequest android.content.ContextWrapper  Log android.content.ContextWrapper  LoginRequest android.content.ContextWrapper  Looper android.content.ContextWrapper  MainActivity android.content.ContextWrapper  Manifest android.content.ContextWrapper  NOTIFICATION_ID android.content.ContextWrapper  
NetworkClient android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  PackageManager android.content.ContextWrapper  
PendingIntent android.content.ContextWrapper  PermissionHelper android.content.ContextWrapper  PreferenceManager android.content.ContextWrapper  Priority android.content.ContextWrapper  ProfileSelectionActivity android.content.ContextWrapper  R android.content.ContextWrapper  RingtoneManager android.content.ContextWrapper  START_NOT_STICKY android.content.ContextWrapper  START_STICKY android.content.ContextWrapper  String android.content.ContextWrapper  
SupervisorJob android.content.ContextWrapper  System android.content.ContextWrapper  TAG android.content.ContextWrapper  Toast android.content.ContextWrapper  
TokenActivity android.content.ContextWrapper  TokenRequest android.content.ContextWrapper  View android.content.ContextWrapper  WebAppInterface android.content.ContextWrapper  WebSessionRequest android.content.ContextWrapper  WebSettings android.content.ContextWrapper  WebView android.content.ContextWrapper  
WebViewClient android.content.ContextWrapper  all android.content.ContextWrapper  android android.content.ContextWrapper  androidx android.content.ContextWrapper  apply android.content.ContextWrapper  cancel android.content.ContextWrapper  com android.content.ContextWrapper  contains android.content.ContextWrapper   createWebSessionAndLoadDashboard android.content.ContextWrapper  currentPollingInterval android.content.ContextWrapper  fillTokenFormAutomatically android.content.ContextWrapper  finish android.content.ContextWrapper  fusedLocationClient android.content.ContextWrapper  getLastKnownLocationImmediate android.content.ContextWrapper  getSystemService android.content.ContextWrapper  hasBackgroundLocationPermission android.content.ContextWrapper  hasLocationPermissions android.content.ContextWrapper  injectDashboardJavaScript android.content.ContextWrapper  isEmpty android.content.ContextWrapper  isGpsEnabled android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  
isNullOrEmpty android.content.ContextWrapper  java android.content.ContextWrapper  kotlinx android.content.ContextWrapper  lastUrgentRequest android.content.ContextWrapper  launch android.content.ContextWrapper  let android.content.ContextWrapper  lifecycleScope android.content.ContextWrapper  longArrayOf android.content.ContextWrapper  openAppSettings android.content.ContextWrapper  openLocationSettings android.content.ContextWrapper  preferenceManager android.content.ContextWrapper  registerReceiver android.content.ContextWrapper  #requestBackgroundLocationPermission android.content.ContextWrapper  !requestIgnoreBatteryOptimizations android.content.ContextWrapper  requestImmediateLocationNow android.content.ContextWrapper  requestLocationPermissions android.content.ContextWrapper  run android.content.ContextWrapper  sendImmediateLocationToServer android.content.ContextWrapper  sendLocationToServer android.content.ContextWrapper  	showError android.content.ContextWrapper  showLoading android.content.ContextWrapper  
startActivity android.content.ContextWrapper  startService android.content.ContextWrapper  trim android.content.ContextWrapper  
trimIndent android.content.ContextWrapper  graphics &android.content.ContextWrapper.android  webkit &android.content.ContextWrapper.android  Bitmap /android.content.ContextWrapper.android.graphics  GeolocationPermissions -android.content.ContextWrapper.android.webkit  WebChromeClient -android.content.ContextWrapper.android.webkit  WebResourceError -android.content.ContextWrapper.android.webkit  WebResourceRequest -android.content.ContextWrapper.android.webkit  Callback Dandroid.content.ContextWrapper.android.webkit.GeolocationPermissions  google "android.content.ContextWrapper.com  android )android.content.ContextWrapper.com.google  gms 1android.content.ContextWrapper.com.google.android  location 5android.content.ContextWrapper.com.google.android.gms  LocationCallback >android.content.ContextWrapper.com.google.android.gms.location  LocationResult >android.content.ContextWrapper.com.google.android.gms.location  OnClickListener android.content.DialogInterface  <SAM-CONSTRUCTOR> /android.content.DialogInterface.OnClickListener  ACTION_BATTERY_CHANGED android.content.Intent  ACTION_BOOT_COMPLETED android.content.Intent  ACTION_MY_PACKAGE_REPLACED android.content.Intent  ACTION_PACKAGE_REPLACED android.content.Intent  FLAG_ACTIVITY_CLEAR_TOP android.content.Intent  Intent android.content.Intent  Uri android.content.Intent  action android.content.Intent  addFlags android.content.Intent  apply android.content.Intent  data android.content.Intent  getIntExtra android.content.Intent  putExtra android.content.Intent  edit !android.content.SharedPreferences  
getBoolean !android.content.SharedPreferences  getInt !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  clear (android.content.SharedPreferences.Editor  
putBoolean (android.content.SharedPreferences.Editor  putInt (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  Bitmap android.graphics  Location android.location  LocationManager android.location  accuracy android.location.Location  latitude android.location.Location  let android.location.Location  	longitude android.location.Location  GPS_PROVIDER  android.location.LocationManager  isProviderEnabled  android.location.LocationManager  RingtoneManager 
android.media  TYPE_NOTIFICATION android.media.RingtoneManager  
getDefaultUri android.media.RingtoneManager  Uri android.net  	fromParts android.net.Uri  parse android.net.Uri  BatteryManager 
android.os  Build 
android.os  Bundle 
android.os  Handler 
android.os  IBinder 
android.os  Looper 
android.os  PowerManager 
android.os  EXTRA_LEVEL android.os.BatteryManager  EXTRA_SCALE android.os.BatteryManager  SDK_INT android.os.Build.VERSION  M android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  postDelayed android.os.Handler  
getMainLooper android.os.Looper  isIgnoringBatteryOptimizations android.os.PowerManager  Settings android.provider  #ACTION_APPLICATION_DETAILS_SETTINGS android.provider.Settings  +ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS android.provider.Settings  ACTION_LOCATION_SOURCE_SETTINGS android.provider.Settings  +ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS android.provider.Settings  ACTION_SETTINGS android.provider.Settings  Log android.util  d android.util.Log  e android.util.Log  w android.util.Log  View android.view  AlertDialog  android.view.ContextThemeWrapper  BatteryOptimizationHelper  android.view.ContextThemeWrapper  Button  android.view.ContextThemeWrapper  
CookieManager  android.view.ContextThemeWrapper  DashboardHijoActivity  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  
FCMManager  android.view.ContextThemeWrapper  Handler  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  LocationService  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  LoginRequest  android.view.ContextThemeWrapper  Looper  android.view.ContextThemeWrapper  MainActivity  android.view.ContextThemeWrapper  
NetworkClient  android.view.ContextThemeWrapper  PackageManager  android.view.ContextThemeWrapper  PermissionHelper  android.view.ContextThemeWrapper  PreferenceManager  android.view.ContextThemeWrapper  ProfileSelectionActivity  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  System  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  
TokenActivity  android.view.ContextThemeWrapper  TokenRequest  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  WebAppInterface  android.view.ContextThemeWrapper  WebSessionRequest  android.view.ContextThemeWrapper  WebSettings  android.view.ContextThemeWrapper  WebView  android.view.ContextThemeWrapper  
WebViewClient  android.view.ContextThemeWrapper  all  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  androidx  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  com  android.view.ContextThemeWrapper  contains  android.view.ContextThemeWrapper   createWebSessionAndLoadDashboard  android.view.ContextThemeWrapper  fillTokenFormAutomatically  android.view.ContextThemeWrapper  finish  android.view.ContextThemeWrapper  hasBackgroundLocationPermission  android.view.ContextThemeWrapper  hasLocationPermissions  android.view.ContextThemeWrapper  injectDashboardJavaScript  android.view.ContextThemeWrapper  isEmpty  android.view.ContextThemeWrapper  isGpsEnabled  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  kotlinx  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  lifecycleScope  android.view.ContextThemeWrapper  openAppSettings  android.view.ContextThemeWrapper  openLocationSettings  android.view.ContextThemeWrapper  preferenceManager  android.view.ContextThemeWrapper  #requestBackgroundLocationPermission  android.view.ContextThemeWrapper  !requestIgnoreBatteryOptimizations  android.view.ContextThemeWrapper  requestLocationPermissions  android.view.ContextThemeWrapper  run  android.view.ContextThemeWrapper  sendImmediateLocationToServer  android.view.ContextThemeWrapper  	showError  android.view.ContextThemeWrapper  showLoading  android.view.ContextThemeWrapper  
startActivity  android.view.ContextThemeWrapper  startService  android.view.ContextThemeWrapper  trim  android.view.ContextThemeWrapper  
trimIndent  android.view.ContextThemeWrapper  graphics (android.view.ContextThemeWrapper.android  webkit (android.view.ContextThemeWrapper.android  Bitmap 1android.view.ContextThemeWrapper.android.graphics  GeolocationPermissions /android.view.ContextThemeWrapper.android.webkit  WebChromeClient /android.view.ContextThemeWrapper.android.webkit  WebResourceError /android.view.ContextThemeWrapper.android.webkit  WebResourceRequest /android.view.ContextThemeWrapper.android.webkit  Callback Fandroid.view.ContextThemeWrapper.android.webkit.GeolocationPermissions  google $android.view.ContextThemeWrapper.com  android +android.view.ContextThemeWrapper.com.google  gms 3android.view.ContextThemeWrapper.com.google.android  location 7android.view.ContextThemeWrapper.com.google.android.gms  LocationCallback @android.view.ContextThemeWrapper.com.google.android.gms.location  LocationResult @android.view.ContextThemeWrapper.com.google.android.gms.location  GONE android.view.View  OnClickListener android.view.View  VISIBLE android.view.View  	isEnabled android.view.View  postDelayed android.view.View  setOnClickListener android.view.View  
visibility android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  
CookieManager android.webkit  JavascriptInterface android.webkit  
ValueCallback android.webkit  WebChromeClient android.webkit  WebResourceError android.webkit  WebResourceRequest android.webkit  WebSettings android.webkit  WebView android.webkit  
WebViewClient android.webkit  getInstance android.webkit.CookieManager  setAcceptCookie android.webkit.CookieManager  setAcceptThirdPartyCookies android.webkit.CookieManager  Callback %android.webkit.GeolocationPermissions  invoke .android.webkit.GeolocationPermissions.Callback  <SAM-CONSTRUCTOR> android.webkit.ValueCallback  description android.webkit.WebResourceError  LOAD_DEFAULT android.webkit.WebSettings  LayoutAlgorithm android.webkit.WebSettings  MIXED_CONTENT_ALWAYS_ALLOW android.webkit.WebSettings  WebSettings android.webkit.WebSettings  allowContentAccess android.webkit.WebSettings  allowFileAccess android.webkit.WebSettings  apply android.webkit.WebSettings  builtInZoomControls android.webkit.WebSettings  	cacheMode android.webkit.WebSettings  databaseEnabled android.webkit.WebSettings  defaultFontSize android.webkit.WebSettings  displayZoomControls android.webkit.WebSettings  domStorageEnabled android.webkit.WebSettings  javaScriptEnabled android.webkit.WebSettings  layoutAlgorithm android.webkit.WebSettings  loadWithOverviewMode android.webkit.WebSettings  minimumFontSize android.webkit.WebSettings  mixedContentMode android.webkit.WebSettings  setGeolocationEnabled android.webkit.WebSettings  setSupportZoom android.webkit.WebSettings  textZoom android.webkit.WebSettings  useWideViewPort android.webkit.WebSettings  TEXT_AUTOSIZING *android.webkit.WebSettings.LayoutAlgorithm  addJavascriptInterface android.webkit.WebView  evaluateJavascript android.webkit.WebView  loadDataWithBaseURL android.webkit.WebView  loadUrl android.webkit.WebView  postDelayed android.webkit.WebView  setBackgroundColor android.webkit.WebView  setInitialScale android.webkit.WebView  settings android.webkit.WebView  webChromeClient android.webkit.WebView  
webViewClient android.webkit.WebView  android android.webkit.WebViewClient  contains android.webkit.WebViewClient  fillTokenFormAutomatically android.webkit.WebViewClient  injectDashboardJavaScript android.webkit.WebViewClient  onPageFinished android.webkit.WebViewClient  
onPageStarted android.webkit.WebViewClient  onReceivedError android.webkit.WebViewClient  Button android.widget  EditText android.widget  ProgressBar android.widget  TextView android.widget  Toast android.widget  	isEnabled android.widget.Button  setOnClickListener android.widget.Button  error android.widget.EditText  	isEnabled android.widget.EditText  text android.widget.EditText  
visibility android.widget.ProgressBar  error android.widget.TextView  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  AlertDialog #androidx.activity.ComponentActivity  Array #androidx.activity.ComponentActivity  BatteryOptimizationHelper #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  Button #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  
CookieManager #androidx.activity.ComponentActivity  DashboardHijoActivity #androidx.activity.ComponentActivity  EditText #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  
FCMManager #androidx.activity.ComponentActivity  Handler #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  IntArray #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  LocationService #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  LoginRequest #androidx.activity.ComponentActivity  Looper #androidx.activity.ComponentActivity  MainActivity #androidx.activity.ComponentActivity  
NetworkClient #androidx.activity.ComponentActivity  PackageManager #androidx.activity.ComponentActivity  PermissionHelper #androidx.activity.ComponentActivity  PreferenceManager #androidx.activity.ComponentActivity  ProfileSelectionActivity #androidx.activity.ComponentActivity  ProgressBar #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  System #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  
TokenActivity #androidx.activity.ComponentActivity  TokenRequest #androidx.activity.ComponentActivity  Unit #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  WebAppInterface #androidx.activity.ComponentActivity  WebSessionRequest #androidx.activity.ComponentActivity  WebSettings #androidx.activity.ComponentActivity  WebView #androidx.activity.ComponentActivity  
WebViewClient #androidx.activity.ComponentActivity  all #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  androidx #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  com #androidx.activity.ComponentActivity  contains #androidx.activity.ComponentActivity   createWebSessionAndLoadDashboard #androidx.activity.ComponentActivity  fillTokenFormAutomatically #androidx.activity.ComponentActivity  finish #androidx.activity.ComponentActivity  hasBackgroundLocationPermission #androidx.activity.ComponentActivity  hasLocationPermissions #androidx.activity.ComponentActivity  injectDashboardJavaScript #androidx.activity.ComponentActivity  isEmpty #androidx.activity.ComponentActivity  isGpsEnabled #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  kotlinx #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  lifecycleScope #androidx.activity.ComponentActivity  openAppSettings #androidx.activity.ComponentActivity  openLocationSettings #androidx.activity.ComponentActivity  preferenceManager #androidx.activity.ComponentActivity  #requestBackgroundLocationPermission #androidx.activity.ComponentActivity  !requestIgnoreBatteryOptimizations #androidx.activity.ComponentActivity  requestLocationPermissions #androidx.activity.ComponentActivity  run #androidx.activity.ComponentActivity  sendImmediateLocationToServer #androidx.activity.ComponentActivity  	showError #androidx.activity.ComponentActivity  showLoading #androidx.activity.ComponentActivity  
startActivity #androidx.activity.ComponentActivity  startService #androidx.activity.ComponentActivity  trim #androidx.activity.ComponentActivity  
trimIndent #androidx.activity.ComponentActivity  AlertDialog -androidx.activity.ComponentActivity.Companion  BatteryOptimizationHelper -androidx.activity.ComponentActivity.Companion  
CookieManager -androidx.activity.ComponentActivity.Companion  DashboardHijoActivity -androidx.activity.ComponentActivity.Companion  
FCMManager -androidx.activity.ComponentActivity.Companion  Handler -androidx.activity.ComponentActivity.Companion  Intent -androidx.activity.ComponentActivity.Companion  LocationService -androidx.activity.ComponentActivity.Companion  Log -androidx.activity.ComponentActivity.Companion  LoginRequest -androidx.activity.ComponentActivity.Companion  Looper -androidx.activity.ComponentActivity.Companion  MainActivity -androidx.activity.ComponentActivity.Companion  
NetworkClient -androidx.activity.ComponentActivity.Companion  PackageManager -androidx.activity.ComponentActivity.Companion  PermissionHelper -androidx.activity.ComponentActivity.Companion  PreferenceManager -androidx.activity.ComponentActivity.Companion  ProfileSelectionActivity -androidx.activity.ComponentActivity.Companion  R -androidx.activity.ComponentActivity.Companion  System -androidx.activity.ComponentActivity.Companion  Toast -androidx.activity.ComponentActivity.Companion  
TokenActivity -androidx.activity.ComponentActivity.Companion  TokenRequest -androidx.activity.ComponentActivity.Companion  View -androidx.activity.ComponentActivity.Companion  WebAppInterface -androidx.activity.ComponentActivity.Companion  WebSessionRequest -androidx.activity.ComponentActivity.Companion  WebSettings -androidx.activity.ComponentActivity.Companion  
WebViewClient -androidx.activity.ComponentActivity.Companion  all -androidx.activity.ComponentActivity.Companion  android -androidx.activity.ComponentActivity.Companion  androidx -androidx.activity.ComponentActivity.Companion  apply -androidx.activity.ComponentActivity.Companion  com -androidx.activity.ComponentActivity.Companion  contains -androidx.activity.ComponentActivity.Companion   createWebSessionAndLoadDashboard -androidx.activity.ComponentActivity.Companion  fillTokenFormAutomatically -androidx.activity.ComponentActivity.Companion  finish -androidx.activity.ComponentActivity.Companion  hasBackgroundLocationPermission -androidx.activity.ComponentActivity.Companion  hasLocationPermissions -androidx.activity.ComponentActivity.Companion  injectDashboardJavaScript -androidx.activity.ComponentActivity.Companion  isEmpty -androidx.activity.ComponentActivity.Companion  isGpsEnabled -androidx.activity.ComponentActivity.Companion  
isNotEmpty -androidx.activity.ComponentActivity.Companion  java -androidx.activity.ComponentActivity.Companion  kotlinx -androidx.activity.ComponentActivity.Companion  launch -androidx.activity.ComponentActivity.Companion  let -androidx.activity.ComponentActivity.Companion  lifecycleScope -androidx.activity.ComponentActivity.Companion  openAppSettings -androidx.activity.ComponentActivity.Companion  openLocationSettings -androidx.activity.ComponentActivity.Companion  preferenceManager -androidx.activity.ComponentActivity.Companion  #requestBackgroundLocationPermission -androidx.activity.ComponentActivity.Companion  !requestIgnoreBatteryOptimizations -androidx.activity.ComponentActivity.Companion  requestLocationPermissions -androidx.activity.ComponentActivity.Companion  run -androidx.activity.ComponentActivity.Companion  sendImmediateLocationToServer -androidx.activity.ComponentActivity.Companion  	showError -androidx.activity.ComponentActivity.Companion  showLoading -androidx.activity.ComponentActivity.Companion  
startActivity -androidx.activity.ComponentActivity.Companion  startService -androidx.activity.ComponentActivity.Companion  trim -androidx.activity.ComponentActivity.Companion  
trimIndent -androidx.activity.ComponentActivity.Companion  graphics +androidx.activity.ComponentActivity.android  location +androidx.activity.ComponentActivity.android  webkit +androidx.activity.ComponentActivity.android  Bitmap 4androidx.activity.ComponentActivity.android.graphics  Location 4androidx.activity.ComponentActivity.android.location  
CookieManager 2androidx.activity.ComponentActivity.android.webkit  GeolocationPermissions 2androidx.activity.ComponentActivity.android.webkit  WebChromeClient 2androidx.activity.ComponentActivity.android.webkit  WebResourceError 2androidx.activity.ComponentActivity.android.webkit  WebResourceRequest 2androidx.activity.ComponentActivity.android.webkit  Callback Iandroidx.activity.ComponentActivity.android.webkit.GeolocationPermissions  google 'androidx.activity.ComponentActivity.com  android .androidx.activity.ComponentActivity.com.google  gms 6androidx.activity.ComponentActivity.com.google.android  location :androidx.activity.ComponentActivity.com.google.android.gms  LocationCallback Candroidx.activity.ComponentActivity.com.google.android.gms.location  LocationResult Candroidx.activity.ComponentActivity.com.google.android.gms.location  AlertDialog androidx.appcompat.app  AppCompatActivity androidx.appcompat.app  Builder "androidx.appcompat.app.AlertDialog  
setCancelable *androidx.appcompat.app.AlertDialog.Builder  
setMessage *androidx.appcompat.app.AlertDialog.Builder  setNegativeButton *androidx.appcompat.app.AlertDialog.Builder  setPositiveButton *androidx.appcompat.app.AlertDialog.Builder  setTitle *androidx.appcompat.app.AlertDialog.Builder  show *androidx.appcompat.app.AlertDialog.Builder  AlertDialog (androidx.appcompat.app.AppCompatActivity  BatteryOptimizationHelper (androidx.appcompat.app.AppCompatActivity  Button (androidx.appcompat.app.AppCompatActivity  
CookieManager (androidx.appcompat.app.AppCompatActivity  DashboardHijoActivity (androidx.appcompat.app.AppCompatActivity  	Exception (androidx.appcompat.app.AppCompatActivity  
FCMManager (androidx.appcompat.app.AppCompatActivity  Handler (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  LocationService (androidx.appcompat.app.AppCompatActivity  Log (androidx.appcompat.app.AppCompatActivity  LoginRequest (androidx.appcompat.app.AppCompatActivity  Looper (androidx.appcompat.app.AppCompatActivity  MainActivity (androidx.appcompat.app.AppCompatActivity  
NetworkClient (androidx.appcompat.app.AppCompatActivity  PackageManager (androidx.appcompat.app.AppCompatActivity  PermissionHelper (androidx.appcompat.app.AppCompatActivity  PreferenceManager (androidx.appcompat.app.AppCompatActivity  ProfileSelectionActivity (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  System (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  
TokenActivity (androidx.appcompat.app.AppCompatActivity  TokenRequest (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  WebAppInterface (androidx.appcompat.app.AppCompatActivity  WebSessionRequest (androidx.appcompat.app.AppCompatActivity  WebSettings (androidx.appcompat.app.AppCompatActivity  WebView (androidx.appcompat.app.AppCompatActivity  
WebViewClient (androidx.appcompat.app.AppCompatActivity  all (androidx.appcompat.app.AppCompatActivity  android (androidx.appcompat.app.AppCompatActivity  androidx (androidx.appcompat.app.AppCompatActivity  apply (androidx.appcompat.app.AppCompatActivity  com (androidx.appcompat.app.AppCompatActivity  contains (androidx.appcompat.app.AppCompatActivity   createWebSessionAndLoadDashboard (androidx.appcompat.app.AppCompatActivity  fillTokenFormAutomatically (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  finish (androidx.appcompat.app.AppCompatActivity  hasBackgroundLocationPermission (androidx.appcompat.app.AppCompatActivity  hasLocationPermissions (androidx.appcompat.app.AppCompatActivity  injectDashboardJavaScript (androidx.appcompat.app.AppCompatActivity  isEmpty (androidx.appcompat.app.AppCompatActivity  isGpsEnabled (androidx.appcompat.app.AppCompatActivity  
isNotEmpty (androidx.appcompat.app.AppCompatActivity  java (androidx.appcompat.app.AppCompatActivity  kotlinx (androidx.appcompat.app.AppCompatActivity  launch (androidx.appcompat.app.AppCompatActivity  let (androidx.appcompat.app.AppCompatActivity  lifecycleScope (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  	onDestroy (androidx.appcompat.app.AppCompatActivity  onRequestPermissionsResult (androidx.appcompat.app.AppCompatActivity  onResume (androidx.appcompat.app.AppCompatActivity  openAppSettings (androidx.appcompat.app.AppCompatActivity  openLocationSettings (androidx.appcompat.app.AppCompatActivity  preferenceManager (androidx.appcompat.app.AppCompatActivity  #requestBackgroundLocationPermission (androidx.appcompat.app.AppCompatActivity  !requestIgnoreBatteryOptimizations (androidx.appcompat.app.AppCompatActivity  requestLocationPermissions (androidx.appcompat.app.AppCompatActivity  run (androidx.appcompat.app.AppCompatActivity  sendImmediateLocationToServer (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  	showError (androidx.appcompat.app.AppCompatActivity  showLoading (androidx.appcompat.app.AppCompatActivity  
startActivity (androidx.appcompat.app.AppCompatActivity  startService (androidx.appcompat.app.AppCompatActivity  trim (androidx.appcompat.app.AppCompatActivity  
trimIndent (androidx.appcompat.app.AppCompatActivity  graphics 0androidx.appcompat.app.AppCompatActivity.android  webkit 0androidx.appcompat.app.AppCompatActivity.android  Bitmap 9androidx.appcompat.app.AppCompatActivity.android.graphics  GeolocationPermissions 7androidx.appcompat.app.AppCompatActivity.android.webkit  WebChromeClient 7androidx.appcompat.app.AppCompatActivity.android.webkit  WebResourceError 7androidx.appcompat.app.AppCompatActivity.android.webkit  WebResourceRequest 7androidx.appcompat.app.AppCompatActivity.android.webkit  Callback Nandroidx.appcompat.app.AppCompatActivity.android.webkit.GeolocationPermissions  google ,androidx.appcompat.app.AppCompatActivity.com  android 3androidx.appcompat.app.AppCompatActivity.com.google  gms ;androidx.appcompat.app.AppCompatActivity.com.google.android  location ?androidx.appcompat.app.AppCompatActivity.com.google.android.gms  LocationCallback Handroidx.appcompat.app.AppCompatActivity.com.google.android.gms.location  LocationResult Handroidx.appcompat.app.AppCompatActivity.com.google.android.gms.location  ActivityCompat androidx.core.app  NotificationCompat androidx.core.app  checkSelfPermission  androidx.core.app.ActivityCompat  requestPermissions  androidx.core.app.ActivityCompat  AlertDialog #androidx.core.app.ComponentActivity  Array #androidx.core.app.ComponentActivity  BatteryOptimizationHelper #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Button #androidx.core.app.ComponentActivity  
CookieManager #androidx.core.app.ComponentActivity  DashboardHijoActivity #androidx.core.app.ComponentActivity  EditText #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  
FCMManager #androidx.core.app.ComponentActivity  Handler #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  IntArray #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  LocationService #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  LoginRequest #androidx.core.app.ComponentActivity  Looper #androidx.core.app.ComponentActivity  MainActivity #androidx.core.app.ComponentActivity  
NetworkClient #androidx.core.app.ComponentActivity  PackageManager #androidx.core.app.ComponentActivity  PermissionHelper #androidx.core.app.ComponentActivity  PreferenceManager #androidx.core.app.ComponentActivity  ProfileSelectionActivity #androidx.core.app.ComponentActivity  ProgressBar #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  System #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  
TokenActivity #androidx.core.app.ComponentActivity  TokenRequest #androidx.core.app.ComponentActivity  Unit #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  WebAppInterface #androidx.core.app.ComponentActivity  WebSessionRequest #androidx.core.app.ComponentActivity  WebSettings #androidx.core.app.ComponentActivity  WebView #androidx.core.app.ComponentActivity  
WebViewClient #androidx.core.app.ComponentActivity  all #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  androidx #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  com #androidx.core.app.ComponentActivity  contains #androidx.core.app.ComponentActivity   createWebSessionAndLoadDashboard #androidx.core.app.ComponentActivity  fillTokenFormAutomatically #androidx.core.app.ComponentActivity  finish #androidx.core.app.ComponentActivity  hasBackgroundLocationPermission #androidx.core.app.ComponentActivity  hasLocationPermissions #androidx.core.app.ComponentActivity  injectDashboardJavaScript #androidx.core.app.ComponentActivity  isEmpty #androidx.core.app.ComponentActivity  isGpsEnabled #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  kotlinx #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  lifecycleScope #androidx.core.app.ComponentActivity  openAppSettings #androidx.core.app.ComponentActivity  openLocationSettings #androidx.core.app.ComponentActivity  preferenceManager #androidx.core.app.ComponentActivity  #requestBackgroundLocationPermission #androidx.core.app.ComponentActivity  !requestIgnoreBatteryOptimizations #androidx.core.app.ComponentActivity  requestLocationPermissions #androidx.core.app.ComponentActivity  run #androidx.core.app.ComponentActivity  sendImmediateLocationToServer #androidx.core.app.ComponentActivity  	showError #androidx.core.app.ComponentActivity  showLoading #androidx.core.app.ComponentActivity  
startActivity #androidx.core.app.ComponentActivity  startService #androidx.core.app.ComponentActivity  trim #androidx.core.app.ComponentActivity  
trimIndent #androidx.core.app.ComponentActivity  graphics +androidx.core.app.ComponentActivity.android  location +androidx.core.app.ComponentActivity.android  webkit +androidx.core.app.ComponentActivity.android  Bitmap 4androidx.core.app.ComponentActivity.android.graphics  Location 4androidx.core.app.ComponentActivity.android.location  
CookieManager 2androidx.core.app.ComponentActivity.android.webkit  GeolocationPermissions 2androidx.core.app.ComponentActivity.android.webkit  WebChromeClient 2androidx.core.app.ComponentActivity.android.webkit  WebResourceError 2androidx.core.app.ComponentActivity.android.webkit  WebResourceRequest 2androidx.core.app.ComponentActivity.android.webkit  Callback Iandroidx.core.app.ComponentActivity.android.webkit.GeolocationPermissions  google 'androidx.core.app.ComponentActivity.com  android .androidx.core.app.ComponentActivity.com.google  gms 6androidx.core.app.ComponentActivity.com.google.android  location :androidx.core.app.ComponentActivity.com.google.android.gms  LocationCallback Candroidx.core.app.ComponentActivity.com.google.android.gms.location  LocationResult Candroidx.core.app.ComponentActivity.com.google.android.gms.location  BigTextStyle $androidx.core.app.NotificationCompat  Builder $androidx.core.app.NotificationCompat  CATEGORY_ALARM $androidx.core.app.NotificationCompat  
PRIORITY_HIGH $androidx.core.app.NotificationCompat  PRIORITY_LOW $androidx.core.app.NotificationCompat  bigText 1androidx.core.app.NotificationCompat.BigTextStyle  build ,androidx.core.app.NotificationCompat.Builder  
setAutoCancel ,androidx.core.app.NotificationCompat.Builder  setCategory ,androidx.core.app.NotificationCompat.Builder  setContentIntent ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  
setOngoing ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  setSound ,androidx.core.app.NotificationCompat.Builder  setStyle ,androidx.core.app.NotificationCompat.Builder  
setSubText ,androidx.core.app.NotificationCompat.Builder  
setVibrate ,androidx.core.app.NotificationCompat.Builder  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  AlertDialog &androidx.fragment.app.FragmentActivity  BatteryOptimizationHelper &androidx.fragment.app.FragmentActivity  Button &androidx.fragment.app.FragmentActivity  
CookieManager &androidx.fragment.app.FragmentActivity  DashboardHijoActivity &androidx.fragment.app.FragmentActivity  	Exception &androidx.fragment.app.FragmentActivity  
FCMManager &androidx.fragment.app.FragmentActivity  Handler &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  LocationService &androidx.fragment.app.FragmentActivity  Log &androidx.fragment.app.FragmentActivity  LoginRequest &androidx.fragment.app.FragmentActivity  Looper &androidx.fragment.app.FragmentActivity  MainActivity &androidx.fragment.app.FragmentActivity  
NetworkClient &androidx.fragment.app.FragmentActivity  PackageManager &androidx.fragment.app.FragmentActivity  PermissionHelper &androidx.fragment.app.FragmentActivity  PreferenceManager &androidx.fragment.app.FragmentActivity  ProfileSelectionActivity &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  System &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  
TokenActivity &androidx.fragment.app.FragmentActivity  TokenRequest &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  WebAppInterface &androidx.fragment.app.FragmentActivity  WebSessionRequest &androidx.fragment.app.FragmentActivity  WebSettings &androidx.fragment.app.FragmentActivity  WebView &androidx.fragment.app.FragmentActivity  
WebViewClient &androidx.fragment.app.FragmentActivity  all &androidx.fragment.app.FragmentActivity  android &androidx.fragment.app.FragmentActivity  androidx &androidx.fragment.app.FragmentActivity  apply &androidx.fragment.app.FragmentActivity  com &androidx.fragment.app.FragmentActivity  contains &androidx.fragment.app.FragmentActivity   createWebSessionAndLoadDashboard &androidx.fragment.app.FragmentActivity  fillTokenFormAutomatically &androidx.fragment.app.FragmentActivity  finish &androidx.fragment.app.FragmentActivity  hasBackgroundLocationPermission &androidx.fragment.app.FragmentActivity  hasLocationPermissions &androidx.fragment.app.FragmentActivity  injectDashboardJavaScript &androidx.fragment.app.FragmentActivity  isEmpty &androidx.fragment.app.FragmentActivity  isGpsEnabled &androidx.fragment.app.FragmentActivity  
isNotEmpty &androidx.fragment.app.FragmentActivity  java &androidx.fragment.app.FragmentActivity  kotlinx &androidx.fragment.app.FragmentActivity  launch &androidx.fragment.app.FragmentActivity  let &androidx.fragment.app.FragmentActivity  lifecycleScope &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  onRequestPermissionsResult &androidx.fragment.app.FragmentActivity  onResume &androidx.fragment.app.FragmentActivity  openAppSettings &androidx.fragment.app.FragmentActivity  openLocationSettings &androidx.fragment.app.FragmentActivity  preferenceManager &androidx.fragment.app.FragmentActivity  #requestBackgroundLocationPermission &androidx.fragment.app.FragmentActivity  !requestIgnoreBatteryOptimizations &androidx.fragment.app.FragmentActivity  requestLocationPermissions &androidx.fragment.app.FragmentActivity  run &androidx.fragment.app.FragmentActivity  sendImmediateLocationToServer &androidx.fragment.app.FragmentActivity  	showError &androidx.fragment.app.FragmentActivity  showLoading &androidx.fragment.app.FragmentActivity  
startActivity &androidx.fragment.app.FragmentActivity  startService &androidx.fragment.app.FragmentActivity  trim &androidx.fragment.app.FragmentActivity  
trimIndent &androidx.fragment.app.FragmentActivity  graphics .androidx.fragment.app.FragmentActivity.android  webkit .androidx.fragment.app.FragmentActivity.android  Bitmap 7androidx.fragment.app.FragmentActivity.android.graphics  GeolocationPermissions 5androidx.fragment.app.FragmentActivity.android.webkit  WebChromeClient 5androidx.fragment.app.FragmentActivity.android.webkit  WebResourceError 5androidx.fragment.app.FragmentActivity.android.webkit  WebResourceRequest 5androidx.fragment.app.FragmentActivity.android.webkit  Callback Landroidx.fragment.app.FragmentActivity.android.webkit.GeolocationPermissions  google *androidx.fragment.app.FragmentActivity.com  android 1androidx.fragment.app.FragmentActivity.com.google  gms 9androidx.fragment.app.FragmentActivity.com.google.android  location =androidx.fragment.app.FragmentActivity.com.google.android.gms  LocationCallback Fandroidx.fragment.app.FragmentActivity.com.google.android.gms.location  LocationResult Fandroidx.fragment.app.FragmentActivity.com.google.android.gms.location  LifecycleCoroutineScope androidx.lifecycle  lifecycleScope androidx.lifecycle  launch *androidx.lifecycle.LifecycleCoroutineScope  AlertDialog com.example.corredores  AppCompatActivity com.example.corredores  Array com.example.corredores  BatteryOptimizationHelper com.example.corredores  Boolean com.example.corredores  Bundle com.example.corredores  Button com.example.corredores  Context com.example.corredores  
CookieManager com.example.corredores  DashboardHijoActivity com.example.corredores  EditText com.example.corredores  	Exception com.example.corredores  
FCMManager com.example.corredores  Handler com.example.corredores  Int com.example.corredores  IntArray com.example.corredores  Intent com.example.corredores  JavascriptInterface com.example.corredores  LocationService com.example.corredores  Log com.example.corredores  
LoginActivity com.example.corredores  LoginRequest com.example.corredores  Looper com.example.corredores  MainActivity com.example.corredores  
NetworkClient com.example.corredores  NotificationRequest com.example.corredores  PackageManager com.example.corredores  PermissionHelper com.example.corredores  PreferenceManager com.example.corredores  ProfileSelectionActivity com.example.corredores  ProgressBar com.example.corredores  R com.example.corredores  SplashActivity com.example.corredores  String com.example.corredores  System com.example.corredores  Toast com.example.corredores  
TokenActivity com.example.corredores  TokenRequest com.example.corredores  Unit com.example.corredores  View com.example.corredores  WebAppInterface com.example.corredores  WebSessionRequest com.example.corredores  WebSettings com.example.corredores  WebView com.example.corredores  
WebViewClient com.example.corredores  all com.example.corredores  android com.example.corredores  androidx com.example.corredores  apply com.example.corredores  com com.example.corredores  contains com.example.corredores  context com.example.corredores   createWebSessionAndLoadDashboard com.example.corredores  fillTokenFormAutomatically com.example.corredores  finish com.example.corredores  hasBackgroundLocationPermission com.example.corredores  hasLocationPermissions com.example.corredores  ifEmpty com.example.corredores  injectDashboardJavaScript com.example.corredores  isEmpty com.example.corredores  isGpsEnabled com.example.corredores  
isNotEmpty com.example.corredores  java com.example.corredores  kotlinx com.example.corredores  launch com.example.corredores  let com.example.corredores  openAppSettings com.example.corredores  openLocationSettings com.example.corredores  preferenceManager com.example.corredores  #requestBackgroundLocationPermission com.example.corredores  !requestIgnoreBatteryOptimizations com.example.corredores  requestLocationPermissions com.example.corredores  run com.example.corredores  sendImmediateLocationToServer com.example.corredores  	showError com.example.corredores  showLoading com.example.corredores  
startActivity com.example.corredores  startService com.example.corredores  trim com.example.corredores  
trimIndent com.example.corredores  
CookieManager ,com.example.corredores.DashboardHijoActivity  R ,com.example.corredores.DashboardHijoActivity  
WebViewClient ,com.example.corredores.DashboardHijoActivity  apply ,com.example.corredores.DashboardHijoActivity  findViewById ,com.example.corredores.DashboardHijoActivity  setContentView ,com.example.corredores.DashboardHijoActivity  Intent $com.example.corredores.LoginActivity  LoginRequest $com.example.corredores.LoginActivity  MainActivity $com.example.corredores.LoginActivity  
NetworkClient $com.example.corredores.LoginActivity  PreferenceManager $com.example.corredores.LoginActivity  R $com.example.corredores.LoginActivity  Toast $com.example.corredores.LoginActivity  View $com.example.corredores.LoginActivity  btnLogin $com.example.corredores.LoginActivity  etFolio $com.example.corredores.LoginActivity  
etPassword $com.example.corredores.LoginActivity  findViewById $com.example.corredores.LoginActivity  finish $com.example.corredores.LoginActivity  	initViews $com.example.corredores.LoginActivity  isEmpty $com.example.corredores.LoginActivity  java $com.example.corredores.LoginActivity  launch $com.example.corredores.LoginActivity  lifecycleScope $com.example.corredores.LoginActivity  performLogin $com.example.corredores.LoginActivity  preferenceManager $com.example.corredores.LoginActivity  progressBar $com.example.corredores.LoginActivity  setContentView $com.example.corredores.LoginActivity  setupClickListeners $com.example.corredores.LoginActivity  	showError $com.example.corredores.LoginActivity  showLoading $com.example.corredores.LoginActivity  
startActivity $com.example.corredores.LoginActivity  trim $com.example.corredores.LoginActivity  AlertDialog #com.example.corredores.MainActivity  BatteryOptimizationHelper #com.example.corredores.MainActivity  
CookieManager #com.example.corredores.MainActivity  
FCMManager #com.example.corredores.MainActivity  Intent #com.example.corredores.MainActivity  LocationService #com.example.corredores.MainActivity  Log #com.example.corredores.MainActivity  MainActivity #com.example.corredores.MainActivity  
NetworkClient #com.example.corredores.MainActivity  PreferenceManager #com.example.corredores.MainActivity  ProfileSelectionActivity #com.example.corredores.MainActivity  R #com.example.corredores.MainActivity  System #com.example.corredores.MainActivity  Toast #com.example.corredores.MainActivity  
TokenActivity #com.example.corredores.MainActivity  WebAppInterface #com.example.corredores.MainActivity  WebSessionRequest #com.example.corredores.MainActivity  WebSettings #com.example.corredores.MainActivity  android #com.example.corredores.MainActivity  androidx #com.example.corredores.MainActivity  apply #com.example.corredores.MainActivity  com #com.example.corredores.MainActivity  contains #com.example.corredores.MainActivity   createWebSessionAndLoadDashboard #com.example.corredores.MainActivity  createWebSessionViaAPI #com.example.corredores.MainActivity  fillTokenFormAutomatically #com.example.corredores.MainActivity  findViewById #com.example.corredores.MainActivity  finish #com.example.corredores.MainActivity  getImmediateLocation #com.example.corredores.MainActivity  injectDashboardJavaScript #com.example.corredores.MainActivity  java #com.example.corredores.MainActivity  kotlinx #com.example.corredores.MainActivity  launch #com.example.corredores.MainActivity  let #com.example.corredores.MainActivity  lifecycleScope #com.example.corredores.MainActivity  logout #com.example.corredores.MainActivity  preferenceManager #com.example.corredores.MainActivity  redirectToLogin #com.example.corredores.MainActivity  redirectToProfileSelection #com.example.corredores.MainActivity  !requestIgnoreBatteryOptimizations #com.example.corredores.MainActivity  run #com.example.corredores.MainActivity  
runOnUiThread #com.example.corredores.MainActivity  sendImmediateLocationToServer #com.example.corredores.MainActivity  setContentView #com.example.corredores.MainActivity  setupChildView #com.example.corredores.MainActivity  setupParentView #com.example.corredores.MainActivity  setupWebViewClient #com.example.corredores.MainActivity  
startActivity #com.example.corredores.MainActivity  startService #com.example.corredores.MainActivity  startWebAuthenticationFlow #com.example.corredores.MainActivity  
trimIndent #com.example.corredores.MainActivity  DashboardHijoActivity /com.example.corredores.ProfileSelectionActivity  Intent /com.example.corredores.ProfileSelectionActivity  MainActivity /com.example.corredores.ProfileSelectionActivity  PreferenceManager /com.example.corredores.ProfileSelectionActivity  R /com.example.corredores.ProfileSelectionActivity  
TokenActivity /com.example.corredores.ProfileSelectionActivity  findViewById /com.example.corredores.ProfileSelectionActivity  finish /com.example.corredores.ProfileSelectionActivity  java /com.example.corredores.ProfileSelectionActivity  preferenceManager /com.example.corredores.ProfileSelectionActivity  redirectToAppropriateScreen /com.example.corredores.ProfileSelectionActivity  
selectProfile /com.example.corredores.ProfileSelectionActivity  setContentView /com.example.corredores.ProfileSelectionActivity  
setupViews /com.example.corredores.ProfileSelectionActivity  
startActivity /com.example.corredores.ProfileSelectionActivity  ic_launcher_foreground !com.example.corredores.R.drawable  	btn_child com.example.corredores.R.id  btn_connect com.example.corredores.R.id  	btn_login com.example.corredores.R.id  
btn_parent com.example.corredores.R.id  et_folio com.example.corredores.R.id  et_password com.example.corredores.R.id  et_token com.example.corredores.R.id  progress_bar com.example.corredores.R.id  webview_dashboard com.example.corredores.R.id  webview_dashboard_child com.example.corredores.R.id  activity_login com.example.corredores.R.layout  activity_main_child com.example.corredores.R.layout  activity_main_parent com.example.corredores.R.layout  activity_profile_selection com.example.corredores.R.layout  activity_splash com.example.corredores.R.layout  activity_token com.example.corredores.R.layout  allow com.example.corredores.R.string  error_permissions com.example.corredores.R.string  exit com.example.corredores.R.string  gps_disabled_message com.example.corredores.R.string  gps_disabled_title com.example.corredores.R.string  permission_background_message com.example.corredores.R.string  permission_background_title com.example.corredores.R.string  permission_location_message com.example.corredores.R.string  permission_location_title com.example.corredores.R.string  settings com.example.corredores.R.string  AlertDialog %com.example.corredores.SplashActivity  Handler %com.example.corredores.SplashActivity  Intent %com.example.corredores.SplashActivity  Looper %com.example.corredores.SplashActivity  MainActivity %com.example.corredores.SplashActivity  PackageManager %com.example.corredores.SplashActivity  PermissionHelper %com.example.corredores.SplashActivity  PreferenceManager %com.example.corredores.SplashActivity  ProfileSelectionActivity %com.example.corredores.SplashActivity  R %com.example.corredores.SplashActivity  Toast %com.example.corredores.SplashActivity  
TokenActivity %com.example.corredores.SplashActivity  all %com.example.corredores.SplashActivity  checkPermissions %com.example.corredores.SplashActivity  currentPermissionStep %com.example.corredores.SplashActivity  finish %com.example.corredores.SplashActivity  	getString %com.example.corredores.SplashActivity  hasBackgroundLocationPermission %com.example.corredores.SplashActivity  hasLocationPermissions %com.example.corredores.SplashActivity  isEmpty %com.example.corredores.SplashActivity  isGpsEnabled %com.example.corredores.SplashActivity  
isNotEmpty %com.example.corredores.SplashActivity  java %com.example.corredores.SplashActivity  navigateToNextScreen %com.example.corredores.SplashActivity  openAppSettings %com.example.corredores.SplashActivity  openLocationSettings %com.example.corredores.SplashActivity  preferenceManager %com.example.corredores.SplashActivity  #requestBackgroundLocationPermission %com.example.corredores.SplashActivity  requestLocationPermissions %com.example.corredores.SplashActivity  setContentView %com.example.corredores.SplashActivity  
showGpsDialog %com.example.corredores.SplashActivity  showPermissionDeniedDialog %com.example.corredores.SplashActivity  showPermissionDialog %com.example.corredores.SplashActivity  
startActivity %com.example.corredores.SplashActivity  Intent $com.example.corredores.TokenActivity  LocationService $com.example.corredores.TokenActivity  MainActivity $com.example.corredores.TokenActivity  
NetworkClient $com.example.corredores.TokenActivity  PreferenceManager $com.example.corredores.TokenActivity  R $com.example.corredores.TokenActivity  Toast $com.example.corredores.TokenActivity  TokenRequest $com.example.corredores.TokenActivity  View $com.example.corredores.TokenActivity  
btnConnect $com.example.corredores.TokenActivity  etToken $com.example.corredores.TokenActivity  findViewById $com.example.corredores.TokenActivity  finish $com.example.corredores.TokenActivity  	initViews $com.example.corredores.TokenActivity  isEmpty $com.example.corredores.TokenActivity  java $com.example.corredores.TokenActivity  launch $com.example.corredores.TokenActivity  let $com.example.corredores.TokenActivity  lifecycleScope $com.example.corredores.TokenActivity  performTokenLogin $com.example.corredores.TokenActivity  preferenceManager $com.example.corredores.TokenActivity  progressBar $com.example.corredores.TokenActivity  run $com.example.corredores.TokenActivity  setContentView $com.example.corredores.TokenActivity  setupClickListeners $com.example.corredores.TokenActivity  	showError $com.example.corredores.TokenActivity  showLoading $com.example.corredores.TokenActivity  
startActivity $com.example.corredores.TokenActivity  startService $com.example.corredores.TokenActivity  trim $com.example.corredores.TokenActivity  
NetworkClient &com.example.corredores.WebAppInterface  NotificationRequest &com.example.corredores.WebAppInterface  R &com.example.corredores.WebAppInterface  Toast &com.example.corredores.WebAppInterface  android &com.example.corredores.WebAppInterface  context &com.example.corredores.WebAppInterface  ifEmpty &com.example.corredores.WebAppInterface  launch &com.example.corredores.WebAppInterface  lifecycleScope &com.example.corredores.WebAppInterface  graphics com.example.corredores.android  location com.example.corredores.android  webkit com.example.corredores.android  Bitmap 'com.example.corredores.android.graphics  Location 'com.example.corredores.android.location  
CookieManager %com.example.corredores.android.webkit  GeolocationPermissions %com.example.corredores.android.webkit  WebChromeClient %com.example.corredores.android.webkit  WebResourceError %com.example.corredores.android.webkit  WebResourceRequest %com.example.corredores.android.webkit  Callback <com.example.corredores.android.webkit.GeolocationPermissions  google com.example.corredores.com  android !com.example.corredores.com.google  gms )com.example.corredores.com.google.android  location -com.example.corredores.com.google.android.gms  LocationCallback 6com.example.corredores.com.google.android.gms.location  LocationResult 6com.example.corredores.com.google.android.gms.location  Body com.example.corredores.models  Boolean com.example.corredores.models  Double com.example.corredores.models  Float com.example.corredores.models  GET com.example.corredores.models  GeofenceStatusResponse com.example.corredores.models  GeofenceZone com.example.corredores.models  Header com.example.corredores.models  Int com.example.corredores.models  List com.example.corredores.models  LocationRequestResponse com.example.corredores.models  LocationUpdateRequest com.example.corredores.models  LocationUpdateResponse com.example.corredores.models  LoginRequest com.example.corredores.models  
LoginResponse com.example.corredores.models  NotificationRequest com.example.corredores.models  NotificationResponse com.example.corredores.models  POST com.example.corredores.models  Query com.example.corredores.models  Response com.example.corredores.models  String com.example.corredores.models  TokenRequest com.example.corredores.models  
TokenResponse com.example.corredores.models  User com.example.corredores.models  WebSessionRequest com.example.corredores.models  WebSessionResponse com.example.corredores.models  status 5com.example.corredores.models.LocationRequestResponse  access_token +com.example.corredores.models.LoginResponse  user +com.example.corredores.models.LoginResponse  child_id +com.example.corredores.models.TokenResponse  message +com.example.corredores.models.TokenResponse  status +com.example.corredores.models.TokenResponse  folio "com.example.corredores.models.User  id "com.example.corredores.models.User  name "com.example.corredores.models.User  
dashboard_url 0com.example.corredores.models.WebSessionResponse  message 0com.example.corredores.models.WebSessionResponse  status 0com.example.corredores.models.WebSessionResponse  
ApiService com.example.corredores.network  Body com.example.corredores.network  GET com.example.corredores.network  GeofenceStatusResponse com.example.corredores.network  GsonConverterFactory com.example.corredores.network  Header com.example.corredores.network  HttpLoggingInterceptor com.example.corredores.network  Int com.example.corredores.network  LocationRequestResponse com.example.corredores.network  LocationUpdateRequest com.example.corredores.network  LocationUpdateResponse com.example.corredores.network  LoginRequest com.example.corredores.network  
LoginResponse com.example.corredores.network  
NetworkClient com.example.corredores.network  NotificationRequest com.example.corredores.network  NotificationResponse com.example.corredores.network  OkHttpClient com.example.corredores.network  POST com.example.corredores.network  Query com.example.corredores.network  Response com.example.corredores.network  Retrofit com.example.corredores.network  String com.example.corredores.network  TimeUnit com.example.corredores.network  TokenRequest com.example.corredores.network  
TokenResponse com.example.corredores.network  WebSessionRequest com.example.corredores.network  WebSessionResponse com.example.corredores.network  apply com.example.corredores.network  java com.example.corredores.network  checkPendingLocationRequests )com.example.corredores.network.ApiService  createWebSession )com.example.corredores.network.ApiService  
loginPadre )com.example.corredores.network.ApiService  registerConnection )com.example.corredores.network.ApiService  sendNotification )com.example.corredores.network.ApiService  updateLocation )com.example.corredores.network.ApiService  vincularDispositivo )com.example.corredores.network.ApiService  
ApiService ,com.example.corredores.network.NetworkClient  BASE_URL ,com.example.corredores.network.NetworkClient  GsonConverterFactory ,com.example.corredores.network.NetworkClient  HttpLoggingInterceptor ,com.example.corredores.network.NetworkClient  OkHttpClient ,com.example.corredores.network.NetworkClient  Retrofit ,com.example.corredores.network.NetworkClient  TimeUnit ,com.example.corredores.network.NetworkClient  
apiService ,com.example.corredores.network.NetworkClient  apply ,com.example.corredores.network.NetworkClient  java ,com.example.corredores.network.NetworkClient  loggingInterceptor ,com.example.corredores.network.NetworkClient  okHttpClient ,com.example.corredores.network.NetworkClient  retrofit ,com.example.corredores.network.NetworkClient  BootReceiver  com.example.corredores.receivers  BroadcastReceiver  com.example.corredores.receivers  Context  com.example.corredores.receivers  Intent  com.example.corredores.receivers  LocationService  com.example.corredores.receivers  Log  com.example.corredores.receivers  PreferenceManager  com.example.corredores.receivers  TAG  com.example.corredores.receivers  startService  com.example.corredores.receivers  Context -com.example.corredores.receivers.BootReceiver  Intent -com.example.corredores.receivers.BootReceiver  LocationService -com.example.corredores.receivers.BootReceiver  Log -com.example.corredores.receivers.BootReceiver  PreferenceManager -com.example.corredores.receivers.BootReceiver  TAG -com.example.corredores.receivers.BootReceiver  startService -com.example.corredores.receivers.BootReceiver  Intent 7com.example.corredores.receivers.BootReceiver.Companion  LocationService 7com.example.corredores.receivers.BootReceiver.Companion  Log 7com.example.corredores.receivers.BootReceiver.Companion  PreferenceManager 7com.example.corredores.receivers.BootReceiver.Companion  TAG 7com.example.corredores.receivers.BootReceiver.Companion  startService 7com.example.corredores.receivers.BootReceiver.Companion  ActivityCompat com.example.corredores.services  BatteryManager com.example.corredores.services  Boolean com.example.corredores.services  Build com.example.corredores.services  
CHANNEL_ID com.example.corredores.services  Context com.example.corredores.services  CoroutineScope com.example.corredores.services  Dispatchers com.example.corredores.services  	Exception com.example.corredores.services  FASTEST_UPDATE_INTERVAL com.example.corredores.services  FirebaseMessagingService com.example.corredores.services  FusedLocationProviderClient com.example.corredores.services  IBinder com.example.corredores.services  Int com.example.corredores.services  Intent com.example.corredores.services  IntentFilter com.example.corredores.services  Job com.example.corredores.services  LOCATION_UPDATE_INTERVAL com.example.corredores.services  Location com.example.corredores.services  LocationAvailability com.example.corredores.services  LocationCallback com.example.corredores.services  LocationRequest com.example.corredores.services  LocationResult com.example.corredores.services  LocationService com.example.corredores.services  LocationServices com.example.corredores.services  LocationUpdateRequest com.example.corredores.services  Log com.example.corredores.services  Looper com.example.corredores.services  MainActivity com.example.corredores.services  Manifest com.example.corredores.services  MyFirebaseMessagingService com.example.corredores.services  NOTIFICATION_ID com.example.corredores.services  
NetworkClient com.example.corredores.services  Notification com.example.corredores.services  NotificationChannel com.example.corredores.services  NotificationCompat com.example.corredores.services  NotificationManager com.example.corredores.services  PackageManager com.example.corredores.services  
PendingIntent com.example.corredores.services  PreferenceManager com.example.corredores.services  Priority com.example.corredores.services  R com.example.corredores.services  
RemoteMessage com.example.corredores.services  RingtoneManager com.example.corredores.services  START_NOT_STICKY com.example.corredores.services  START_STICKY com.example.corredores.services  Service com.example.corredores.services  String com.example.corredores.services  
SupervisorJob com.example.corredores.services  System com.example.corredores.services  TAG com.example.corredores.services  android com.example.corredores.services  apply com.example.corredores.services  cancel com.example.corredores.services  currentPollingInterval com.example.corredores.services  fusedLocationClient com.example.corredores.services  getLastKnownLocationImmediate com.example.corredores.services  
isNotEmpty com.example.corredores.services  
isNullOrEmpty com.example.corredores.services  java com.example.corredores.services  kotlinx com.example.corredores.services  lastUrgentRequest com.example.corredores.services  launch com.example.corredores.services  let com.example.corredores.services  longArrayOf com.example.corredores.services  preferenceManager com.example.corredores.services  requestImmediateLocationNow com.example.corredores.services  sendLocationToServer com.example.corredores.services  ActivityCompat /com.example.corredores.services.LocationService  BatteryManager /com.example.corredores.services.LocationService  Boolean /com.example.corredores.services.LocationService  Build /com.example.corredores.services.LocationService  
CHANNEL_ID /com.example.corredores.services.LocationService  	Companion /com.example.corredores.services.LocationService  Context /com.example.corredores.services.LocationService  CoroutineScope /com.example.corredores.services.LocationService  Dispatchers /com.example.corredores.services.LocationService  	Exception /com.example.corredores.services.LocationService  FASTEST_UPDATE_INTERVAL /com.example.corredores.services.LocationService  FusedLocationProviderClient /com.example.corredores.services.LocationService  IBinder /com.example.corredores.services.LocationService  Int /com.example.corredores.services.LocationService  Intent /com.example.corredores.services.LocationService  IntentFilter /com.example.corredores.services.LocationService  Job /com.example.corredores.services.LocationService  LOCATION_UPDATE_INTERVAL /com.example.corredores.services.LocationService  Location /com.example.corredores.services.LocationService  LocationAvailability /com.example.corredores.services.LocationService  LocationCallback /com.example.corredores.services.LocationService  LocationRequest /com.example.corredores.services.LocationService  LocationResult /com.example.corredores.services.LocationService  LocationService /com.example.corredores.services.LocationService  LocationServices /com.example.corredores.services.LocationService  LocationUpdateRequest /com.example.corredores.services.LocationService  Log /com.example.corredores.services.LocationService  Looper /com.example.corredores.services.LocationService  MainActivity /com.example.corredores.services.LocationService  Manifest /com.example.corredores.services.LocationService  NOTIFICATION_ID /com.example.corredores.services.LocationService  
NetworkClient /com.example.corredores.services.LocationService  Notification /com.example.corredores.services.LocationService  NotificationChannel /com.example.corredores.services.LocationService  NotificationCompat /com.example.corredores.services.LocationService  NotificationManager /com.example.corredores.services.LocationService  PackageManager /com.example.corredores.services.LocationService  
PendingIntent /com.example.corredores.services.LocationService  PreferenceManager /com.example.corredores.services.LocationService  Priority /com.example.corredores.services.LocationService  R /com.example.corredores.services.LocationService  START_NOT_STICKY /com.example.corredores.services.LocationService  START_STICKY /com.example.corredores.services.LocationService  
SupervisorJob /com.example.corredores.services.LocationService  System /com.example.corredores.services.LocationService  TAG /com.example.corredores.services.LocationService  apply /com.example.corredores.services.LocationService  cancel /com.example.corredores.services.LocationService  createLocationCallback /com.example.corredores.services.LocationService  createLocationRequest /com.example.corredores.services.LocationService  createNotification /com.example.corredores.services.LocationService  createNotificationChannel /com.example.corredores.services.LocationService  currentPollingInterval /com.example.corredores.services.LocationService  demandPollingJob /com.example.corredores.services.LocationService  fusedLocationClient /com.example.corredores.services.LocationService  getLastKnownLocationImmediate /com.example.corredores.services.LocationService  getSystemService /com.example.corredores.services.LocationService  java /com.example.corredores.services.LocationService  kotlinx /com.example.corredores.services.LocationService  lastUrgentRequest /com.example.corredores.services.LocationService  launch /com.example.corredores.services.LocationService  let /com.example.corredores.services.LocationService  locationCallback /com.example.corredores.services.LocationService  locationRequest /com.example.corredores.services.LocationService  preferenceManager /com.example.corredores.services.LocationService  registerConnectionWithServer /com.example.corredores.services.LocationService  registerReceiver /com.example.corredores.services.LocationService  requestImmediateLocationNow /com.example.corredores.services.LocationService  sendLocationToServer /com.example.corredores.services.LocationService  serviceScope /com.example.corredores.services.LocationService  startDemandPolling /com.example.corredores.services.LocationService  startForeground /com.example.corredores.services.LocationService  startLocationUpdates /com.example.corredores.services.LocationService  startService /com.example.corredores.services.LocationService  stopLocationUpdates /com.example.corredores.services.LocationService  stopSelf /com.example.corredores.services.LocationService  ActivityCompat 9com.example.corredores.services.LocationService.Companion  BatteryManager 9com.example.corredores.services.LocationService.Companion  Build 9com.example.corredores.services.LocationService.Companion  
CHANNEL_ID 9com.example.corredores.services.LocationService.Companion  CoroutineScope 9com.example.corredores.services.LocationService.Companion  Dispatchers 9com.example.corredores.services.LocationService.Companion  FASTEST_UPDATE_INTERVAL 9com.example.corredores.services.LocationService.Companion  Intent 9com.example.corredores.services.LocationService.Companion  IntentFilter 9com.example.corredores.services.LocationService.Companion  LOCATION_UPDATE_INTERVAL 9com.example.corredores.services.LocationService.Companion  LocationRequest 9com.example.corredores.services.LocationService.Companion  LocationService 9com.example.corredores.services.LocationService.Companion  LocationServices 9com.example.corredores.services.LocationService.Companion  LocationUpdateRequest 9com.example.corredores.services.LocationService.Companion  Log 9com.example.corredores.services.LocationService.Companion  Looper 9com.example.corredores.services.LocationService.Companion  MainActivity 9com.example.corredores.services.LocationService.Companion  Manifest 9com.example.corredores.services.LocationService.Companion  NOTIFICATION_ID 9com.example.corredores.services.LocationService.Companion  
NetworkClient 9com.example.corredores.services.LocationService.Companion  NotificationChannel 9com.example.corredores.services.LocationService.Companion  NotificationCompat 9com.example.corredores.services.LocationService.Companion  NotificationManager 9com.example.corredores.services.LocationService.Companion  PackageManager 9com.example.corredores.services.LocationService.Companion  
PendingIntent 9com.example.corredores.services.LocationService.Companion  PreferenceManager 9com.example.corredores.services.LocationService.Companion  Priority 9com.example.corredores.services.LocationService.Companion  R 9com.example.corredores.services.LocationService.Companion  START_NOT_STICKY 9com.example.corredores.services.LocationService.Companion  START_STICKY 9com.example.corredores.services.LocationService.Companion  
SupervisorJob 9com.example.corredores.services.LocationService.Companion  System 9com.example.corredores.services.LocationService.Companion  TAG 9com.example.corredores.services.LocationService.Companion  apply 9com.example.corredores.services.LocationService.Companion  cancel 9com.example.corredores.services.LocationService.Companion  currentPollingInterval 9com.example.corredores.services.LocationService.Companion  fusedLocationClient 9com.example.corredores.services.LocationService.Companion  getLastKnownLocationImmediate 9com.example.corredores.services.LocationService.Companion  java 9com.example.corredores.services.LocationService.Companion  kotlinx 9com.example.corredores.services.LocationService.Companion  lastUrgentRequest 9com.example.corredores.services.LocationService.Companion  launch 9com.example.corredores.services.LocationService.Companion  let 9com.example.corredores.services.LocationService.Companion  preferenceManager 9com.example.corredores.services.LocationService.Companion  requestImmediateLocationNow 9com.example.corredores.services.LocationService.Companion  sendLocationToServer 9com.example.corredores.services.LocationService.Companion  startService 9com.example.corredores.services.LocationService.Companion  Build :com.example.corredores.services.MyFirebaseMessagingService  
CHANNEL_ID :com.example.corredores.services.MyFirebaseMessagingService  Context :com.example.corredores.services.MyFirebaseMessagingService  Intent :com.example.corredores.services.MyFirebaseMessagingService  Log :com.example.corredores.services.MyFirebaseMessagingService  MainActivity :com.example.corredores.services.MyFirebaseMessagingService  NOTIFICATION_ID :com.example.corredores.services.MyFirebaseMessagingService  NotificationChannel :com.example.corredores.services.MyFirebaseMessagingService  NotificationCompat :com.example.corredores.services.MyFirebaseMessagingService  NotificationManager :com.example.corredores.services.MyFirebaseMessagingService  
PendingIntent :com.example.corredores.services.MyFirebaseMessagingService  PreferenceManager :com.example.corredores.services.MyFirebaseMessagingService  
RemoteMessage :com.example.corredores.services.MyFirebaseMessagingService  RingtoneManager :com.example.corredores.services.MyFirebaseMessagingService  String :com.example.corredores.services.MyFirebaseMessagingService  TAG :com.example.corredores.services.MyFirebaseMessagingService  android :com.example.corredores.services.MyFirebaseMessagingService  apply :com.example.corredores.services.MyFirebaseMessagingService  createNotificationChannel :com.example.corredores.services.MyFirebaseMessagingService  getSystemService :com.example.corredores.services.MyFirebaseMessagingService  
isNotEmpty :com.example.corredores.services.MyFirebaseMessagingService  
isNullOrEmpty :com.example.corredores.services.MyFirebaseMessagingService  java :com.example.corredores.services.MyFirebaseMessagingService  longArrayOf :com.example.corredores.services.MyFirebaseMessagingService  sendTokenToServer :com.example.corredores.services.MyFirebaseMessagingService  showNotification :com.example.corredores.services.MyFirebaseMessagingService  Build Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  
CHANNEL_ID Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  Context Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  Intent Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  Log Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  MainActivity Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  NOTIFICATION_ID Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  NotificationChannel Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  NotificationCompat Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  NotificationManager Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  
PendingIntent Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  PreferenceManager Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  RingtoneManager Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  TAG Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  android Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  apply Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  
isNotEmpty Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  
isNullOrEmpty Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  java Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  longArrayOf Dcom.example.corredores.services.MyFirebaseMessagingService.Companion  Activity com.example.corredores.utils  ActivityCompat com.example.corredores.utils  AlertDialog com.example.corredores.utils  BatteryOptimizationHelper com.example.corredores.utils  Boolean com.example.corredores.utils  Build com.example.corredores.utils  Call com.example.corredores.utils  Callback com.example.corredores.utils  Context com.example.corredores.utils  
ContextCompat com.example.corredores.utils  CoroutineScope com.example.corredores.utils  Dispatchers com.example.corredores.utils  	Exception com.example.corredores.utils  
FCMManager com.example.corredores.utils  FirebaseMessaging com.example.corredores.utils  IOException com.example.corredores.utils  Int com.example.corredores.utils  Intent com.example.corredores.utils  
JSONObject com.example.corredores.utils  KEY_AUTH_TOKEN com.example.corredores.utils  KEY_CHILD_ID com.example.corredores.utils  KEY_FCM_TOKEN_REGISTERED com.example.corredores.utils  KEY_IS_LOGGED_IN com.example.corredores.utils  KEY_LAST_TOKEN com.example.corredores.utils  KEY_USER_FOLIO com.example.corredores.utils  KEY_USER_ID com.example.corredores.utils  
KEY_USER_NAME com.example.corredores.utils  
KEY_USER_TYPE com.example.corredores.utils  LocationManager com.example.corredores.utils  Log com.example.corredores.utils  Manifest com.example.corredores.utils  OkHttpClient com.example.corredores.utils  	PREF_NAME com.example.corredores.utils  PackageManager com.example.corredores.utils  PermissionHelper com.example.corredores.utils  PowerManager com.example.corredores.utils  PreferenceManager com.example.corredores.utils  Request com.example.corredores.utils  Response com.example.corredores.utils  
SERVER_URL com.example.corredores.utils  Settings com.example.corredores.utils  SharedPreferences com.example.corredores.utils  String com.example.corredores.utils  TAG com.example.corredores.utils  Uri com.example.corredores.utils  all com.example.corredores.utils  apply com.example.corredores.utils  arrayOf com.example.corredores.utils  client com.example.corredores.utils  
isNullOrEmpty com.example.corredores.utils  launch com.example.corredores.utils  preferenceManager com.example.corredores.utils  toMediaType com.example.corredores.utils  
toRequestBody com.example.corredores.utils  use com.example.corredores.utils  AlertDialog 6com.example.corredores.utils.BatteryOptimizationHelper  Build 6com.example.corredores.utils.BatteryOptimizationHelper  Context 6com.example.corredores.utils.BatteryOptimizationHelper  Intent 6com.example.corredores.utils.BatteryOptimizationHelper  Settings 6com.example.corredores.utils.BatteryOptimizationHelper  Uri 6com.example.corredores.utils.BatteryOptimizationHelper  isIgnoringBatteryOptimizations 6com.example.corredores.utils.BatteryOptimizationHelper  openBatteryOptimizationSettings 6com.example.corredores.utils.BatteryOptimizationHelper  !requestIgnoreBatteryOptimizations 6com.example.corredores.utils.BatteryOptimizationHelper  showBatteryOptimizationDialog 6com.example.corredores.utils.BatteryOptimizationHelper  Call 'com.example.corredores.utils.FCMManager  Callback 'com.example.corredores.utils.FCMManager  Context 'com.example.corredores.utils.FCMManager  CoroutineScope 'com.example.corredores.utils.FCMManager  Dispatchers 'com.example.corredores.utils.FCMManager  	Exception 'com.example.corredores.utils.FCMManager  FirebaseMessaging 'com.example.corredores.utils.FCMManager  IOException 'com.example.corredores.utils.FCMManager  
JSONObject 'com.example.corredores.utils.FCMManager  Log 'com.example.corredores.utils.FCMManager  OkHttpClient 'com.example.corredores.utils.FCMManager  PreferenceManager 'com.example.corredores.utils.FCMManager  Request 'com.example.corredores.utils.FCMManager  Response 'com.example.corredores.utils.FCMManager  
SERVER_URL 'com.example.corredores.utils.FCMManager  String 'com.example.corredores.utils.FCMManager  TAG 'com.example.corredores.utils.FCMManager  apply 'com.example.corredores.utils.FCMManager  client 'com.example.corredores.utils.FCMManager  context 'com.example.corredores.utils.FCMManager  
isNullOrEmpty 'com.example.corredores.utils.FCMManager  launch 'com.example.corredores.utils.FCMManager  preferenceManager 'com.example.corredores.utils.FCMManager  registerTokenWithServer 'com.example.corredores.utils.FCMManager  toMediaType 'com.example.corredores.utils.FCMManager  
toRequestBody 'com.example.corredores.utils.FCMManager  unregisterToken 'com.example.corredores.utils.FCMManager  use 'com.example.corredores.utils.FCMManager  CoroutineScope 1com.example.corredores.utils.FCMManager.Companion  Dispatchers 1com.example.corredores.utils.FCMManager.Companion  FirebaseMessaging 1com.example.corredores.utils.FCMManager.Companion  
JSONObject 1com.example.corredores.utils.FCMManager.Companion  Log 1com.example.corredores.utils.FCMManager.Companion  OkHttpClient 1com.example.corredores.utils.FCMManager.Companion  PreferenceManager 1com.example.corredores.utils.FCMManager.Companion  Request 1com.example.corredores.utils.FCMManager.Companion  
SERVER_URL 1com.example.corredores.utils.FCMManager.Companion  TAG 1com.example.corredores.utils.FCMManager.Companion  apply 1com.example.corredores.utils.FCMManager.Companion  client 1com.example.corredores.utils.FCMManager.Companion  
isNullOrEmpty 1com.example.corredores.utils.FCMManager.Companion  launch 1com.example.corredores.utils.FCMManager.Companion  preferenceManager 1com.example.corredores.utils.FCMManager.Companion  toMediaType 1com.example.corredores.utils.FCMManager.Companion  
toRequestBody 1com.example.corredores.utils.FCMManager.Companion  use 1com.example.corredores.utils.FCMManager.Companion  ActivityCompat -com.example.corredores.utils.PermissionHelper  BACKGROUND_LOCATION_REQUEST -com.example.corredores.utils.PermissionHelper  Build -com.example.corredores.utils.PermissionHelper  Context -com.example.corredores.utils.PermissionHelper  
ContextCompat -com.example.corredores.utils.PermissionHelper  Intent -com.example.corredores.utils.PermissionHelper  LOCATION_PERMISSIONS -com.example.corredores.utils.PermissionHelper  LOCATION_PERMISSION_REQUEST -com.example.corredores.utils.PermissionHelper  LocationManager -com.example.corredores.utils.PermissionHelper  Manifest -com.example.corredores.utils.PermissionHelper  PackageManager -com.example.corredores.utils.PermissionHelper  Settings -com.example.corredores.utils.PermissionHelper  Uri -com.example.corredores.utils.PermissionHelper  all -com.example.corredores.utils.PermissionHelper  apply -com.example.corredores.utils.PermissionHelper  arrayOf -com.example.corredores.utils.PermissionHelper  hasBackgroundLocationPermission -com.example.corredores.utils.PermissionHelper  hasLocationPermissions -com.example.corredores.utils.PermissionHelper  isGpsEnabled -com.example.corredores.utils.PermissionHelper  openAppSettings -com.example.corredores.utils.PermissionHelper  openLocationSettings -com.example.corredores.utils.PermissionHelper  #requestBackgroundLocationPermission -com.example.corredores.utils.PermissionHelper  requestLocationPermissions -com.example.corredores.utils.PermissionHelper  Boolean .com.example.corredores.utils.PreferenceManager  	Companion .com.example.corredores.utils.PreferenceManager  Context .com.example.corredores.utils.PreferenceManager  Int .com.example.corredores.utils.PreferenceManager  KEY_AUTH_TOKEN .com.example.corredores.utils.PreferenceManager  KEY_CHILD_ID .com.example.corredores.utils.PreferenceManager  KEY_FCM_TOKEN_REGISTERED .com.example.corredores.utils.PreferenceManager  KEY_IS_LOGGED_IN .com.example.corredores.utils.PreferenceManager  KEY_LAST_TOKEN .com.example.corredores.utils.PreferenceManager  KEY_USER_FOLIO .com.example.corredores.utils.PreferenceManager  KEY_USER_ID .com.example.corredores.utils.PreferenceManager  
KEY_USER_NAME .com.example.corredores.utils.PreferenceManager  
KEY_USER_TYPE .com.example.corredores.utils.PreferenceManager  	PREF_NAME .com.example.corredores.utils.PreferenceManager  SharedPreferences .com.example.corredores.utils.PreferenceManager  String .com.example.corredores.utils.PreferenceManager  USER_TYPE_CHILD .com.example.corredores.utils.PreferenceManager  USER_TYPE_PARENT .com.example.corredores.utils.PreferenceManager  clearAll .com.example.corredores.utils.PreferenceManager  getAuthToken .com.example.corredores.utils.PreferenceManager  
getChildId .com.example.corredores.utils.PreferenceManager  getLastToken .com.example.corredores.utils.PreferenceManager  	getUserId .com.example.corredores.utils.PreferenceManager  getUserType .com.example.corredores.utils.PreferenceManager  hasSelectedProfile .com.example.corredores.utils.PreferenceManager  
isLoggedIn .com.example.corredores.utils.PreferenceManager  setAuthToken .com.example.corredores.utils.PreferenceManager  
setChildId .com.example.corredores.utils.PreferenceManager  setFCMTokenRegistered .com.example.corredores.utils.PreferenceManager  setLastToken .com.example.corredores.utils.PreferenceManager  setLoggedIn .com.example.corredores.utils.PreferenceManager  setUserData .com.example.corredores.utils.PreferenceManager  setUserType .com.example.corredores.utils.PreferenceManager  sharedPreferences .com.example.corredores.utils.PreferenceManager  Context 8com.example.corredores.utils.PreferenceManager.Companion  KEY_AUTH_TOKEN 8com.example.corredores.utils.PreferenceManager.Companion  KEY_CHILD_ID 8com.example.corredores.utils.PreferenceManager.Companion  KEY_FCM_TOKEN_REGISTERED 8com.example.corredores.utils.PreferenceManager.Companion  KEY_IS_LOGGED_IN 8com.example.corredores.utils.PreferenceManager.Companion  KEY_LAST_TOKEN 8com.example.corredores.utils.PreferenceManager.Companion  KEY_USER_FOLIO 8com.example.corredores.utils.PreferenceManager.Companion  KEY_USER_ID 8com.example.corredores.utils.PreferenceManager.Companion  
KEY_USER_NAME 8com.example.corredores.utils.PreferenceManager.Companion  
KEY_USER_TYPE 8com.example.corredores.utils.PreferenceManager.Companion  	PREF_NAME 8com.example.corredores.utils.PreferenceManager.Companion  USER_TYPE_CHILD 8com.example.corredores.utils.PreferenceManager.Companion  USER_TYPE_PARENT 8com.example.corredores.utils.PreferenceManager.Companion  ActivityCompat com.google.android.gms.location  BatteryManager com.google.android.gms.location  Boolean com.google.android.gms.location  Build com.google.android.gms.location  
CHANNEL_ID com.google.android.gms.location  Context com.google.android.gms.location  CoroutineScope com.google.android.gms.location  Dispatchers com.google.android.gms.location  	Exception com.google.android.gms.location  FASTEST_UPDATE_INTERVAL com.google.android.gms.location  FusedLocationProviderClient com.google.android.gms.location  IBinder com.google.android.gms.location  Int com.google.android.gms.location  Intent com.google.android.gms.location  IntentFilter com.google.android.gms.location  Job com.google.android.gms.location  LOCATION_UPDATE_INTERVAL com.google.android.gms.location  Location com.google.android.gms.location  LocationAvailability com.google.android.gms.location  LocationCallback com.google.android.gms.location  LocationRequest com.google.android.gms.location  LocationResult com.google.android.gms.location  LocationService com.google.android.gms.location  LocationServices com.google.android.gms.location  LocationUpdateRequest com.google.android.gms.location  Log com.google.android.gms.location  Looper com.google.android.gms.location  MainActivity com.google.android.gms.location  Manifest com.google.android.gms.location  NOTIFICATION_ID com.google.android.gms.location  
NetworkClient com.google.android.gms.location  Notification com.google.android.gms.location  NotificationChannel com.google.android.gms.location  NotificationCompat com.google.android.gms.location  NotificationManager com.google.android.gms.location  PackageManager com.google.android.gms.location  
PendingIntent com.google.android.gms.location  PreferenceManager com.google.android.gms.location  Priority com.google.android.gms.location  R com.google.android.gms.location  START_NOT_STICKY com.google.android.gms.location  START_STICKY com.google.android.gms.location  Service com.google.android.gms.location  
SupervisorJob com.google.android.gms.location  System com.google.android.gms.location  TAG com.google.android.gms.location  apply com.google.android.gms.location  cancel com.google.android.gms.location  currentPollingInterval com.google.android.gms.location  fusedLocationClient com.google.android.gms.location  getLastKnownLocationImmediate com.google.android.gms.location  java com.google.android.gms.location  kotlinx com.google.android.gms.location  lastUrgentRequest com.google.android.gms.location  launch com.google.android.gms.location  let com.google.android.gms.location  preferenceManager com.google.android.gms.location  requestImmediateLocationNow com.google.android.gms.location  sendLocationToServer com.google.android.gms.location  lastLocation ;com.google.android.gms.location.FusedLocationProviderClient  removeLocationUpdates ;com.google.android.gms.location.FusedLocationProviderClient  requestLocationUpdates ;com.google.android.gms.location.FusedLocationProviderClient  isLocationAvailable 4com.google.android.gms.location.LocationAvailability  Log 0com.google.android.gms.location.LocationCallback  TAG 0com.google.android.gms.location.LocationCallback  android 0com.google.android.gms.location.LocationCallback  fusedLocationClient 0com.google.android.gms.location.LocationCallback  getLastKnownLocationImmediate 0com.google.android.gms.location.LocationCallback  let 0com.google.android.gms.location.LocationCallback  onLocationAvailability 0com.google.android.gms.location.LocationCallback  onLocationResult 0com.google.android.gms.location.LocationCallback  sendImmediateLocationToServer 0com.google.android.gms.location.LocationCallback  sendLocationToServer 0com.google.android.gms.location.LocationCallback  Builder /com.google.android.gms.location.LocationRequest  FASTEST_UPDATE_INTERVAL 7com.google.android.gms.location.LocationRequest.Builder  LOCATION_UPDATE_INTERVAL 7com.google.android.gms.location.LocationRequest.Builder  apply 7com.google.android.gms.location.LocationRequest.Builder  build 7com.google.android.gms.location.LocationRequest.Builder  setMaxUpdateDelayMillis 7com.google.android.gms.location.LocationRequest.Builder  setMinUpdateIntervalMillis 7com.google.android.gms.location.LocationRequest.Builder  setWaitForAccurateLocation 7com.google.android.gms.location.LocationRequest.Builder  lastLocation .com.google.android.gms.location.LocationResult  getFusedLocationProviderClient 0com.google.android.gms.location.LocationServices  PRIORITY_HIGH_ACCURACY (com.google.android.gms.location.Priority  OnCompleteListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> /com.google.android.gms.tasks.OnCompleteListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnCompleteListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  	exception !com.google.android.gms.tasks.Task  isSuccessful !com.google.android.gms.tasks.Task  result !com.google.android.gms.tasks.Task  FirebaseMessaging com.google.firebase.messaging  FirebaseMessagingService com.google.firebase.messaging  
RemoteMessage com.google.firebase.messaging  Build 3com.google.firebase.messaging.EnhancedIntentService  
CHANNEL_ID 3com.google.firebase.messaging.EnhancedIntentService  Context 3com.google.firebase.messaging.EnhancedIntentService  Intent 3com.google.firebase.messaging.EnhancedIntentService  Log 3com.google.firebase.messaging.EnhancedIntentService  MainActivity 3com.google.firebase.messaging.EnhancedIntentService  NOTIFICATION_ID 3com.google.firebase.messaging.EnhancedIntentService  NotificationChannel 3com.google.firebase.messaging.EnhancedIntentService  NotificationCompat 3com.google.firebase.messaging.EnhancedIntentService  NotificationManager 3com.google.firebase.messaging.EnhancedIntentService  
PendingIntent 3com.google.firebase.messaging.EnhancedIntentService  PreferenceManager 3com.google.firebase.messaging.EnhancedIntentService  RingtoneManager 3com.google.firebase.messaging.EnhancedIntentService  TAG 3com.google.firebase.messaging.EnhancedIntentService  android 3com.google.firebase.messaging.EnhancedIntentService  apply 3com.google.firebase.messaging.EnhancedIntentService  
isNotEmpty 3com.google.firebase.messaging.EnhancedIntentService  
isNullOrEmpty 3com.google.firebase.messaging.EnhancedIntentService  java 3com.google.firebase.messaging.EnhancedIntentService  longArrayOf 3com.google.firebase.messaging.EnhancedIntentService  getInstance /com.google.firebase.messaging.FirebaseMessaging  token /com.google.firebase.messaging.FirebaseMessaging  Build 6com.google.firebase.messaging.FirebaseMessagingService  
CHANNEL_ID 6com.google.firebase.messaging.FirebaseMessagingService  Context 6com.google.firebase.messaging.FirebaseMessagingService  Intent 6com.google.firebase.messaging.FirebaseMessagingService  Log 6com.google.firebase.messaging.FirebaseMessagingService  MainActivity 6com.google.firebase.messaging.FirebaseMessagingService  NOTIFICATION_ID 6com.google.firebase.messaging.FirebaseMessagingService  NotificationChannel 6com.google.firebase.messaging.FirebaseMessagingService  NotificationCompat 6com.google.firebase.messaging.FirebaseMessagingService  NotificationManager 6com.google.firebase.messaging.FirebaseMessagingService  
PendingIntent 6com.google.firebase.messaging.FirebaseMessagingService  PreferenceManager 6com.google.firebase.messaging.FirebaseMessagingService  RingtoneManager 6com.google.firebase.messaging.FirebaseMessagingService  TAG 6com.google.firebase.messaging.FirebaseMessagingService  android 6com.google.firebase.messaging.FirebaseMessagingService  apply 6com.google.firebase.messaging.FirebaseMessagingService  
isNotEmpty 6com.google.firebase.messaging.FirebaseMessagingService  
isNullOrEmpty 6com.google.firebase.messaging.FirebaseMessagingService  java 6com.google.firebase.messaging.FirebaseMessagingService  longArrayOf 6com.google.firebase.messaging.FirebaseMessagingService  onMessageReceived 6com.google.firebase.messaging.FirebaseMessagingService  
onNewToken 6com.google.firebase.messaging.FirebaseMessagingService  Notification +com.google.firebase.messaging.RemoteMessage  data +com.google.firebase.messaging.RemoteMessage  from +com.google.firebase.messaging.RemoteMessage  notification +com.google.firebase.messaging.RemoteMessage  body 8com.google.firebase.messaging.RemoteMessage.Notification  title 8com.google.firebase.messaging.RemoteMessage.Notification  IOException java.io  Class 	java.lang  	Exception 	java.lang  Runnable 	java.lang  message java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  ofEpochMilli java.time.Instant  toString java.time.Instant  Date 	java.util  
getDefault java.util.Locale  TimeUnit java.util.concurrent  SECONDS java.util.concurrent.TimeUnit  Array kotlin  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  apply kotlin  arrayOf kotlin  let kotlin  longArrayOf kotlin  run kotlin  use kotlin  toString 
kotlin.Any  all kotlin.Array  not kotlin.Boolean  isEmpty kotlin.CharSequence  invoke kotlin.Function0  	compareTo 
kotlin.Int  div 
kotlin.Int  let 
kotlin.Int  or 
kotlin.Int  times 
kotlin.Int  toString 
kotlin.Int  all kotlin.IntArray  get kotlin.IntArray  isEmpty kotlin.IntArray  
isNotEmpty kotlin.IntArray  	compareTo kotlin.Long  minus kotlin.Long  times kotlin.Long  contains 
kotlin.String  ifEmpty 
kotlin.String  isEmpty 
kotlin.String  
isNotEmpty 
kotlin.String  
isNullOrEmpty 
kotlin.String  length 
kotlin.String  let 
kotlin.String  toMediaType 
kotlin.String  
toRequestBody 
kotlin.String  trim 
kotlin.String  
trimIndent 
kotlin.String  message kotlin.Throwable  List kotlin.collections  all kotlin.collections  contains kotlin.collections  ifEmpty kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  get kotlin.collections.MutableMap  CoroutineContext kotlin.coroutines  SuspendFunction1 kotlin.coroutines  plus "kotlin.coroutines.CoroutineContext  use 	kotlin.io  java 
kotlin.jvm  contains 
kotlin.ranges  java kotlin.reflect.KClass  Sequence kotlin.sequences  all kotlin.sequences  contains kotlin.sequences  ifEmpty kotlin.sequences  all kotlin.text  contains kotlin.text  ifEmpty kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  trim kotlin.text  
trimIndent kotlin.text  ActivityCompat kotlinx.coroutines  BatteryManager kotlinx.coroutines  Boolean kotlinx.coroutines  Build kotlinx.coroutines  
CHANNEL_ID kotlinx.coroutines  CompletableJob kotlinx.coroutines  Context kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  	Exception kotlinx.coroutines  FASTEST_UPDATE_INTERVAL kotlinx.coroutines  FusedLocationProviderClient kotlinx.coroutines  IBinder kotlinx.coroutines  Int kotlinx.coroutines  Intent kotlinx.coroutines  IntentFilter kotlinx.coroutines  Job kotlinx.coroutines  LOCATION_UPDATE_INTERVAL kotlinx.coroutines  Location kotlinx.coroutines  LocationAvailability kotlinx.coroutines  LocationCallback kotlinx.coroutines  LocationRequest kotlinx.coroutines  LocationResult kotlinx.coroutines  LocationService kotlinx.coroutines  LocationServices kotlinx.coroutines  LocationUpdateRequest kotlinx.coroutines  Log kotlinx.coroutines  Looper kotlinx.coroutines  MainActivity kotlinx.coroutines  Manifest kotlinx.coroutines  NOTIFICATION_ID kotlinx.coroutines  
NetworkClient kotlinx.coroutines  Notification kotlinx.coroutines  NotificationChannel kotlinx.coroutines  NotificationCompat kotlinx.coroutines  NotificationManager kotlinx.coroutines  PackageManager kotlinx.coroutines  
PendingIntent kotlinx.coroutines  PreferenceManager kotlinx.coroutines  Priority kotlinx.coroutines  R kotlinx.coroutines  START_NOT_STICKY kotlinx.coroutines  START_STICKY kotlinx.coroutines  Service kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  System kotlinx.coroutines  TAG kotlinx.coroutines  apply kotlinx.coroutines  cancel kotlinx.coroutines  currentPollingInterval kotlinx.coroutines  delay kotlinx.coroutines  fusedLocationClient kotlinx.coroutines  getLastKnownLocationImmediate kotlinx.coroutines  java kotlinx.coroutines  kotlinx kotlinx.coroutines  lastUrgentRequest kotlinx.coroutines  launch kotlinx.coroutines  let kotlinx.coroutines  preferenceManager kotlinx.coroutines  requestImmediateLocationNow kotlinx.coroutines  sendLocationToServer kotlinx.coroutines  plus &kotlinx.coroutines.CoroutineDispatcher  Intent !kotlinx.coroutines.CoroutineScope  
JSONObject !kotlinx.coroutines.CoroutineScope  LocationService !kotlinx.coroutines.CoroutineScope  LocationUpdateRequest !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  LoginRequest !kotlinx.coroutines.CoroutineScope  MainActivity !kotlinx.coroutines.CoroutineScope  
NetworkClient !kotlinx.coroutines.CoroutineScope  NotificationRequest !kotlinx.coroutines.CoroutineScope  R !kotlinx.coroutines.CoroutineScope  Request !kotlinx.coroutines.CoroutineScope  
SERVER_URL !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  Toast !kotlinx.coroutines.CoroutineScope  TokenRequest !kotlinx.coroutines.CoroutineScope  WebSessionRequest !kotlinx.coroutines.CoroutineScope  android !kotlinx.coroutines.CoroutineScope  apply !kotlinx.coroutines.CoroutineScope  cancel !kotlinx.coroutines.CoroutineScope  client !kotlinx.coroutines.CoroutineScope  com !kotlinx.coroutines.CoroutineScope  context !kotlinx.coroutines.CoroutineScope   createWebSessionAndLoadDashboard !kotlinx.coroutines.CoroutineScope  currentPollingInterval !kotlinx.coroutines.CoroutineScope  finish !kotlinx.coroutines.CoroutineScope  fusedLocationClient !kotlinx.coroutines.CoroutineScope  getLastKnownLocationImmediate !kotlinx.coroutines.CoroutineScope  ifEmpty !kotlinx.coroutines.CoroutineScope  java !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  lastUrgentRequest !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  preferenceManager !kotlinx.coroutines.CoroutineScope  requestImmediateLocationNow !kotlinx.coroutines.CoroutineScope  run !kotlinx.coroutines.CoroutineScope  sendImmediateLocationToServer !kotlinx.coroutines.CoroutineScope  	showError !kotlinx.coroutines.CoroutineScope  showLoading !kotlinx.coroutines.CoroutineScope  
startActivity !kotlinx.coroutines.CoroutineScope  startService !kotlinx.coroutines.CoroutineScope  toMediaType !kotlinx.coroutines.CoroutineScope  
toRequestBody !kotlinx.coroutines.CoroutineScope  use !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  cancel kotlinx.coroutines.Job  Call okhttp3  Callback okhttp3  Context okhttp3  CoroutineScope okhttp3  Dispatchers okhttp3  	Exception okhttp3  FirebaseMessaging okhttp3  IOException okhttp3  
JSONObject okhttp3  Log okhttp3  	MediaType okhttp3  OkHttpClient okhttp3  PreferenceManager okhttp3  Request okhttp3  RequestBody okhttp3  Response okhttp3  
SERVER_URL okhttp3  String okhttp3  TAG okhttp3  apply okhttp3  client okhttp3  
isNullOrEmpty okhttp3  launch okhttp3  preferenceManager okhttp3  toMediaType okhttp3  
toRequestBody okhttp3  use okhttp3  enqueue okhttp3.Call  toMediaType okhttp3.MediaType.Companion  Builder okhttp3.OkHttpClient  	Companion okhttp3.OkHttpClient  newCall okhttp3.OkHttpClient  addInterceptor okhttp3.OkHttpClient.Builder  build okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  writeTimeout okhttp3.OkHttpClient.Builder  Builder okhttp3.Request  	addHeader okhttp3.Request.Builder  build okhttp3.Request.Builder  delete okhttp3.Request.Builder  post okhttp3.Request.Builder  url okhttp3.Request.Builder  
toRequestBody okhttp3.RequestBody.Companion  code okhttp3.Response  isSuccessful okhttp3.Response  use okhttp3.Response  HttpLoggingInterceptor okhttp3.logging  HttpLoggingInterceptor &okhttp3.logging.HttpLoggingInterceptor  Level &okhttp3.logging.HttpLoggingInterceptor  apply &okhttp3.logging.HttpLoggingInterceptor  level &okhttp3.logging.HttpLoggingInterceptor  BODY ,okhttp3.logging.HttpLoggingInterceptor.Level  
JSONObject org.json  apply org.json.JSONObject  put org.json.JSONObject  toString org.json.JSONObject  Response 	retrofit2  Retrofit 	retrofit2  body retrofit2.Response  code retrofit2.Response  isSuccessful retrofit2.Response  Builder retrofit2.Retrofit  create retrofit2.Retrofit  addConverterFactory retrofit2.Retrofit.Builder  baseUrl retrofit2.Retrofit.Builder  build retrofit2.Retrofit.Builder  client retrofit2.Retrofit.Builder  GsonConverterFactory retrofit2.converter.gson  create -retrofit2.converter.gson.GsonConverterFactory  Body retrofit2.http  GET retrofit2.http  GeofenceStatusResponse retrofit2.http  Header retrofit2.http  Int retrofit2.http  LocationRequestResponse retrofit2.http  LocationUpdateRequest retrofit2.http  LocationUpdateResponse retrofit2.http  LoginRequest retrofit2.http  
LoginResponse retrofit2.http  NotificationRequest retrofit2.http  NotificationResponse retrofit2.http  POST retrofit2.http  Query retrofit2.http  Response retrofit2.http  String retrofit2.http  TokenRequest retrofit2.http  
TokenResponse retrofit2.http  WebSessionRequest retrofit2.http  WebSessionResponse retrofit2.http                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                