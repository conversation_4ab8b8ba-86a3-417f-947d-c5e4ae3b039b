package com.example.corredores.utils

import android.content.Context
import android.content.SharedPreferences

class PreferenceManager(context: Context) {

    companion object {
        private const val PREF_NAME = "corredores_prefs"
        private const val KEY_USER_TYPE = "user_type"
        private const val KEY_IS_LOGGED_IN = "is_logged_in"
        private const val KEY_AUTH_TOKEN = "auth_token"
        private const val KEY_USER_ID = "user_id"
        private const val KEY_USER_FOLIO = "user_folio"
        private const val KEY_USER_NAME = "user_name"
        private const val KEY_CHILD_ID = "child_id"
        private const val KEY_LAST_TOKEN = "last_token"
        private const val KEY_FCM_TOKEN_REGISTERED = "fcm_token_registered"

        const val USER_TYPE_PARENT = "parent"
        const val USER_TYPE_CHILD = "child"
    }

    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)

    // Tipo de usuario
    fun setUserType(userType: String) {
        sharedPreferences.edit().putString(KEY_USER_TYPE, userType).apply()
    }

    fun getUserType(): String? {
        return sharedPreferences.getString(KEY_USER_TYPE, null)
    }

    // Estado de login
    fun setLoggedIn(isLoggedIn: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_IS_LOGGED_IN, isLoggedIn).apply()
    }

    fun isLoggedIn(): Boolean {
        return sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false)
    }

    // Token de autenticación (para padres)
    fun setAuthToken(token: String) {
        sharedPreferences.edit().putString(KEY_AUTH_TOKEN, token).apply()
    }

    fun getAuthToken(): String? {
        return sharedPreferences.getString(KEY_AUTH_TOKEN, null)
    }

    // Datos del usuario padre
    fun setUserData(userId: Int, folio: String, name: String?) {
        sharedPreferences.edit()
            .putInt(KEY_USER_ID, userId)
            .putString(KEY_USER_FOLIO, folio)
            .putString(KEY_USER_NAME, name)
            .apply()
    }

    fun getUserId(): Int {
        return sharedPreferences.getInt(KEY_USER_ID, -1)
    }

    fun getUserFolio(): String? {
        return sharedPreferences.getString(KEY_USER_FOLIO, null)
    }

    fun getUserName(): String? {
        return sharedPreferences.getString(KEY_USER_NAME, null)
    }

    // ID del hijo
    fun setChildId(childId: Int) {
        sharedPreferences.edit().putInt(KEY_CHILD_ID, childId).apply()
    }

    fun getChildId(): Int {
        return sharedPreferences.getInt(KEY_CHILD_ID, -1)
    }

    // Último token usado (para reautenticación web)
    fun setLastToken(token: String) {
        sharedPreferences.edit().putString(KEY_LAST_TOKEN, token).apply()
    }

    fun getLastToken(): String? {
        return sharedPreferences.getString(KEY_LAST_TOKEN, null)
    }

    // Verificaciones de estado
    fun hasSelectedProfile(): Boolean {
        return getUserType() != null
    }

    // Métodos para FCM
    fun setFCMTokenRegistered(registered: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_FCM_TOKEN_REGISTERED, registered).apply()
    }

    fun isFCMTokenRegistered(): Boolean {
        return sharedPreferences.getBoolean(KEY_FCM_TOKEN_REGISTERED, false)
    }

    // Limpiar todos los datos
    fun clearAll() {
        sharedPreferences.edit().clear().apply()
    }
}
