package com.example.corredoresseguroshijo // Asegúrate que coincida con tu package name real

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.util.Log // Para logging (mensajes en Logcat)
import android.view.View
import android.view.inputmethod.InputMethodManager // Para ocultar teclado
import android.widget.Button
import android.widget.EditText
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast // Para mensajes flotantes rápidos al usuario
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.google.gson.Gson // Importar Gson explícitamente para parsear errores
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
// Importaciones centralizadas desde el paquete 'network'
import com.example.corredoresseguroshijo.network.ApiService         // Interfaz centralizada
import com.example.corredoresseguroshijo.network.RetrofitClient     // Cliente Retrofit centralizado
import com.example.corredoresseguroshijo.network.LinkRequest        // DTOs desde paquete network (CORRECTO)
import com.example.corredoresseguroshijo.network.LinkResponse       // DTOs desde paquete network (CORRECTO)
import com.example.corredoresseguroshijo.network.ApiErrorResponse   // DTOs desde paquete network (CORRECTO)

import java.io.IOException // Para capturar errores de red específicos


// --- MainActivity: La pantalla de vinculación ---
class MainActivity : AppCompatActivity() {

    // Tag para identificar los logs de esta actividad
    private val TAG = "MainActivity"

    // Constantes para SharedPreferences (almacenamiento local simple)
    private val PREFS_NAME = "CorredoresHijoPrefs" // Nombre del archivo de preferencias
    private val CHILD_ID_KEY = "child_id"         // Clave para guardar/leer el ID del hijo (CORRECTO)

    // Constantes para permisos
    private val LOCATION_PERMISSION_REQUEST_CODE = 1001
    private val BACKGROUND_LOCATION_PERMISSION_REQUEST_CODE = 1002

    // Permisos requeridos
    private val REQUIRED_PERMISSIONS = arrayOf(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION,
        Manifest.permission.POST_NOTIFICATIONS
    )

    // Referencias a las Vistas del layout (se inicializarán más tarde usando lateinit)
    private lateinit var editTextToken: EditText
    private lateinit var buttonLink: Button
    private lateinit var progressBar: ProgressBar
    private lateinit var textViewError: TextView
    private lateinit var sharedPreferences: SharedPreferences // Para guardar/leer el child_id


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main) // Carga el diseño XML definido en activity_main.xml

        Log.d(TAG, "onCreate iniciado") // Mensaje para depuración

        // Inicializar SharedPreferences para poder leer/escribir datos locales
        sharedPreferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

        // --- Solicitar permisos necesarios ---
        if (!hasAllRequiredPermissions()) {
            requestRequiredPermissions()
        } else {
            // Si ya tenemos permisos, verificar vinculación
            checkDeviceLinkingStatus()
        }

        // Obtener referencias a las vistas del layout usando sus IDs
        initializeViews()

        // Configurar el listener para el clic del botón "Vincular"
        setupLinkButton()
    }

    /** Verifica el estado de vinculación del dispositivo */
    private fun checkDeviceLinkingStatus() {
        val savedChildId = getSavedChildId()
        if (savedChildId != -1L) { // -1L indica que no hay ID guardado
            Log.i(TAG, "Dispositivo ya vinculado con Child ID: $savedChildId. Navegando a Dashboard.")
            navigateToDashboard(savedChildId) // Ir directamente al dashboard
            // finish() // Descomentar si quieres que el usuario no pueda volver a esta pantalla
        } else {
            Log.d(TAG, "Dispositivo NO vinculado. Configurando UI de vinculación.")
        }
    }

    /** Inicializa las referencias a las vistas del layout. */
    private fun initializeViews() {
        editTextToken = findViewById(R.id.editTextToken)
        buttonLink = findViewById(R.id.buttonLink)
        progressBar = findViewById(R.id.progressBar)
        textViewError = findViewById(R.id.textViewError)
    }

    /** Configura el OnClickListener para el botón de vinculación. */
    private fun setupLinkButton() {
        buttonLink.setOnClickListener {
            Log.d(TAG, "Botón Vincular clickeado")
            hideKeyboard() // Buena práctica: ocultar teclado
            val token = editTextToken.text.toString().trim() // Obtener y limpiar el token ingresado
            if (token.isNotEmpty()) {
                attemptLinkDevice(token) // Iniciar la llamada a la API si hay token
            } else {
                showError(getString(R.string.error_missing_token)) // Mostrar error si no ingresó token
                Log.w(TAG, "Intento de vincular con token vacío.")
            }
        }
    }


    /**
     * Inicia la llamada a la API (usando Retrofit) para intentar vincular
     * el dispositivo con el token proporcionado por el usuario.
     */
    private fun attemptLinkDevice(token: String) {
        Log.d(TAG, "Intentando vincular con token: $token")
        showLoading(true) // Mostrar UI de carga

        // Crear el cuerpo de la solicitud usando la data class correcta
        val request = LinkRequest(token = token) // Usa DTO importado

        // Usar el cliente Retrofit centralizado para obtener la instancia de ApiService
        // y realizar la llamada al endpoint linkDevice.
        RetrofitClient.instance.linkDevice(request).enqueue(object : Callback<LinkResponse> { // Usa DTO importado

            // Callback: se ejecuta cuando se recibe una respuesta HTTP del servidor.
            override fun onResponse(call: Call<LinkResponse>, response: Response<LinkResponse>) { // Usa DTO importado
                showLoading(false) // Ocultar UI de carga

                if (response.isSuccessful && response.body() != null) {
                    // Respuesta HTTP exitosa (código 2xx) y con cuerpo JSON válido.
                    handleSuccessfulLinkResponse(response.body()!!) // Procesar la respuesta exitosa
                } else {
                    // Respuesta HTTP con error (código 4xx o 5xx) o cuerpo vacío inesperado.
                    val errorCode = response.code()
                    val errorBodyString = response.errorBody()?.string() // Intentar leer el cuerpo del error
                    Log.e(TAG, "Error HTTP al vincular: $errorCode - Cuerpo: $errorBodyString")
                    handleApiHttpError(errorCode, errorBodyString) // Interpretar y mostrar el error
                }
            }

            // Callback: se ejecuta si falla la conexión de red o hay un problema al procesar
            // la solicitud/respuesta (ej. error de parseo JSON, timeout).
            override fun onFailure(call: Call<LinkResponse>, t: Throwable) { // Usa DTO importado
                showLoading(false) // Ocultar UI de carga
                Log.e(TAG, "Fallo en llamada API de vinculación: ${t.message}", t)
                handleNetworkFailure(t) // Interpretar y mostrar el error de red/procesamiento
            }
        })
    }

    /** Procesa una respuesta exitosa (lógica) del endpoint de vinculación. */
    private fun handleSuccessfulLinkResponse(linkResponse: LinkResponse) { // Usa DTO importado
        if (linkResponse.status == "success" && linkResponse.child_id != null) {
            // El servidor confirma que la vinculación fue exitosa y devuelve el ID.
            Log.i(TAG, "Vinculación API exitosa. Child ID: ${linkResponse.child_id}, Mensaje: ${linkResponse.message}")
            saveChildId(linkResponse.child_id) // Guardar el ID para futuras sesiones
            Toast.makeText(this@MainActivity, R.string.link_success, Toast.LENGTH_LONG).show()
            navigateToDashboard(linkResponse.child_id) // Ir a la pantalla principal del hijo
            // finish() // Opcional: cerrar esta pantalla
        } else {
            // El servidor respondió con 2xx, pero indicó un error lógico en el JSON.
            Log.w(TAG, "Respuesta API indica error lógico de vinculación: ${linkResponse.message}")
            showError(linkResponse.message ?: getString(R.string.error_link_api_generic)) // Usar mensaje del servidor o uno genérico
        }
    }

    // --- Funciones Auxiliares de UI y Datos ---

    /** Muestra u oculta el ProgressBar y gestiona el estado de habilitación de la UI. */
    private fun showLoading(isLoading: Boolean) {
        progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        textViewError.visibility = View.GONE // Ocultar errores mientras carga
        buttonLink.isEnabled = !isLoading
        editTextToken.isEnabled = !isLoading
        buttonLink.text = if (isLoading) getString(R.string.linking_in_progress) else getString(R.string.link_button_text)
    }

    /** Muestra un mensaje de error en el TextView de errores. */
    private fun showError(message: String?) {
        // Asegurarse de mostrar un mensaje incluso si es nulo
        textViewError.text = message ?: getString(R.string.error_generic)
        textViewError.visibility = View.VISIBLE
    }

    /** Interpreta errores HTTP (4xx, 5xx) e intenta extraer un mensaje del cuerpo del error. */
    private fun handleApiHttpError(httpCode: Int, errorBody: String?) {
        val defaultError = getString(R.string.error_generic)
        var displayMessage = defaultError

        // Intentar parsear el cuerpo del error como JSON
        if (errorBody != null) {
            try {
                val gson = Gson()
                // Usar la data class ApiErrorResponse centralizada
                val errorResponse = gson.fromJson(errorBody, ApiErrorResponse::class.java) // Usa DTO importado
                // Usar el mensaje del JSON de error si existe
                displayMessage = errorResponse?.message ?: displayMessage
            } catch (e: Exception) {
                Log.w(TAG, "No se pudo parsear el cuerpo del error JSON: $errorBody", e)
            }
        }

        // Mostrar mensajes más específicos basados en códigos HTTP si no se obtuvo un mensaje del cuerpo
        if (displayMessage == defaultError) { // Solo si no pudimos extraer un mensaje mejor
            when (httpCode) {
                404 -> displayMessage = getString(R.string.error_invalid_token) // Token no encontrado
                410 -> displayMessage = getString(R.string.error_already_used) // Token usado/expirado
            }
        }
        showError(displayMessage)
    }

    /** Maneja fallos de red o procesamiento en la llamada Retrofit. */
    private fun handleNetworkFailure(t: Throwable) {
        val errorMessage = if (t is IOException) {
            getString(R.string.error_network)
        } else {
            getString(R.string.error_generic)
        }
        showError(errorMessage)
    }


    /** Guarda el child_id en SharedPreferences para persistencia. */
    private fun saveChildId(childId: Long) {
        Log.d(TAG, "Guardando Child ID: $childId en SharedPreferences")
        try {
            with(sharedPreferences.edit()) {
                putLong(CHILD_ID_KEY, childId)
                apply()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error al guardar Child ID en SharedPreferences", e)
        }
    }

    /** Lee el child_id guardado de SharedPreferences. Devuelve -1L si no se encuentra. */
     private fun getSavedChildId(): Long {
         // CORRECCIÓN APLICADA: Usar CHILD_ID_KEY
         val savedId = sharedPreferences.getLong(CHILD_ID_KEY, -1L)
         Log.d(TAG, "Leyendo Child ID de SharedPreferences: $savedId")
         return savedId
     }

    /** Inicia la DashboardActivity pasando el child_id. */
    private fun navigateToDashboard(childId: Long) {
        Log.i(TAG, "Navegando a DashboardActivity con Child ID: $childId")
        val intent = Intent(this, DashboardActivity::class.java).apply {
            putExtra("EXTRA_CHILD_ID", childId)
            // flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK // Opcional: limpiar pila
        }
        startActivity(intent)
        // finish() // Opcional: cerrar esta actividad
    }

    /** Oculta el teclado virtual si está visible. */
    private fun hideKeyboard() {
        val view = this.currentFocus
        if (view != null) {
            val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
            imm?.hideSoftInputFromWindow(view.windowToken, 0)
        }
    }

    // --- Funciones para manejo de permisos ---

    /** Verifica si todos los permisos requeridos están concedidos */
    private fun hasAllRequiredPermissions(): Boolean {
        return REQUIRED_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
        }
    }

    /** Solicita los permisos requeridos */
    private fun requestRequiredPermissions() {
        Log.d(TAG, "Solicitando permisos requeridos")

        // Verificar si necesitamos mostrar explicación
        val shouldShowRationale = REQUIRED_PERMISSIONS.any { permission ->
            ActivityCompat.shouldShowRequestPermissionRationale(this, permission)
        }

        if (shouldShowRationale) {
            showPermissionRationaleDialog()
        } else {
            ActivityCompat.requestPermissions(this, REQUIRED_PERMISSIONS, LOCATION_PERMISSION_REQUEST_CODE)
        }
    }

    /** Muestra un diálogo explicando por qué necesitamos los permisos */
    private fun showPermissionRationaleDialog() {
        AlertDialog.Builder(this)
            .setTitle("Permisos necesarios")
            .setMessage("Esta aplicación necesita acceso a la ubicación y notificaciones para funcionar correctamente y mantener la seguridad de tu hijo.")
            .setPositiveButton("Conceder") { _, _ ->
                ActivityCompat.requestPermissions(this, REQUIRED_PERMISSIONS, LOCATION_PERMISSION_REQUEST_CODE)
            }
            .setNegativeButton("Cancelar") { _, _ ->
                Toast.makeText(this, "Los permisos son necesarios para el funcionamiento de la aplicación", Toast.LENGTH_LONG).show()
                finish()
            }
            .setCancelable(false)
            .show()
    }

    /** Maneja la respuesta de la solicitud de permisos */
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            LOCATION_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                    Log.d(TAG, "Permisos básicos concedidos")
                    // Solicitar permiso de ubicación en segundo plano si es necesario
                    requestBackgroundLocationPermission()
                } else {
                    Log.w(TAG, "Algunos permisos fueron denegados")
                    handlePermissionDenied()
                }
            }
            BACKGROUND_LOCATION_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Log.d(TAG, "Permiso de ubicación en segundo plano concedido")
                } else {
                    Log.w(TAG, "Permiso de ubicación en segundo plano denegado")
                    Toast.makeText(this, "El seguimiento en segundo plano puede ser limitado", Toast.LENGTH_LONG).show()
                }
                // Continuar con la verificación de vinculación
                checkDeviceLinkingStatus()
                requestBatteryOptimizationExemption()
            }
        }
    }

    /** Solicita el permiso de ubicación en segundo plano (Android 10+) */
    private fun requestBackgroundLocationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_BACKGROUND_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {

                AlertDialog.Builder(this)
                    .setTitle("Ubicación en segundo plano")
                    .setMessage("Para un seguimiento continuo, necesitamos acceso a la ubicación en segundo plano. Por favor, selecciona 'Permitir todo el tiempo' en la siguiente pantalla.")
                    .setPositiveButton("Continuar") { _, _ ->
                        ActivityCompat.requestPermissions(
                            this,
                            arrayOf(Manifest.permission.ACCESS_BACKGROUND_LOCATION),
                            BACKGROUND_LOCATION_PERMISSION_REQUEST_CODE
                        )
                    }
                    .setNegativeButton("Omitir") { _, _ ->
                        checkDeviceLinkingStatus()
                        requestBatteryOptimizationExemption()
                    }
                    .show()
            } else {
                checkDeviceLinkingStatus()
                requestBatteryOptimizationExemption()
            }
        } else {
            checkDeviceLinkingStatus()
            requestBatteryOptimizationExemption()
        }
    }

    /** Maneja cuando los permisos son denegados */
    private fun handlePermissionDenied() {
        AlertDialog.Builder(this)
            .setTitle("Permisos requeridos")
            .setMessage("Los permisos de ubicación son esenciales para el funcionamiento de esta aplicación. ¿Deseas ir a configuración para habilitarlos?")
            .setPositiveButton("Ir a configuración") { _, _ ->
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.fromParts("package", packageName, null)
                }
                startActivity(intent)
            }
            .setNegativeButton("Salir") { _, _ ->
                finish()
            }
            .setCancelable(false)
            .show()
    }

    /** Solicita exención de optimización de batería */
    private fun requestBatteryOptimizationExemption() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            val packageName = packageName

            if (!powerManager.isIgnoringBatteryOptimizations(packageName)) {
                AlertDialog.Builder(this)
                    .setTitle("Optimización de batería")
                    .setMessage("Para un mejor funcionamiento en segundo plano, recomendamos desactivar la optimización de batería para esta aplicación.")
                    .setPositiveButton("Configurar") { _, _ ->
                        try {
                            val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                                data = Uri.parse("package:$packageName")
                            }
                            startActivity(intent)
                        } catch (e: Exception) {
                            Log.e(TAG, "Error al abrir configuración de batería", e)
                            Toast.makeText(this, "No se pudo abrir la configuración de batería", Toast.LENGTH_SHORT).show()
                        }
                    }
                    .setNegativeButton("Omitir") { _, _ ->
                        // Continuar sin optimización
                    }
                    .show()
            }
        }
    }
}
