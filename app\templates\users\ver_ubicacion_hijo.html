<!-- app/templates/users/ver_ubicacion_hijo.html -->
{% extends 'base.html' %}

{% block content %}
  <h2>Ubicación de {{ child.first_name }} {{ child.last_name }}</h2>

  {% if location_data %}
    <div id="map" style="height: 400px;"></div> 
    <p><strong>Última Ubicación Registrada:</strong> {{ log.recorded_at }}</p>
    <p><strong>Latitud:</strong> {{ location_data.lat }}</p>
    <p><strong>Longitud:</strong> {{ location_data.lng }}</p>
  {% else %}
    <p>No se ha encontrado información de ubicación reciente para {{ child.first_name }}.</p>
  {% endif %}

  <a href="{{ url_for('users.dashboard') }}" class="btn btn-primary">Volver al Dashboard</a>

<script>
    var map = L.map('map').setView([-40.8136, -62.9936], 13); // Centro en Viedma por defecto
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 19,
        attribution: 'Map data © <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    {% if location_data %}
        var marker = L.marker([{{ location_data.lat }}, {{ location_data.lng }}]).addTo(map);
        marker.bindPopup("Última ubicación de {{ child.first_name }}");
        map.setView([{{ location_data.lat }}, {{ location_data.lng }}], 15); // Centrar en la ubicación del hijo
    {% endif %}
</script>

{% endblock %}