# app/routes/zones_management.py

import json
import traceback # <--- IMPORTADO PARA OBTENER TRACEBACK DETALLADO
from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from app import db
# Importar modelos actualizados
from app.models import SafeZone, ChildZoneAssignment, User, Child, ZoneComplianceLog
# Importar formularios actualizados
from app.forms.zone_forms import CreateZoneForm, EditZoneForm, AssignZoneForm
from app.utils.encryption import Encryption # Import Encryption

# Definir el Blueprint para las zonas
# No necesitamos especificar template_folder ya que usaremos rutas relativas
# en los render_template (por ejemplo, 'zones/crear_zona.html')
zones_bp = Blueprint('zones', __name__)

# Constante para el radio fijo de la zona segura (en metros)
ZONE_RADIUS = 50

@zones_bp.route('/crear_zona', methods=['GET', 'POST']) # URL actualizada
@login_required
def crear_zona(): # Función renombrada
    form = CreateZoneForm() # Usar el nuevo formulario

    # Cargar los hijos del padre actual
    # Usar .all() si la relación children es lazy='dynamic'
    hijos = current_user.children.all() if hasattr(current_user.children, 'all') else current_user.children
    if not hijos:
        flash('No tienes hijos registrados para asignar la zona.', 'warning')
        return redirect(url_for('users.dashboard'))

    # Asignar opciones al campo child_id
    form.child_id.choices = [(hijo.child_id, f"{hijo.first_name} {hijo.last_name}") for hijo in hijos]

    if form.validate_on_submit():
        zone_name = form.zone_name.data # Usar zone_name
        schedule_start = form.schedule_start.data
        schedule_end = form.schedule_end.data
        # Obtener latitud y longitud de los campos ocultos del formulario
        latitude = form.latitude.data
        longitude = form.longitude.data

        try:
            # Validar que lat/lon sean números flotantes
            lat_float = float(latitude)
            lon_float = float(longitude)
        except (ValueError, TypeError):
            flash('Coordenadas inválidas recibidas.', 'danger')
            # Re-renderizar el formulario
            # Asegúrate que la plantilla 'crear_zona.html' exista en la ruta correcta
            return render_template('zones/crear_zona.html', form=form)

        # Crear el diccionario con los datos de la zona
        zone_data_dict = {
            'lat': lat_float,
            'lon': lon_float,
            'radius': ZONE_RADIUS # Usar la constante definida
        }
        # Convertir a JSON string
        zone_data_json = json.dumps(zone_data_dict)

        # --- ENCRIPTAR los datos de la zona (lat, lon, radius) ---
        try:
            fernet_key = current_app.config.get('FERNET_KEY') # Usar .get() es más seguro
            if not fernet_key:
                 raise ValueError("FERNET_KEY no configurada en la aplicación.")
            encryption = Encryption(fernet_key)
            encrypted_zone_data = encryption.encrypt(zone_data_json) # ENCRYPTAR JSON
        except Exception as e:
            current_app.logger.error(f"Error de encriptación al crear zona: {e}", exc_info=True) # Añadir exc_info
            flash('Error interno al procesar los datos de la zona.', 'danger')
            return render_template('zones/crear_zona.html', form=form)

        # Crear la nueva zona segura
        new_zone = SafeZone( # Usar el nuevo modelo
            father_id=current_user.user_id,
            zone_name=zone_name,
            schedule_start=schedule_start,
            schedule_end=schedule_end,
            encrypted_zone_data=encrypted_zone_data # Guardar datos encriptados
        )
        db.session.add(new_zone)
        try:
            db.session.commit() # Commit para obtener new_zone.zone_id
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error de BD al guardar SafeZone: {e}", exc_info=True) # Añadir exc_info
            flash('Error al guardar la zona en la base de datos.', 'danger')
            return render_template('zones/crear_zona.html', form=form)

        # Crear la asignación de la zona al hijo seleccionado
        assignment = ChildZoneAssignment( # Usar el nuevo modelo de asignación
            child_id=form.child_id.data,
            zone_id=new_zone.zone_id, # Usar zone_id
            status='assigned'
        )
        db.session.add(assignment)
        try:
            db.session.commit()
        except Exception as e:
             db.session.rollback()
             # Considerar eliminar la zona si la asignación falla
             # try:
             #     db.session.delete(new_zone)
             #     db.session.commit()
             # except Exception as del_e:
             #     current_app.logger.error(f"Error al intentar eliminar zona {new_zone.zone_id} tras fallo de asignación: {del_e}", exc_info=True)
             current_app.logger.error(f"Error de BD al guardar ChildZoneAssignment: {e}", exc_info=True) # Añadir exc_info
             flash('Zona creada, pero hubo un error al asignarla al hijo.', 'warning')
             # Redirigir a ver zonas para que pueda intentar asignar manualmente
             return redirect(url_for('zones.ver_zonas')) # URL actualizada

        flash('Zona Segura creada y asignada exitosamente.', 'success')
        return redirect(url_for('zones.ver_zonas')) # URL actualizada

    # Renderizar plantilla actualizada en GET o si el form no es válido
    # Usar la ruta correcta para la plantilla
    return render_template('zones/crear_zona.html', form=form)

# --- VER ZONAS CON BLOQUE TRY...EXCEPT ---
@zones_bp.route('/ver_zonas') # URL actualizada
@login_required
def ver_zonas(): # Función renombrada
    try: # <--- INICIO DEL TRY
        current_app.logger.debug("Entrando en la función ver_zonas") # Log para ver si entra

        # Lista las zonas creadas por el padre actual
        zones = SafeZone.query.filter_by(father_id=current_user.user_id).order_by(SafeZone.zone_name).all() # Usar SafeZone
        current_app.logger.debug(f"Zonas consultadas: {len(zones)}")

        # Crear instancia del formulario de asignación
        form_asignar = AssignZoneForm() # Usar el nuevo formulario
        current_app.logger.debug("Formulario de asignación creado")

        # Pasar hijos a la plantilla para el select de asignación
        hijos = current_user.children.all() if hasattr(current_user.children, 'all') else current_user.children
        current_app.logger.debug(f"Hijos consultados: {len(hijos)}")

        # Renderizar plantilla actualizada
        current_app.logger.debug("Intentando renderizar ver_zona.html")
        # Usar la ruta correcta para la plantilla
        response = render_template('zones/ver_zona.html', zones=zones, form_asignar=form_asignar, hijos=hijos)
        current_app.logger.debug("Renderizado de ver_zona.html completado")
        return response

    except Exception as e: # <--- CAPTURAR CUALQUIER EXCEPCIÓN
        # Imprimir el traceback completo en la consola (stderr) y logs
        current_app.logger.error(f"¡¡¡ ERROR CATASTRÓFICO EN ver_zonas !!!")
        current_app.logger.error(f"Tipo de error: {type(e).__name__}")
        current_app.logger.error(f"Error: {e}")
        current_app.logger.error("--- TRACEBACK COMPLETO ---")
        # Usar el módulo traceback para imprimir el stack trace a stderr y a los logs si están configurados
        traceback_str = traceback.format_exc()
        print(traceback_str) # Directo a stderr por si los logs fallan
        current_app.logger.error(traceback_str)
        current_app.logger.error("--- FIN TRACEBACK ---")

        # Devolver una respuesta de error 500 genérica para el navegador
        flash("Ocurrió un error interno al intentar mostrar las zonas.", "danger")
        # Puedes crear una plantilla específica para errores 500
        return "Internal Server Error", 500
    # <--- FIN DEL EXCEPT

@zones_bp.route('/zona/<int:zone_id>/asignar', methods=['POST']) # URL y parámetro actualizados
@login_required
def asignar_zona(zone_id): # Función y parámetro renombrados
    # Obtener la zona o devolver 404
    zone = SafeZone.query.get_or_404(zone_id) # Usar SafeZone
    # Verificar que la zona pertenezca al padre actual
    if zone.father_id != current_user.user_id:
        flash('No tienes permiso para asignar esta zona.', 'danger')
        return redirect(url_for('zones.ver_zonas')) # URL actualizada

    # Obtener el child_id enviado por el formulario (desde el <select> en la plantilla)
    child_id = request.form.get('child_id')
    if not child_id:
        flash('No se seleccionó ningún hijo.', 'danger')
        return redirect(url_for('zones.ver_zonas')) # URL actualizada

    # Verificar que el hijo pertenezca al padre actual
    try:
        child_id_int = int(child_id)
        child = Child.query.filter_by(child_id=child_id_int, parent_id=current_user.user_id).first()
        if not child:
            flash('El hijo seleccionado no pertenece a tu cuenta o no existe.', 'danger')
            return redirect(url_for('zones.ver_zonas')) # URL actualizada
    except ValueError:
         flash('ID de hijo inválido.', 'danger')
         return redirect(url_for('zones.ver_zonas'))

    # Validar para evitar asignaciones duplicadas
    existing_assignment = ChildZoneAssignment.query.filter_by(child_id=child_id_int, zone_id=zone_id).first() # Usar ChildZoneAssignment
    if existing_assignment:
        flash('Esta zona ya está asignada a ese hijo.', 'warning')
        return redirect(url_for('zones.ver_zonas')) # URL actualizada

    # Si no existe, crear la asignación
    assignment = ChildZoneAssignment( # Usar ChildZoneAssignment
        child_id=child_id_int,
        zone_id=zone_id,
        status='assigned'
    )
    db.session.add(assignment)
    try:
        db.session.commit()
        flash('Zona asignada exitosamente.', 'success')
    except Exception as e:
         db.session.rollback()
         current_app.logger.error(f"Error de BD al asignar zona {zone_id} a hijo {child_id}: {e}", exc_info=True) # Añadir exc_info
         flash('Error al asignar la zona.', 'danger')

    return redirect(url_for('zones.ver_zonas')) # URL actualizada

@zones_bp.route('/zona/<int:zone_id>/editar', methods=['GET', 'POST']) # URL y parámetro actualizados
@login_required
def editar_zona(zone_id): # Función y parámetro renombrados
    # Obtener la zona a editar o devolver 404
    zone = SafeZone.query.get_or_404(zone_id) # Usar SafeZone
    # Verificar que la zona pertenezca al padre actual
    if zone.father_id != current_user.user_id:
        flash('No tienes permiso para editar esta zona.', 'danger')
        return redirect(url_for('zones.ver_zonas')) # URL actualizada

    # Cargar los hijos del padre actual
    hijos = current_user.children.all() if hasattr(current_user.children, 'all') else current_user.children
    if not hijos:
        flash('No tienes hijos registrados.', 'warning')
        return redirect(url_for('users.dashboard'))

    # Consultar la asignación actual de la zona
    assignment = ChildZoneAssignment.query.filter_by(zone_id=zone_id).first() # Usar ChildZoneAssignment

    form = EditZoneForm(obj=zone) # Usar EditZoneForm, precarga campos si coinciden

    # Asignar opciones al campo child_id
    form.child_id.choices = [(hijo.child_id, f"{hijo.first_name} {hijo.last_name}") for hijo in hijos]

    if request.method == 'GET':
        # Precargar datos existentes en el formulario
        form.zone_name.data = zone.zone_name
        form.schedule_start.data = zone.schedule_start
        form.schedule_end.data = zone.schedule_end

        # --- DESENCRIPTAR y PRECARGAR lat/lon en campos ocultos ---
        try:
            fernet_key = current_app.config.get('FERNET_KEY')
            if not fernet_key:
                 raise ValueError("FERNET_KEY no configurada.")
            encryption = Encryption(fernet_key)
            decrypted_zone_json = encryption.decrypt(zone.encrypted_zone_data)
            decrypted_data = json.loads(decrypted_zone_json)

            # Poblar los campos ocultos del formulario
            form.latitude.data = decrypted_data.get('lat')
            form.longitude.data = decrypted_data.get('lon')

        except Exception as e:
            current_app.logger.error(f"Error desencriptando/parseando datos de zona {zone_id} para editar: {e}", exc_info=True) # Añadir exc_info
            flash('Error al cargar los datos de ubicación de la zona. No se puede mostrar en el mapa.', 'danger')
            form.latitude.data = None
            form.longitude.data = None

        # Seleccionar el hijo asignado actualmente en el <select>
        if assignment:
            form.child_id.data = assignment.child_id

    if form.validate_on_submit():
        # Actualizar los datos de la zona
        zone.zone_name = form.zone_name.data
        zone.schedule_start = form.schedule_start.data
        zone.schedule_end = form.schedule_end.data
        latitude = form.latitude.data
        longitude = form.longitude.data

        try:
            lat_float = float(latitude)
            lon_float = float(longitude)
        except (ValueError, TypeError):
            flash('Coordenadas inválidas recibidas.', 'danger')
             # Asegúrate que la plantilla 'editar_zona.html' exista en la ruta correcta
            return render_template('zones/editar_zona.html', form=form, zone_id=zone_id) # Re-renderizar

        # Crear diccionario, convertir a JSON y encriptar (igual que en crear_zona)
        zone_data_dict = {'lat': lat_float, 'lon': lon_float, 'radius': ZONE_RADIUS}
        zone_data_json = json.dumps(zone_data_dict)
        try:
            fernet_key = current_app.config.get('FERNET_KEY')
            if not fernet_key: raise ValueError("FERNET_KEY no configurada.")
            encryption = Encryption(fernet_key)
            zone.encrypted_zone_data = encryption.encrypt(zone_data_json) # Actualizar campo encriptado
        except Exception as e:
             current_app.logger.error(f"Error de encriptación al editar zona {zone_id}: {e}", exc_info=True) # Añadir exc_info
             flash('Error interno al procesar los datos de la zona.', 'danger')
             return render_template('zones/editar_zona.html', form=form, zone_id=zone_id)

        # Actualizar o crear la asignación del hijo
        nuevo_child_id = form.child_id.data
        # Verificar que el hijo seleccionado pertenezca al padre
        if not any(c.child_id == nuevo_child_id for c in hijos):
            flash('El hijo seleccionado no pertenece a tu cuenta.', 'danger')
            return render_template('zones/editar_zona.html', form=form, zone_id=zone_id)

        if assignment:
            # Si la asignación ya existía, solo cambiar el hijo si es diferente
            if assignment.child_id != nuevo_child_id:
                 assignment.child_id = nuevo_child_id
        else:
            # Si no existía una asignación previa para esta zona, crearla
            assignment = ChildZoneAssignment( # Usar ChildZoneAssignment
                child_id=nuevo_child_id,
                zone_id=zone_id,
                status='assigned'
            )
            db.session.add(assignment)

        try:
            db.session.commit()
            flash('Zona Segura actualizada exitosamente.', 'success')
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error de BD al actualizar zona/asignación {zone_id}: {e}", exc_info=True) # Añadir exc_info
            flash('Error al guardar los cambios en la base de datos.', 'danger')
            return render_template('zones/editar_zona.html', form=form, zone_id=zone_id) # Re-renderizar

        return redirect(url_for('zones.ver_zonas')) # URL actualizada

    # Renderizar plantilla actualizada en GET o si el form no es válido
    # Pasar zone_id es importante para las URLs dentro de la plantilla
    # Asegúrate que la plantilla 'editar_zona.html' exista en la ruta correcta
    return render_template('zones/editar_zona.html', form=form, zone_id=zone_id)

@zones_bp.route('/zona/<int:zone_id>/eliminar', methods=['POST']) # URL y parámetro actualizados
@login_required
def eliminar_zona(zone_id): # Función y parámetro renombrados
    print("=" * 60)
    print("🗑️ INTENTO DE ELIMINAR ZONA")
    print("=" * 60)
    print(f"🆔 Zone ID: {zone_id}")
    print(f"👤 Usuario: {current_user.username} (ID: {current_user.user_id})")
    print(f"🔧 Método: {request.method}")

    try:
        # Obtener la zona o devolver 404
        zone = SafeZone.query.get_or_404(zone_id) # Usar SafeZone
        print(f"✅ Zona encontrada: {zone.zone_name}")
        print(f"👨‍👧‍👦 Propietario de la zona: {zone.father_id}")

        # Verificar que la zona pertenezca al padre actual
        if zone.father_id != current_user.user_id:
            print("❌ Usuario no tiene permisos para eliminar esta zona")
            flash('No tienes permiso para eliminar esta zona.', 'danger')
            return redirect(url_for('zones.ver_zonas')) # URL actualizada

        print("✅ Permisos verificados correctamente")

        # Verificar asignaciones existentes
        assignments = ChildZoneAssignment.query.filter_by(zone_id=zone_id).all()
        print(f"📋 Asignaciones encontradas: {len(assignments)}")
        for assignment in assignments:
            print(f"   - Assignment ID: {assignment.assignment_id}, Child ID: {assignment.child_id}")

        # Verificar logs de cumplimiento
        try:
            compliance_logs = ZoneComplianceLog.query.filter_by(zone_id=zone_id).all()
            print(f"📊 Logs de cumplimiento encontrados: {len(compliance_logs)}")
        except Exception as e:
            print(f"⚠️ Error verificando logs de cumplimiento: {e}")

        # ORDEN CORRECTO: Eliminar primero los logs de cumplimiento, luego las asignaciones

        # 1. Eliminar logs de cumplimiento PRIMERO (referencian a assignments)
        try:
            print("🔄 Eliminando logs de cumplimiento...")
            deleted_logs = ZoneComplianceLog.query.filter_by(zone_id=zone_id).delete()
            print(f"✅ Logs de cumplimiento eliminados: {deleted_logs}")
        except Exception as e:
            print(f"⚠️ Error eliminando logs de cumplimiento: {e}")
            # Continuar aunque falle, puede que no existan logs

        # 2. Eliminar las asignaciones DESPUÉS (ya no hay logs que las referencien)
        print("🔄 Eliminando asignaciones...")
        deleted_assignments = ChildZoneAssignment.query.filter_by(zone_id=zone_id).delete()
        print(f"✅ Asignaciones eliminadas: {deleted_assignments}")

        # Eliminar la zona misma
        print("🔄 Eliminando zona...")
        db.session.delete(zone)

        print("💾 Haciendo commit...")
        db.session.commit()

        print("🎉 Zona eliminada exitosamente")
        flash('Zona Segura eliminada exitosamente.', 'success')

    except Exception as e:
        print("💥 ERROR AL ELIMINAR ZONA")
        print(f"❌ Error: {e}")
        print(f"❌ Tipo: {type(e).__name__}")
        import traceback
        print(f"❌ Traceback:")
        print(traceback.format_exc())

        db.session.rollback()
        current_app.logger.error(f"Error de BD al eliminar zona {zone_id} y/o sus asignaciones: {e}", exc_info=True) # Añadir exc_info
        flash(f'Error al eliminar la zona: {str(e)}', 'danger')

    print("=" * 60)
    return redirect(url_for('zones.ver_zonas')) # URL actualizada