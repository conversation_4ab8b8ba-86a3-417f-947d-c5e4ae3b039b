 <!-- app/templates/users/editar_hijo.html -->
 {% extends 'base.html' %}

 {% block content %}
   <h1>Editar Hijo</h1>
   <form method="POST" action="{{ url_for('users.editar_hijo', child_id=child.child_id) }}">
     {{ form.hidden_tag() }}
     <div class="form-group">
       {{ form.first_name.label }} {{ form.first_name(class="form-control") }}
       {% for error in form.first_name.errors %}
         <span class="text-danger">{{ error }}</span>
       {% endfor %}
     </div>
     <div class="form-group">
       {{ form.last_name.label }} {{ form.last_name(class="form-control") }}
       {% for error in form.last_name.errors %}
         <span class="text-danger">{{ error }}</span>
       {% endfor %}
     </div>
     <button type="submit" class="btn btn-primary">{{ form.submit.label.text }}</button>
   </form>
 {% endblock %}
 