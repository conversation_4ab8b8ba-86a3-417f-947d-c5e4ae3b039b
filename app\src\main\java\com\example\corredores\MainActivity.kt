package com.example.corredores

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.webkit.CookieManager
import android.webkit.JavascriptInterface
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.corredores.models.NotificationRequest
import com.example.corredores.models.WebSessionRequest
import com.example.corredores.network.NetworkClient
import com.example.corredores.services.LocationService
import com.example.corredores.utils.BatteryOptimizationHelper
import com.example.corredores.utils.PreferenceManager
import com.example.corredores.utils.FCMManager
import kotlinx.coroutines.launch

class MainActivity : AppCompatActivity() {

    lateinit var preferenceManager: PreferenceManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            Log.d("MainActivity", "🚀 MainActivity onCreate started")

            preferenceManager = PreferenceManager(this)
            Log.d("MainActivity", "✅ PreferenceManager initialized")

            val userType = preferenceManager.getUserType()
            val isLoggedIn = preferenceManager.isLoggedIn()
            Log.d("MainActivity", "👤 User type: $userType, Logged in: $isLoggedIn")

            // Para padre, no verificar si está logueado (puede no estarlo aún)
            if (userType == PreferenceManager.USER_TYPE_PARENT) {
                Log.d("MainActivity", "👨‍👧‍👦 Setting up parent view...")
                setupParentView()
                return
            }

            // Para hijo, sí verificar que esté logueado
            if (!isLoggedIn) {
                Log.d("MainActivity", "🚪 User not logged in, redirecting...")
                redirectToLogin()
                return
            }

            // Configurar UI según el tipo de usuario
            when (userType) {
                PreferenceManager.USER_TYPE_CHILD -> {
                    Log.d("MainActivity", "👶 Setting up child view...")
                    setupChildView()
                }
                else -> {
                    Log.d("MainActivity", "❓ Unknown user type, redirecting to profile selection")
                    redirectToProfileSelection()
                }
            }

        } catch (e: Exception) {
            Log.e("MainActivity", "💥 Error in onCreate", e)
            Toast.makeText(this, "Error: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun setupParentView() {
        try {
            Log.d("MainActivity", "🔧 Setting up parent view...")
            setContentView(R.layout.activity_main_parent)
            Log.d("MainActivity", "✅ Layout set successfully")

            val webView = findViewById<WebView>(R.id.webview_dashboard)
            Log.d("MainActivity", "✅ Views found successfully")

            // Configurar WebView para móvil
            webView.settings.apply {
                javaScriptEnabled = true
                domStorageEnabled = true
                loadWithOverviewMode = true
                useWideViewPort = true
                setSupportZoom(true)
                builtInZoomControls = true
                displayZoomControls = false
                allowFileAccess = true
                allowContentAccess = true
                // Configuraciones para mantener sesión
                databaseEnabled = true
                cacheMode = WebSettings.LOAD_DEFAULT
                mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            }

            // Habilitar cookies para mantener sesión
            CookieManager.getInstance().setAcceptCookie(true)
            CookieManager.getInstance().setAcceptThirdPartyCookies(webView, true)

            // Configurar escala inicial
            webView.setInitialScale(100)
            Log.d("MainActivity", "✅ WebView configured")

            // Configurar WebViewClient para mejorar visualización móvil
            webView.webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)

                    // Inyectar CSS para mejorar la visualización móvil
                    val mobileCSS = """
                        javascript:(function() {
                            var meta = document.createElement('meta');
                            meta.name = 'viewport';
                            meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
                            document.getElementsByTagName('head')[0].appendChild(meta);

                            var style = document.createElement('style');
                            style.innerHTML = `
                                body {
                                    font-size: 16px !important;
                                    -webkit-text-size-adjust: 100% !important;
                                }
                                input, button, select, textarea {
                                    font-size: 16px !important;
                                    padding: 12px !important;
                                    margin: 8px 0 !important;
                                }
                                .container, .form-container {
                                    max-width: 100% !important;
                                    padding: 15px !important;
                                    margin: 0 auto !important;
                                }
                                table {
                                    width: 100% !important;
                                    overflow-x: auto !important;
                                    display: block !important;
                                    white-space: nowrap !important;
                                }
                            `;
                            document.head.appendChild(style);
                        })()
                    """

                    view?.evaluateJavascript(mobileCSS, null)
                }
            }

            // Cargar URL simple
            val url = "https://patagoniaservers.com.ar:5004/auth/login"
            Log.d("MainActivity", "🌐 Loading URL: $url")
            webView.loadUrl(url)



            Log.d("MainActivity", "✅ Parent view setup completed")

        } catch (e: Exception) {
            Log.e("MainActivity", "💥 Error in setupParentView", e)
            // Mostrar un mensaje de error simple
            Toast.makeText(this, "Error: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun setupChildView() {
        setContentView(R.layout.activity_main_child)

        val webView = findViewById<WebView>(R.id.webview_dashboard_child)

        // Configurar WebView
        webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            loadWithOverviewMode = true
            useWideViewPort = true
            allowFileAccess = true
            allowContentAccess = true
            // Permitir acceso a ubicación desde JavaScript
            setGeolocationEnabled(true)
        }

        // Agregar interfaz JavaScript para comunicación con la app nativa
        webView.addJavascriptInterface(WebAppInterface(this), "AndroidApp")

        // Solicitar optimización de batería
        BatteryOptimizationHelper.requestIgnoreBatteryOptimizations(this)

        // Asegurar que el servicio de ubicación esté corriendo
        LocationService.startService(this)

        // Configurar WebView para manejar cookies
        val cookieManager = android.webkit.CookieManager.getInstance()
        cookieManager.setAcceptCookie(true)
        cookieManager.setAcceptThirdPartyCookies(webView, true)

        // Configurar WebViewClient inteligente para simular el flujo web
        setupWebViewClient(webView, cookieManager)

        // Configurar WebChromeClient para permisos de geolocalización
        webView.webChromeClient = object : android.webkit.WebChromeClient() {
            override fun onGeolocationPermissionsShowPrompt(
                origin: String?,
                callback: android.webkit.GeolocationPermissions.Callback?
            ) {
                // Permitir geolocalización automáticamente
                callback?.invoke(origin, true, false)
            }
        }

        // Iniciar el flujo de autenticación web
        startWebAuthenticationFlow(webView)

        Toast.makeText(this, "Seguimiento de ubicación iniciado", Toast.LENGTH_SHORT).show()
    }

    private fun setupWebViewClient(webView: WebView, cookieManager: android.webkit.CookieManager) {
        webView.webViewClient = object : WebViewClient() {

            override fun onPageStarted(view: WebView?, url: String?, favicon: android.graphics.Bitmap?) {
                super.onPageStarted(view, url, favicon)
                android.util.Log.d("WebView", "Loading: $url")
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                android.util.Log.d("WebView", "Finished loading: $url")

                // Si estamos en la página de vinculación, llenar el formulario automáticamente
                if (url?.contains("vincular_dispositivo") == true) {
                    fillTokenFormAutomatically(view)
                }

                // Si estamos en cualquier dashboard, inyectar JavaScript para interceptar llamadas
                if (url?.contains("dashboard") == true) {
                    // Esperar un poco para que la página se cargue completamente
                    view?.postDelayed({
                        injectDashboardJavaScript(view)
                    }, 1000)
                }
            }

            override fun onReceivedError(view: WebView?, request: android.webkit.WebResourceRequest?, error: android.webkit.WebResourceError?) {
                super.onReceivedError(view, request, error)
                android.util.Log.e("WebView", "Error loading page: ${error?.description}")
            }
        }
    }

    private fun startWebAuthenticationFlow(webView: WebView) {
        // SOLUCIÓN ROBUSTA: Intentar crear sesión web via API, si falla usar método local
        createWebSessionViaAPI(webView)
    }

    private fun createWebSessionViaAPI(webView: WebView) {
        val childId = preferenceManager.getChildId()

        lifecycleScope.launch {
            try {
                val request = WebSessionRequest(child_id = childId)
                val response = NetworkClient.apiService.createWebSession(request)

                if (response.isSuccessful) {
                    val sessionResponse = response.body()
                    if (sessionResponse != null && sessionResponse.status == "success") {
                        // Cargar la URL del dashboard con token temporal
                        sessionResponse.dashboard_url?.let { dashboardUrl ->
                            android.util.Log.d("WebSession", "Loading dashboard: $dashboardUrl")
                            webView.loadUrl(dashboardUrl)
                        } ?: run {
                            android.util.Log.w("WebSession", "No dashboard URL received")
                            createWebSessionAndLoadDashboard(webView)
                        }
                    } else {
                        android.util.Log.w("WebSession", "API call failed: ${sessionResponse?.message}")
                        createWebSessionAndLoadDashboard(webView)
                    }
                } else {
                    android.util.Log.w("WebSession", "HTTP error: ${response.code()}")
                    createWebSessionAndLoadDashboard(webView)
                }
            } catch (e: Exception) {
                android.util.Log.e("WebSession", "Exception creating web session: ${e.message}")
                createWebSessionAndLoadDashboard(webView)
            }
        }
    }

    private fun createWebSessionAndLoadDashboard(webView: WebView) {
        // SOLUCIÓN TEMPORAL: Mostrar una página HTML local que explique la situación
        // y proporcione un enlace manual al dashboard
        val childId = preferenceManager.getChildId()

        val htmlContent = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Dashboard Hijo</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        padding: 20px;
                        text-align: center;
                        background-color: #f5f5f5;
                    }
                    .container {
                        background: white;
                        padding: 30px;
                        border-radius: 10px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        margin: 20px auto;
                        max-width: 400px;
                    }
                    .status {
                        color: #4CAF50;
                        font-size: 18px;
                        font-weight: bold;
                        margin-bottom: 20px;
                    }
                    .info {
                        color: #666;
                        margin-bottom: 20px;
                        line-height: 1.5;
                    }
                    .dashboard-btn {
                        background-color: #2196F3;
                        color: white;
                        padding: 15px 30px;
                        border: none;
                        border-radius: 5px;
                        font-size: 16px;
                        cursor: pointer;
                        text-decoration: none;
                        display: inline-block;
                        margin: 10px;
                    }
                    .child-id {
                        background-color: #e3f2fd;
                        padding: 10px;
                        border-radius: 5px;
                        margin: 15px 0;
                        font-family: monospace;
                        font-size: 14px;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <h2>🟢 Dispositivo Conectado</h2>
                    <div class="status">Seguimiento Activo</div>
                    <div class="child-id">ID: $childId</div>
                    <div class="info">
                        Tu ubicación está siendo compartida de forma segura con tu padre.
                        La aplicación seguirá funcionando en segundo plano.
                    </div>
                    <a href="https://patagoniaservers.com.ar:5004/users/dashboard_hijo" class="dashboard-btn">
                        Ir al Dashboard Web
                    </a>
                    <div class="info" style="margin-top: 20px; font-size: 12px; color: #999;">
                        Si el enlace no funciona, contacta a tu padre para generar un nuevo token.
                    </div>
                </div>

                <script>
                    // Intentar establecer cookie y redirigir automáticamente
                    document.cookie = "child_id=$childId; path=/; domain=patagoniaservers.com.ar";

                    // Intentar redirigir automáticamente después de 2 segundos
                    setTimeout(function() {
                        window.location.href = "https://patagoniaservers.com.ar:5004/users/dashboard_hijo";
                    }, 2000);
                </script>
            </body>
            </html>
        """.trimIndent()

        webView.loadDataWithBaseURL(
            "https://patagoniaservers.com.ar",
            htmlContent,
            "text/html",
            "UTF-8",
            null
        )
    }

    private fun injectDashboardJavaScript(webView: WebView?) {
        android.util.Log.d("WebView", "Injecting JavaScript...")

        val javascript = """
            (function() {
                console.log('🚀 Iniciando interceptación de llamadas...');

                // Función para interceptar y manejar notificaciones
                function interceptNotification(notificationType, messageText) {
                    console.log('📤 Interceptando notificación:', notificationType);
                    try {
                        if (typeof AndroidApp !== 'undefined' && AndroidApp.sendNotification) {
                            AndroidApp.sendNotification(notificationType, messageText || '');
                            return {status: 'success', message: 'Notificación enviada'};
                        } else {
                            console.error('❌ AndroidApp no disponible');
                            return {status: 'error', message: 'App nativa no disponible'};
                        }
                    } catch (e) {
                        console.error('❌ Error enviando notificación:', e);
                        return {status: 'error', message: e.message};
                    }
                }

                // Interceptar fetch
                if (window.fetch) {
                    const originalFetch = window.fetch;
                    window.fetch = function(url, options) {
                        console.log('🌐 Fetch intercepted:', url);

                        if (url.includes('send_notification') || url.includes('api/send_notification')) {
                            console.log('📤 Interceptando fetch de notificación');
                            try {
                                const body = options && options.body ? JSON.parse(options.body) : {};
                                const result = interceptNotification(body.notification_type, body.message_text);

                                return Promise.resolve({
                                    ok: true,
                                    status: 200,
                                    json: () => Promise.resolve(result),
                                    text: () => Promise.resolve(JSON.stringify(result))
                                });
                            } catch (e) {
                                console.error('❌ Error en fetch interceptado:', e);
                                return Promise.reject(e);
                            }
                        }

                        return originalFetch.apply(this, arguments);
                    };
                }

                // Interceptar XMLHttpRequest
                if (window.XMLHttpRequest) {
                    const OriginalXHR = window.XMLHttpRequest;
                    window.XMLHttpRequest = function() {
                        const xhr = new OriginalXHR();
                        const originalOpen = xhr.open;
                        const originalSend = xhr.send;
                        let requestUrl = '';

                        xhr.open = function(method, url, async, user, password) {
                            requestUrl = url;
                            return originalOpen.apply(this, arguments);
                        };

                        xhr.send = function(data) {
                            if (requestUrl.includes('send_notification') || requestUrl.includes('api/send_notification')) {
                                console.log('📤 Interceptando XHR de notificación');
                                try {
                                    const body = data ? JSON.parse(data) : {};
                                    const result = interceptNotification(body.notification_type, body.message_text);

                                    // Simular respuesta exitosa
                                    setTimeout(() => {
                                        Object.defineProperty(xhr, 'readyState', { value: 4, configurable: true });
                                        Object.defineProperty(xhr, 'status', { value: 200, configurable: true });
                                        Object.defineProperty(xhr, 'responseText', {
                                            value: JSON.stringify(result),
                                            configurable: true
                                        });

                                        if (xhr.onreadystatechange) {
                                            xhr.onreadystatechange();
                                        }
                                        if (xhr.onload) {
                                            xhr.onload();
                                        }
                                    }, 100);

                                    return;
                                } catch (e) {
                                    console.error('❌ Error en XHR interceptado:', e);
                                }
                            }

                            return originalSend.apply(this, arguments);
                        };

                        return xhr;
                    };
                }

                // Interceptar solicitudes de ubicación inmediata
                if (window.fetch) {
                    const originalFetch = window.fetch;
                    window.fetch = function(url, options) {
                        if (url.includes('request_immediate_location') || url.includes('get_current_location')) {
                            console.log('🎯 Intercepting immediate location request');
                            try {
                                if (typeof AndroidApp !== 'undefined' && AndroidApp.requestImmediateLocation) {
                                    AndroidApp.requestImmediateLocation();
                                    return Promise.resolve({
                                        ok: true,
                                        status: 200,
                                        json: () => Promise.resolve({status: 'success', message: 'Location request sent'})
                                    });
                                }
                            } catch (e) {
                                console.error('❌ Error requesting immediate location:', e);
                            }
                        }

                        return originalFetch.apply(this, arguments);
                    };
                }

                // Función global para solicitar ubicación inmediata
                window.requestImmediateLocation = function() {
                    console.log('🎯 Global function: requesting immediate location');
                    if (typeof AndroidApp !== 'undefined' && AndroidApp.requestImmediateLocation) {
                        return AndroidApp.requestImmediateLocation();
                    } else {
                        console.error('❌ AndroidApp not available for location request');
                        return false;
                    }
                };

                // Marcar GPS como activo
                window.gpsActive = true;

                console.log('✅ JavaScript injection completed');

                // Verificar que AndroidApp esté disponible
                if (typeof AndroidApp !== 'undefined') {
                    console.log('✅ AndroidApp interface available');
                    console.log('✅ Immediate location function available');
                } else {
                    console.error('❌ AndroidApp interface NOT available');
                }
            })();
        """.trimIndent()

        webView?.evaluateJavascript(javascript) { result ->
            android.util.Log.d("WebView", "JavaScript injection result: $result")
        }
    }

    private fun fillTokenFormAutomatically(webView: WebView?) {
        // Este método ya no se usa en la nueva implementación
        // Pero lo mantenemos por si acaso
        android.util.Log.d("WebView", "fillTokenFormAutomatically called - not needed in new implementation")
    }

    private fun getLastUsedToken(): String {
        return preferenceManager.getLastToken() ?: ""
    }

    private fun showLogoutDialog() {
        // Solo permitir logout para padres
        if (preferenceManager.getUserType() == PreferenceManager.USER_TYPE_PARENT) {
            AlertDialog.Builder(this)
                .setTitle("Cerrar Sesión")
                .setMessage("¿Estás seguro de que quieres cerrar sesión?")
                .setPositiveButton("Sí") { _, _ ->
                    logout()
                }
                .setNegativeButton("No", null)
                .show()
        }
    }

    private fun logout() {
        // Solo para padres - limpiar datos y redirigir
        if (preferenceManager.getUserType() == PreferenceManager.USER_TYPE_PARENT) {
            // Desregistrar tokens FCM antes del logout
            val fcmManager = FCMManager(this)
            fcmManager.unregisterToken()

            // Limpiar datos locales
            preferenceManager.clearAll()
            redirectToProfileSelection()
        }
    }

    private fun redirectToLogin() {
        val userType = preferenceManager.getUserType()
        val intent = when (userType) {
            PreferenceManager.USER_TYPE_PARENT -> Intent(this, MainActivity::class.java)
            PreferenceManager.USER_TYPE_CHILD -> Intent(this, TokenActivity::class.java)
            else -> Intent(this, ProfileSelectionActivity::class.java)
        }
        startActivity(intent)
        finish()
    }

    private fun redirectToProfileSelection() {
        val intent = Intent(this, ProfileSelectionActivity::class.java)
        startActivity(intent)
        finish()
    }

    fun getImmediateLocation() {
        android.util.Log.d("MainActivity", "🎯 Getting immediate location for parent request...")

        // Usar FusedLocationProviderClient para obtener ubicación inmediata
        val fusedLocationClient = com.google.android.gms.location.LocationServices.getFusedLocationProviderClient(this)

        if (androidx.core.app.ActivityCompat.checkSelfPermission(
                this,
                android.Manifest.permission.ACCESS_FINE_LOCATION
            ) != android.content.pm.PackageManager.PERMISSION_GRANTED
        ) {
            android.util.Log.e("MainActivity", "Location permission not granted")
            return
        }

        // Crear solicitud de ubicación inmediata
        val immediateLocationRequest = com.google.android.gms.location.LocationRequest.Builder(
            com.google.android.gms.location.Priority.PRIORITY_HIGH_ACCURACY,
            0L // Inmediato
        ).apply {
            setMaxUpdateDelayMillis(5000L) // Máximo 5 segundos
            setMinUpdateIntervalMillis(0L)
            setWaitForAccurateLocation(false)
        }.build()

        // Callback para una sola ubicación
        val immediateCallback = object : com.google.android.gms.location.LocationCallback() {
            override fun onLocationResult(locationResult: com.google.android.gms.location.LocationResult) {
                super.onLocationResult(locationResult)
                locationResult.lastLocation?.let { location ->
                    android.util.Log.d("MainActivity", "🎯 Immediate location obtained: ${location.latitude}, ${location.longitude}")
                    sendImmediateLocationToServer(location)

                    // Remover el callback después de obtener la ubicación
                    fusedLocationClient.removeLocationUpdates(this)
                }
            }
        }

        // Solicitar ubicación inmediata
        fusedLocationClient.requestLocationUpdates(
            immediateLocationRequest,
            immediateCallback,
            android.os.Looper.getMainLooper()
        )

        // Timeout de seguridad - si no hay respuesta en 8 segundos, usar última ubicación conocida
        lifecycleScope.launch {
            kotlinx.coroutines.delay(8000)
            fusedLocationClient.removeLocationUpdates(immediateCallback)

            // Intentar con última ubicación conocida
            fusedLocationClient.lastLocation.addOnSuccessListener { location ->
                if (location != null) {
                    android.util.Log.d("MainActivity", "🎯 Using last known location for immediate request")
                    sendImmediateLocationToServer(location)
                } else {
                    android.util.Log.e("MainActivity", "🚨 No location available for immediate request")
                }
            }
        }
    }

    private fun sendImmediateLocationToServer(location: android.location.Location) {
        val childId = preferenceManager.getChildId()
        if (childId == -1) {
            android.util.Log.e("MainActivity", "Child ID not found")
            return
        }

        lifecycleScope.launch {
            try {
                val timestamp = java.time.Instant.ofEpochMilli(System.currentTimeMillis()).toString()

                val locationData = com.example.corredores.models.LocationUpdateRequest(
                    latitude = location.latitude,
                    longitude = location.longitude,
                    accuracy = location.accuracy,
                    timestamp = timestamp
                )

                val response = com.example.corredores.network.NetworkClient.apiService.updateLocation(
                    childId = childId.toString(),
                    locationData = locationData
                )

                if (response.isSuccessful) {
                    android.util.Log.d("MainActivity", "🎯 IMMEDIATE location sent successfully (parent request)")
                    Toast.makeText(this@MainActivity, "Ubicación enviada al padre", Toast.LENGTH_SHORT).show()
                } else {
                    android.util.Log.e("MainActivity", "Failed to send immediate location: ${response.code()}")
                }

            } catch (e: Exception) {
                android.util.Log.e("MainActivity", "Error sending immediate location: ${e.message}")
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // No detener el servicio aquí, debe seguir corriendo en segundo plano
    }
}

/**
 * Interfaz JavaScript para comunicación entre WebView y app nativa
 */
class WebAppInterface(private val context: Context) {

    @JavascriptInterface
    fun sendNotification(notificationType: String, message: String = ""): String {
        android.util.Log.d("WebAppInterface", "Sending notification: $notificationType")

        val activity = context as MainActivity
        val childId = activity.preferenceManager.getChildId()

        if (childId == -1) {
            return "{\"success\": false, \"message\": \"Child ID not found\"}"
        }

        // Enviar notificación de forma asíncrona
        activity.lifecycleScope.launch {
            try {
                val notificationRequest = NotificationRequest(
                    notification_type = notificationType,
                    message_text = message.ifEmpty { null }
                )

                val response = NetworkClient.apiService.sendNotification(
                    childId = childId.toString(),
                    notificationData = notificationRequest
                )

                activity.runOnUiThread {
                    if (response.isSuccessful) {
                        Toast.makeText(context, "Notificación enviada", Toast.LENGTH_SHORT).show()
                        // Notificar al JavaScript que fue exitoso
                        activity.findViewById<WebView>(R.id.webview_dashboard_child)
                            .evaluateJavascript("window.notificationSent && window.notificationSent(true);", null)
                    } else {
                        Toast.makeText(context, "Error al enviar notificación", Toast.LENGTH_SHORT).show()
                        activity.findViewById<WebView>(R.id.webview_dashboard_child)
                            .evaluateJavascript("window.notificationSent && window.notificationSent(false);", null)
                    }
                }

            } catch (e: Exception) {
                android.util.Log.e("WebAppInterface", "Error sending notification: ${e.message}")
                activity.runOnUiThread {
                    Toast.makeText(context, "Error de conexión", Toast.LENGTH_SHORT).show()
                    activity.findViewById<WebView>(R.id.webview_dashboard_child)
                        .evaluateJavascript("window.notificationSent && window.notificationSent(false);", null)
                }
            }
        }

        return "{\"success\": true, \"message\": \"Notification queued\"}"
    }

    @JavascriptInterface
    fun getChildId(): String {
        val activity = context as MainActivity
        val childId = activity.preferenceManager.getChildId()
        return childId.toString()
    }

    @JavascriptInterface
    fun isLocationServiceActive(): Boolean {
        // Verificar si el servicio de ubicación está corriendo
        return true // Por simplicidad, asumimos que está activo
    }

    @JavascriptInterface
    fun requestImmediateLocation(): String {
        android.util.Log.d("WebAppInterface", "🚨 Immediate location requested by parent")

        val activity = context as MainActivity

        // Solicitar ubicación inmediata usando FusedLocationProviderClient
        activity.lifecycleScope.launch {
            try {
                activity.getImmediateLocation()
            } catch (e: Exception) {
                android.util.Log.e("WebAppInterface", "Error requesting immediate location: ${e.message}")
            }
        }

        return "{\"success\": true, \"message\": \"Location request sent\"}"
    }
}