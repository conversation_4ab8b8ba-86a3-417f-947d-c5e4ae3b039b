<!-- app/templates/incidents/auditar_incidentes.html -->
{% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">Auditoría de Incidencias</h2>
    <div class="table-responsive">
        <table id="incidentsTable" class="table table-bordered table-striped">
            <thead class="thead-dark">
                <tr>
                    <th>ID</th>
                    <th>Reporta</th>
                    <th>Resuelve</th>
                    <th>Acción</th>
                    <th>Detalles</th>
                    <th>Resolución</th>
                    <th>Fecha</th>
                </tr>
            </thead>
            <tbody>
                {% for incident in incidents %}
                <tr>
                    <td>{{ incident.incident_id }}</td>
                    <td>{{ incident.father.username }}</td>
                    <td>
                        {% if incident.operator %}
                            {{ incident.operator.username }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>
                        {% if incident.status == 'resolved' %}
                            Resolver Incidente
                        {% else %}
                            Report Incident
                        {% endif %}
                    </td>
                    <td>
                        Incidente ID {{ incident.incident_id }} reportado para Hijo ID {{ incident.child_id }}. Token: {{ incident.token }}
                    </td>
                    <td>
                        {% if incident.status == 'resolved' %}
                            {{ incident.resolution }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>
                        {% if incident.status == 'resolved' and incident.resolved_at %}
                            {{ incident.resolved_at.strftime("%d-%m-%Y %H:%M:%S") }}
                        {% else %}
                            {{ incident.reported_at.strftime("%d-%m-%Y %H:%M:%S") }}
                        {% endif %}
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="7" class="text-center">No se encontraron incidencias.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block scripts %}
  <!-- Dependencias de DataTables (asegúrate de que jQuery esté cargado) -->
  <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap4.min.css">
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
  <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap4.min.js"></script>

  <script>
    // Inicialización de DataTables para habilitar búsqueda y paginación
    $(document).ready(function() {
        $('#incidentsTable').DataTable({
            "pageLength": 25,         // Se muestran 25 registros por página
            "lengthChange": false,      // Se oculta el selector de cantidad de registros por página
            "language": {
                "search": "Buscar:",
                "zeroRecords": "No se encontraron resultados",
                "info": "Mostrando página _PAGE_ de _PAGES_",
                "infoEmpty": "No hay registros disponibles",
                "paginate": {
                    "first": "Primero",
                    "last": "Último",
                    "next": "Siguiente",
                    "previous": "Anterior"
                }
            }
        });
    });
  </script>
{% endblock %}
