# app/models.py
from app import db, login_manager
from flask_login import UserMixin
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash, check_password_hash
import secrets, string
# import json # No se usa directamente json aquí, se usa en las vistas/formularios para los datos encriptados
from sqlalchemy.dialects import mysql
# from sqlalchemy import Float # No se usa Float si los datos de ubicación van encriptados como Text

# --- Tablas de Asociación y Roles ---

class UserRoles(db.Model):
    __tablename__ = 'user_roles'
    user_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('users.user_id'), primary_key=True)
    role_id = db.Column(mysql.BIGINT(unsigned=True), db.<PERSON>ey('roles.role_id'), primary_key=True)
    assigned_at = db.Column(db.TIMESTAMP, default=datetime.now)

class Role(db.Model):
    __tablename__ = 'roles'
    role_id = db.Column(mysql.BIGINT(unsigned=True), primary_key=True)
    role_name = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.Text)
    users = db.relationship('User', secondary='user_roles', back_populates='roles')

# --- Modelos Principales ---

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    user_id = db.Column(mysql.BIGINT(unsigned=True), primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    first_name = db.Column(db.String(100))
    last_name = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    active = db.Column(db.Boolean, default=True)
    father_encrypted_key = db.Column(db.Text) # Clave maestra encriptada del padre
    created_at = db.Column(db.TIMESTAMP, default=datetime.now)
    updated_at = db.Column(db.TIMESTAMP, default=datetime.now, onupdate=datetime.now)
    telefono_hijo = db.Column(db.String(20)) # Teléfono asociado al hijo (si aplica)

    # Campos para roles específicos (Operador/Supervisor)
    hierarchy = db.Column(db.String(50))   # Jerarquía (ej. 'Comisario')
    file_number = db.Column(db.String(50)) # Legajo

    # Relaciones
    roles = db.relationship('Role', secondary='user_roles', back_populates='users')
    # Especificar foreign_keys para evitar ambigüedad si User tuviera múltiples FKs a Child (no es el caso aquí, pero buena práctica)
    children = db.relationship('Child', backref='parent', lazy='dynamic', foreign_keys='Child.parent_id')

    # Los backrefs desde otros modelos hacia User son:
    # SafeZone.father -> User.safe_zones
    # ChildNotification.father -> User.notifications_received
    # Incident.father -> User.incidents_reported
    # Incident.operator -> User.incidents_managed
    # OperatorRequest.father -> User.operator_requests_father
    # AuditLog.user -> User.audit_logs
    # Route.father -> User.routes (SI SIGUES USANDO EL MODELO Route, si no, este no aplica)


    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def get_id(self):
        return str(self.user_id)

    def has_role(self, role_name):
        return any(role.role_name == role_name for role in self.roles)

    def add_role(self, role_name):
        role = Role.query.filter_by(role_name=role_name).first()
        if role and role not in self.roles:
            self.roles.append(role)
            db.session.commit()

    def remove_role(self, role_name):
        role = Role.query.filter_by(role_name=role_name).first()
        if role and role in self.roles:
            self.roles.remove(role)
            db.session.commit()

class Child(db.Model):
    __tablename__ = 'children'
    child_id = db.Column(mysql.BIGINT(unsigned=True), primary_key=True)
    parent_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('users.user_id'), nullable=False)
    first_name = db.Column(db.String(100))
    last_name = db.Column(db.String(100))
    created_at = db.Column(db.TIMESTAMP, default=datetime.now)
    updated_at = db.Column(db.TIMESTAMP, default=datetime.now, onupdate=datetime.now)

    # El backref 'parent' está definido en User.children
    # Los backrefs desde otros modelos hacia Child son:
    # ChildZoneAssignment.child -> Child.zone_assignments
    # UserLocationLog.child -> Child.location_logs
    # ChildNotification.child -> Child.notifications_sent
    # Incident.child -> Child.incidents_affecting
    # OperatorRequest.child -> Child.operator_requests_child
    # Token.child -> Child.tokens
    # ChildRouteAssignment.child -> Child.route_assignments (SI SIGUES USANDO ChildRouteAssignment)

# --- Modelos para Rutas (si decides mantenerlos separados de SafeZone) ---
# SI EL ERROR "cannot import name 'Route'" ES PORQUE incidents.py LO USA, Y VOS
# YA NO QUERÉS USAR Route SINO SafeZone, ENTONCES ESTA SECCIÓN NO DEBERÍA ESTAR
# O DEBERÍA ESTAR ELIMINADA/COMENTADA. SI LA NECESITAS, DESCOMENTA Y AJUSTA.

class Route(db.Model):
    __tablename__ = 'routes'
    route_id = db.Column(mysql.BIGINT(unsigned=True), primary_key=True)
    father_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('users.user_id'), nullable=False)
    route_name = db.Column(db.String(100))
    encrypted_route_data = db.Column(db.Text, nullable=False) # GeoJSON encriptado
    schedule_start = db.Column(db.Time)
    schedule_end = db.Column(db.Time)
    created_at = db.Column(db.TIMESTAMP, default=datetime.now)
    updated_at = db.Column(db.TIMESTAMP, default=datetime.now, onupdate=datetime.now)

    father = db.relationship('User', backref='routes') # Crea User.routes
    # Relación con la tabla de asociación IncidentRoute
    incident_routes = db.relationship('IncidentRoute', back_populates='route', cascade='all, delete-orphan')
    # Relación many-to-many con Incident a través de IncidentRoute
    incidents = db.relationship('Incident', secondary='incident_routes', back_populates='routes', overlaps="incident_routes,route")
    assignments = db.relationship('ChildRouteAssignment', back_populates='route', cascade='all, delete-orphan')


class ChildRouteAssignment(db.Model):
    __tablename__ = 'child_route_assignments'
    __table_args__ = (db.UniqueConstraint('child_id', 'route_id', name='_child_route_uc'),)

    assignment_id = db.Column(mysql.BIGINT(unsigned=True), primary_key=True)
    child_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('children.child_id'), nullable=False)
    route_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('routes.route_id'), nullable=False)
    encrypted_assignment_details = db.Column(db.Text) # Opcional
    status = db.Column(db.String(50), default='assigned')
    assigned_at = db.Column(db.TIMESTAMP, default=datetime.now)

    child = db.relationship('Child', backref='route_assignments') # Crea Child.route_assignments
    route = db.relationship('Route', back_populates='assignments')


# --- SafeZone (Anteriormente Route en tu versión, pero si 'Route' sigue dando error, hay una confusión) ---

class SafeZone(db.Model):
    __tablename__ = 'safe_zones'
    zone_id = db.Column(mysql.BIGINT(unsigned=True), primary_key=True)
    father_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('users.user_id'), nullable=False)
    zone_name = db.Column(db.String(100))
    encrypted_zone_data = db.Column(db.Text, nullable=False) # JSON {'lat', 'lon', 'radius'} encriptado
    schedule_start = db.Column(db.Time)
    schedule_end = db.Column(db.Time)
    created_at = db.Column(db.TIMESTAMP, default=datetime.now)
    updated_at = db.Column(db.TIMESTAMP, default=datetime.now, onupdate=datetime.now)

    # Relaciones
    father = db.relationship('User', backref='safe_zones') # Crea User.safe_zones
    # Relación con la tabla de asociación IncidentZone
    incident_zones = db.relationship('IncidentZone', back_populates='zone', cascade='all, delete-orphan')
    # Relación many-to-many con Incident a través de IncidentZone
    incidents = db.relationship('Incident', secondary='incident_zones', back_populates='zones', overlaps="incident_zones,zone") # 'zones' aquí
    assignments = db.relationship('ChildZoneAssignment', back_populates='zone', cascade='all, delete-orphan')


# --- Perímetros de Propiedades ---

class PropertyPerimeter(db.Model):
    __tablename__ = 'property_perimeters'
    perimeter_id = db.Column(mysql.BIGINT(unsigned=True), primary_key=True)
    admin_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('users.user_id'), nullable=False)
    property_name = db.Column(db.String(100), nullable=False)
    property_address = db.Column(db.String(255))
    property_type = db.Column(db.String(50), default='house') # house, building, commercial, etc.
    encrypted_perimeter_data = db.Column(db.Text, nullable=False) # JSON con array de coordenadas del polígono
    perimeter_color = db.Column(db.String(7), default='#FF0000') # Color hex para visualización
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.TIMESTAMP, default=datetime.now)
    updated_at = db.Column(db.TIMESTAMP, default=datetime.now, onupdate=datetime.now)

    # Relaciones
    admin = db.relationship('User', backref='property_perimeters')


# --- Zonas de Vigilancia para Guardias ---

class GuardZone(db.Model):
    __tablename__ = 'guard_zones'
    zone_id = db.Column(mysql.BIGINT(unsigned=True), primary_key=True)
    admin_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('users.user_id'), nullable=False)
    guard_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('users.user_id'), nullable=True) # Puede no estar asignado inicialmente
    zone_name = db.Column(db.String(100), nullable=False)
    zone_description = db.Column(db.Text)
    encrypted_zone_data = db.Column(db.Text, nullable=False) # JSON con array de coordenadas del polígono
    zone_color = db.Column(db.String(7), default='#0000FF') # Color hex para visualización
    patrol_frequency = db.Column(db.Integer, default=60) # Minutos entre patrullajes
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.TIMESTAMP, default=datetime.now)
    updated_at = db.Column(db.TIMESTAMP, default=datetime.now, onupdate=datetime.now)

    # Relaciones
    admin = db.relationship('User', foreign_keys=[admin_id], backref='created_guard_zones')
    guard = db.relationship('User', foreign_keys=[guard_id], backref='assigned_guard_zones')


# --- Asignación de SafeZone a Child (Anteriormente ChildRouteAssignment) ---

class ChildZoneAssignment(db.Model):
    __tablename__ = 'child_zone_assignments'
    __table_args__ = (db.UniqueConstraint('child_id', 'zone_id', name='_child_zone_uc'),)

    assignment_id = db.Column(mysql.BIGINT(unsigned=True), primary_key=True)
    child_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('children.child_id'), nullable=False)
    zone_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('safe_zones.zone_id'), nullable=False)
    encrypted_assignment_details = db.Column(db.Text) # Opcional
    status = db.Column(db.String(50), default='assigned')
    assigned_at = db.Column(db.TIMESTAMP, default=datetime.now)

    # Configuración de notificaciones
    early_warning_minutes = db.Column(db.Integer, default=5) # Minutos antes para alerta temprana
    notifications_enabled = db.Column(db.Boolean, default=True)

    # Relaciones
    child = db.relationship('Child', backref='zone_assignments') # Crea Child.zone_assignments
    zone = db.relationship('SafeZone', back_populates='assignments')


# --- Seguimiento de Cumplimiento de Zonas ---
class ZoneComplianceLog(db.Model):
    __tablename__ = 'zone_compliance_logs'

    log_id = db.Column(mysql.BIGINT(unsigned=True), primary_key=True)
    assignment_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('child_zone_assignments.assignment_id'), nullable=False)
    child_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('children.child_id'), nullable=False)
    zone_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('safe_zones.zone_id'), nullable=False)

    # Información del día y horario
    compliance_date = db.Column(db.Date, nullable=False)
    expected_time = db.Column(db.Time, nullable=False) # Hora esperada de llegada
    actual_arrival_time = db.Column(db.Time) # Hora real de llegada (null si no llegó)

    # Estado del cumplimiento
    status = db.Column(db.String(50), default='pending') # pending, arrived, late, missed
    minutes_late = db.Column(db.Integer, default=0) # Minutos de retraso

    # Notificaciones enviadas
    early_warning_sent = db.Column(db.Boolean, default=False)
    late_notification_sent = db.Column(db.Boolean, default=False)
    missed_notification_sent = db.Column(db.Boolean, default=False)

    # Timestamps
    created_at = db.Column(db.TIMESTAMP, default=datetime.now)
    updated_at = db.Column(db.TIMESTAMP, default=datetime.now, onupdate=datetime.now)

    # Relaciones
    assignment = db.relationship('ChildZoneAssignment', backref='compliance_logs')
    child = db.relationship('Child', backref='zone_compliance_logs')
    zone = db.relationship('SafeZone', backref='compliance_logs')


# --- Logs de Ubicación ---

class UserLocationLog(db.Model):
    __tablename__ = 'user_location_logs'
    location_id = db.Column(mysql.BIGINT(unsigned=True), primary_key=True)
    child_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('children.child_id'), nullable=False)
    encrypted_location_data = db.Column(db.Text, nullable=False) # JSON {'lat', 'lng', ...} encriptado
    recorded_at = db.Column(db.TIMESTAMP, default=datetime.now)

    child = db.relationship('Child', backref='location_logs') # Crea Child.location_logs

# --- Notificaciones ---

class ChildNotification(db.Model):
    __tablename__ = 'child_notifications'
    notification_id = db.Column(mysql.BIGINT(unsigned=True), primary_key=True)
    child_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('children.child_id'), nullable=False)
    father_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('users.user_id'), nullable=False)
    notification_type = db.Column(db.String(100))
    message_text = db.Column(db.Text)
    created_at = db.Column(db.TIMESTAMP, default=datetime.now)
    viewed_at = db.Column(db.TIMESTAMP)

    child = db.relationship('Child', foreign_keys=[child_id], backref='notifications_sent') # Crea Child.notifications_sent
    father = db.relationship('User', foreign_keys=[father_id], backref='notifications_received') # Crea User.notifications_received

# --- Incidentes ---

class Incident(db.Model):
    __tablename__ = 'incidents'
    incident_id = db.Column(mysql.BIGINT(unsigned=True), primary_key=True)
    father_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('users.user_id'))
    child_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('children.child_id'))
    operator_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('users.user_id'))
    incident_type = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text)
    reported_at = db.Column(db.TIMESTAMP, default=datetime.now)
    status = db.Column(db.String(50), default='open')
    resolved_at = db.Column(db.TIMESTAMP)
    resolution = db.Column(db.Text)
    token = db.Column(db.String(6), unique=True)
    token_expiration = db.Column(db.TIMESTAMP)

    # Relaciones
    # Si Route sigue existiendo Y se quiere asociar a Incident
    incident_routes = db.relationship('IncidentRoute', back_populates='incident', cascade='all, delete-orphan')
    routes = db.relationship('Route', secondary='incident_routes', back_populates='incidents', overlaps="incident_routes,route")

    # Si SafeZone se quiere asociar a Incident
    incident_zones = db.relationship('IncidentZone', back_populates='incident', cascade='all, delete-orphan')
    zones = db.relationship('SafeZone', secondary='incident_zones', back_populates='incidents', overlaps="incident_zones,zone")

    father = db.relationship('User', foreign_keys=[father_id], backref='incidents_reported') # Crea User.incidents_reported
    child = db.relationship('Child', foreign_keys=[child_id], backref='incidents_affecting') # Crea Child.incidents_affecting
    operator = db.relationship('User', foreign_keys=[operator_id], backref='incidents_managed') # Crea User.incidents_managed

    def generate_token(self):
        alphabet = string.digits + string.ascii_uppercase
        while True:
            token = ''.join(secrets.choice(alphabet) for _ in range(6))
            if Incident.query.filter_by(token=token).first() is None:
                break
        self.token = token
        self.token_expiration = datetime.now() + timedelta(minutes=30)

    def is_token_valid(self):
        return self.token is not None and self.token_expiration is not None and datetime.now() < self.token_expiration

    def invalidate_token(self):
        self.token = None
        self.token_expiration = datetime.now() - timedelta(minutes=1)


# --- Asociación Incident-Route (SI USAS Route) ---
class IncidentRoute(db.Model):
    __tablename__ = 'incident_routes'
    incident_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('incidents.incident_id'), primary_key=True)
    route_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('routes.route_id'), primary_key=True)
    created_at = db.Column(db.TIMESTAMP, default=datetime.now)

    incident = db.relationship('Incident', back_populates='incident_routes', overlaps="incidents") # incidents en plural si es el many-to-many
    route = db.relationship('Route', back_populates='incident_routes', overlaps="routes") # routes en plural


# --- Asociación Incident-SafeZone ---
class IncidentZone(db.Model):
    __tablename__ = 'incident_zones'
    incident_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('incidents.incident_id'), primary_key=True)
    zone_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('safe_zones.zone_id'), primary_key=True)
    created_at = db.Column(db.TIMESTAMP, default=datetime.now)

    incident = db.relationship('Incident', back_populates='incident_zones')
    zone = db.relationship('SafeZone', back_populates='incident_zones')


# --- Solicitudes de Operador ---

class OperatorRequest(db.Model):
    __tablename__ = 'operator_requests'
    request_id = db.Column(mysql.BIGINT(unsigned=True), primary_key=True)
    incident_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('incidents.incident_id'), nullable=False)
    requested_at = db.Column(db.TIMESTAMP, default=datetime.now)
    approved_at = db.Column(db.TIMESTAMP)
    father_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('users.user_id'))
    child_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('children.child_id'))
    time_range_start = db.Column(db.TIMESTAMP)
    time_range_end = db.Column(db.TIMESTAMP)

    incident = db.relationship('Incident', backref='operator_requests') # Crea Incident.operator_requests
    father = db.relationship('User', foreign_keys=[father_id], backref='operator_requests_father') # Crea User.operator_requests_father
    child = db.relationship('Child', foreign_keys=[child_id], backref='operator_requests_child') # Crea Child.operator_requests_child

# --- Log de Auditoría ---

class AuditLog(db.Model):
    __tablename__ = 'audit_logs'
    audit_id = db.Column(mysql.BIGINT(unsigned=True), primary_key=True)
    user_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('users.user_id'))
    action = db.Column(db.String(255))
    details = db.Column(db.Text)
    created_at = db.Column(db.TIMESTAMP, default=datetime.now)

    user = db.relationship('User', backref='audit_logs') # Crea User.audit_logs

# --- Token de Vinculación ---

class Token(db.Model):
    __tablename__ = 'tokens'
    token_id = db.Column(mysql.BIGINT(unsigned=True), primary_key=True)
    token = db.Column(db.String(16), unique=True, nullable=False)
    child_id = db.Column(mysql.BIGINT(unsigned=True), db.ForeignKey('children.child_id'), nullable=False)
    expiration = db.Column(db.TIMESTAMP, nullable=False)
    used = db.Column(db.Boolean, default=False)
    device_info = db.Column(db.Text) # Opcional

    child = db.relationship('Child', backref='tokens') # Crea Child.tokens


# --- Tokens FCM para Notificaciones Push ---
class FCMToken(db.Model):
    __tablename__ = 'fcm_tokens'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=False)
    token = db.Column(db.String(255), nullable=False, unique=True)
    device_type = db.Column(db.String(20), nullable=False, default='android')  # android, ios, web
    is_active = db.Column(db.Boolean, default=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_used = db.Column(db.DateTime, default=datetime.utcnow)

    # Relación con usuario
    user = db.relationship('User', backref='fcm_tokens')

    def __repr__(self):
        return f'<FCMToken {self.user_id}-{self.device_type}>'

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'token': self.token,
            'device_type': self.device_type,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_used': self.last_used.isoformat() if self.last_used else None
        }


# --- Configuración de Flask-Login ---

@login_manager.user_loader
def load_user(user_id):
    try:
        return User.query.get(int(user_id))
    except ValueError: # Si user_id no es un entero válido
        return None

# --- Funciones de Utilidad (ej: crear roles por defecto) ---

def create_default_roles():
    """Crea roles por defecto si no existen en la base de datos."""
    default_roles = [
        ('ADMIN', 'Administrador con privilegios totales'),
        ('SUPERVISOR', 'Crea usuarios (Padre/Operador), asigna roles, audita'),
        ('OPERADOR', 'Gestiona incidencias, visualiza datos con token'),
        ('PADRE', 'Crea hijos, define zonas seguras y rutas, reporta incidencias'),
        #('HIJO', 'Rol para el usuario hijo, si se decidiera que los hijos son User') # Comentado, ya que Child es un modelo separado
    ]
    roles_added = []
    try:
        with db.session.no_autoflush:
            for role_name, description in default_roles:
                if not Role.query.filter_by(role_name=role_name).first():
                    new_role = Role(role_name=role_name, description=description)
                    db.session.add(new_role)
                    roles_added.append(role_name)
        if roles_added:
            db.session.commit()
            print(f"Roles por defecto añadidos: {', '.join(roles_added)}")
        # else:
            # print("Todos los roles por defecto ya existían.")
    except Exception as e:
        db.session.rollback()
        print(f"Error al crear roles por defecto: {e}")


# --- Bloque para Ejecución Directa (ej: inicializar roles) ---

if __name__ == '__main__':
    # Este bloque solo se ejecuta si models.py se corre como script principal
    # Lo cual no es lo usual en una app Flask estructurada (se hace vía manage.py o similar)
    # Pero es útil para pruebas rápidas o inicializaciones directas si fuera necesario.
    print("Intentando crear la aplicación para el contexto de models.py...")
    from app import create_app # Asegúrate de que esto no cause importaciones circulares si create_app importa modelos
    app = create_app()
    with app.app_context():
        print("Verificando/Creando roles por defecto desde models.py...")
        create_default_roles()
        print("Proceso de roles desde models.py finalizado.")