# 🔥 Configuración de Firebase Cloud Messaging (FCM)

## 📋 **PASOS PARA CONFIGURAR FIREBASE:**

### **1. 🌐 Crear Proyecto Firebase**
1. Ve a [Firebase Console](https://console.firebase.google.com/)
2. Clic en "Crear proyecto"
3. Nombre: `corredores-seguros`
4. Habilitar Google Analytics (opcional)

### **2. 📱 Agregar App Android**
1. En el proyecto Firebase, clic en "Agregar app" → Android
2. **Nombre del paquete**: `com.example.corredores`
3. **Nombre de la app**: `Corredores Seguros`
4. Descargar `google-services.json`
5. Colocar el archivo en: `app/google-services.json`

### **3. 🔑 Obtener Server Key**
1. En Firebase Console → Configuración del proyecto
2. Pestaña "Cloud Messaging"
3. Copiar "Clave del servidor" (Server Key)
4. Agregar a variables de entorno del servidor Flask

### **4. 🖥️ Configurar Servidor Flask**

Agregar a `.env` o variables de entorno:
```bash
FIREBASE_SERVER_KEY=tu_server_key_aqui
```

Actualizar `app/services/fcm_service.py`:
```python
import os
self.server_key = os.getenv('FIREBASE_SERVER_KEY')
```

### **5. 📱 Archivos Necesarios en Android**

**Ubicación del archivo de configuración:**
```
app/
├── google-services.json  ← Descargar de Firebase Console
└── src/main/
    ├── AndroidManifest.xml  ✅ Ya configurado
    └── java/com/example/corredores/
        ├── MainActivity.kt  ✅ Ya configurado
        ├── services/
        │   └── MyFirebaseMessagingService.kt  ✅ Ya creado
        └── utils/
            ├── FCMManager.kt  ✅ Ya creado
            └── PreferenceManager.kt  ✅ Ya actualizado
```

### **6. 🗄️ Base de Datos**

Ejecutar migración:
```bash
mysql -u usuario -p base_de_datos < migrate_fcm_tokens.sql
```

### **7. 🧪 Probar Notificaciones**

**Desde Firebase Console:**
1. Cloud Messaging → "Enviar tu primer mensaje"
2. Título: "Prueba"
3. Texto: "Notificación de prueba"
4. App: `com.example.corredores`

**Desde código (cuando un hijo no llega a zona):**
```python
from app.services.fcm_service import send_zone_notification_to_father

send_zone_notification_to_father(
    father_id=123,
    child_name="Juan",
    zone_name="Escuela",
    notification_type="zone_late_arrival"
)
```

## 🔧 **CONFIGURACIÓN ADICIONAL:**

### **Permisos Android (ya incluidos):**
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
```

### **Dependencias Gradle (ya incluidas):**
```kotlin
implementation(platform("com.google.firebase:firebase-bom:32.7.0"))
implementation("com.google.firebase:firebase-messaging-ktx")
implementation("com.google.firebase:firebase-analytics-ktx")
```

### **Servicio en Manifest (ya incluido):**
```xml
<service
    android:name=".services.MyFirebaseMessagingService"
    android:enabled="true"
    android:exported="false">
    <intent-filter>
        <action android:name="com.google.firebase.MESSAGING_EVENT" />
    </intent-filter>
</service>
```

## 🚀 **FLUJO COMPLETO:**

1. **👨‍👧‍👦 Padre se loguea** → App registra token FCM en servidor
2. **👶 Hijo no llega a zona** → Sistema detecta retraso
3. **🔔 Servidor envía notificación** → FCM → Dispositivo del padre
4. **📱 Padre recibe alerta** → Puede ver detalles en dashboard

## 🔍 **LOGS PARA DEBUG:**

**Android (Logcat):**
```
FCMService: Token FCM obtenido: [token]
FCMService: Notificación recibida: [título]
```

**Flask (logs):**
```
Token FCM registrado para padre 123
Notificación PUSH enviada: zone_late_arrival - Hijo 456 -> Padre 123
```

## ⚠️ **IMPORTANTE:**

- El archivo `google-services.json` es **OBLIGATORIO**
- La Server Key debe mantenerse **SECRETA**
- Los tokens FCM pueden cambiar, el sistema los actualiza automáticamente
- Las notificaciones solo funcionan en dispositivos con Google Play Services
