<!--app/templates/incidents/gestionar_incidentes.html-->

{% extends 'base.html' %}

{% block content %}
<h2>Gestionar Incidencias</h2>
<table border="1">
    <tr>
        <th>ID</th>
        <th>Padre</th>
        <th>Hijo</th>
        <th>Tipo</th>
        <th>Descripción</th>
        <th>Estado</th>
        <th>Reportado en</th>
        <th>Acciones</th>
    </tr>
    {% for incident in incidences %}
    <tr>
        <td>{{ incident.incident_id }}</td>
        <td>{{ incident.father.first_name }} {{ incident.father.last_name }}</td>
        <td>{{ incident.child.first_name }} {{ incident.child.last_name }}</td>
        <td>{{ incident.incident_type }}</td>
        <td>{{ incident.description }}</td>
        <td>{{ incident.status }}</td>
        <td>{{ incident.reported_at }}</td>
        <td>
            <!-- Acciones como 'Ver', 'Resolver' -->
            <a href="{{ url_for('incidents.ver_incidente', incident_id=incident.incident_id) }}" class="btn btn-primary">Ver</a>
            {% if 'OPERADOR' in roles and incident.status == 'open' %}
            <a href="{{ url_for('incidents.resolver_incidente', incident_id=incident.incident_id) }}" class="btn btn-primary">Resolver</a>
        {% endif %}
            </td>
    </tr>
    {% endfor %}
</table>
{% endblock %}
