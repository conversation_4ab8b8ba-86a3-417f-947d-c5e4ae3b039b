package com.example.corredores.services

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.RingtoneManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import com.example.corredores.MainActivity
import com.example.corredores.R
import com.example.corredores.utils.PreferenceManager
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage

/**
 * Servicio de Firebase Cloud Messaging para manejar notificaciones push
 * Especialmente diseñado para padres que reciben alertas de zonas
 */
class MyFirebaseMessagingService : FirebaseMessagingService() {

    companion object {
        private const val TAG = "FCMService"
        private const val CHANNEL_ID = "zone_notifications"
        private const val NOTIFICATION_ID = 1001
    }

    /**
     * Se ejecuta cuando se recibe un nuevo token FCM
     * Esto ocurre en la primera instalación y cuando el token se actualiza
     */
    override fun onNewToken(token: String) {
        super.onNewToken(token)
        Log.d(TAG, "Nuevo token FCM recibido: $token")
        
        // Enviar el token al servidor para asociarlo con el padre
        sendTokenToServer(token)
    }

    /**
     * Se ejecuta cuando se recibe una notificación push
     */
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        
        Log.d(TAG, "Notificación recibida de: ${remoteMessage.from}")
        
        // Verificar que el usuario sea un padre
        val preferenceManager = PreferenceManager(this)
        val userType = preferenceManager.getUserType()
        
        if (userType != PreferenceManager.USER_TYPE_PARENT) {
            Log.d(TAG, "Usuario no es padre, ignorando notificación")
            return
        }
        
        // Procesar datos de la notificación
        val title = remoteMessage.notification?.title ?: "Alerta de Zona"
        val body = remoteMessage.notification?.body ?: "Nueva notificación disponible"
        val notificationType = remoteMessage.data["type"] ?: "zone_notification"
        val childName = remoteMessage.data["child_name"] ?: ""
        
        Log.d(TAG, "Mostrando notificación: $title - $body")
        
        // Mostrar la notificación
        showNotification(title, body, notificationType, childName)
    }

    /**
     * Envía el token FCM al servidor para asociarlo con el padre logueado
     */
    private fun sendTokenToServer(token: String) {
        val preferenceManager = PreferenceManager(this)
        val userType = preferenceManager.getUserType()
        val userId = preferenceManager.getUserId()
        val authToken = preferenceManager.getAuthToken()
        
        // Solo enviar si es un padre logueado
        if (userType == PreferenceManager.USER_TYPE_PARENT && userId != -1 && !authToken.isNullOrEmpty()) {
            Log.d(TAG, "Enviando token FCM al servidor para padre ID: $userId")
            
            // TODO: Implementar llamada HTTP al servidor
            // Por ahora solo logueamos
            Log.d(TAG, "Token a enviar: $token")
        } else {
            Log.d(TAG, "No se envía token - Usuario no es padre logueado")
        }
    }

    /**
     * Muestra una notificación local en el dispositivo
     */
    private fun showNotification(title: String, body: String, type: String, childName: String) {
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        
        // Crear canal de notificación (requerido para Android 8.0+)
        createNotificationChannel(notificationManager)
        
        // Intent para abrir la app cuando se toque la notificación
        val intent = Intent(this, MainActivity::class.java).apply {
            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            putExtra("notification_type", type)
            putExtra("child_name", childName)
        }
        
        val pendingIntent = PendingIntent.getActivity(
            this, 
            0, 
            intent, 
            PendingIntent.FLAG_ONE_SHOT or PendingIntent.FLAG_IMMUTABLE
        )
        
        // Configurar sonido y vibración
        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        
        // Seleccionar icono según el tipo de notificación
        val iconRes = when (type) {
            "zone_early_warning" -> android.R.drawable.ic_dialog_alert
            "zone_late_arrival" -> android.R.drawable.ic_dialog_alert
            "zone_missed" -> android.R.drawable.ic_dialog_alert
            else -> android.R.drawable.ic_dialog_info
        }
        
        // Construir la notificación
        val notificationBuilder = NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(iconRes)
            .setContentTitle(title)
            .setContentText(body)
            .setAutoCancel(true)
            .setSound(defaultSoundUri)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_ALARM)
            .setVibrate(longArrayOf(0, 1000, 500, 1000)) // Patrón de vibración
        
        // Expandir texto si es muy largo
        if (body.length > 50) {
            notificationBuilder.setStyle(
                NotificationCompat.BigTextStyle().bigText(body)
            )
        }
        
        // Agregar información adicional si hay nombre del hijo
        if (childName.isNotEmpty()) {
            notificationBuilder.setSubText("Hijo: $childName")
        }
        
        // Mostrar la notificación
        notificationManager.notify(NOTIFICATION_ID, notificationBuilder.build())
        
        Log.d(TAG, "Notificación mostrada exitosamente")
    }

    /**
     * Crea el canal de notificación para Android 8.0+
     */
    private fun createNotificationChannel(notificationManager: NotificationManager) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Notificaciones de Zonas",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Alertas cuando los hijos no llegan a las zonas asignadas"
                enableVibration(true)
                vibrationPattern = longArrayOf(0, 1000, 500, 1000)
                setSound(
                    RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION),
                    null
                )
            }
            
            notificationManager.createNotificationChannel(channel)
        }
    }
}
