<!-- app/templates/routes/ver_rutas.html -->
{% extends 'base.html' %}

{% block content %}
<h2>Mis Rutas de Corredor Seguro</h2>
<table border="1">
    <tr>
        <th>Nombre de la Ruta</th>
        <th>Horar<PERSON></th>
        <th>Asignada</th>
        <th>Acciones</th>
    </tr>
    {% for route in routes %}
    <tr>
        <td>{{ route.route_name }}</td>
        <td>{{ route.schedule_start }} - {{ route.schedule_end }}</td>
        <td>
            {% if route.assignments %}
                {% for assignment in route.assignments %}
                    {{ assignment.child.first_name }} {{ assignment.child.last_name }}{% if not loop.last %}, {% endif %}
                {% endfor %}
            {% else %}
                No asignada
            {% endif %}
        </td>
        <td>
            <!-- Formulario para asignar la ruta a un hijo -->
            <form method="POST" action="{{ url_for('routes.asignar_ruta', route_id=route.route_id) }}" style="display:inline;">
                {{ form_asignar.hidden_tag() }}
                <select name="child_id">
                    {% for child in current_user.children %}
                        <option value="{{ child.child_id }}">{{ child.first_name }} {{ child.last_name }}</option>
                    {% endfor %}
                </select>
                <button type="submit" class="btn btn-secondary">Asignar a Hijo</button>
            </form>
            <!-- Contenedor para los botones Editar y Eliminar -->
            <div style="display:inline-block; margin-left: 10px;">
                <a href="{{ url_for('routes.editar_ruta', route_id=route.route_id) }}" class="btn btn-danger">Editar</a>
                <form method="POST" action="{{ url_for('routes.eliminar_ruta', route_id=route.route_id) }}" style="display:inline;" onsubmit="return confirm('¿Está seguro de eliminar la ruta?');">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger" style="margin-left: 5px;">Eliminar</button>
                  </form>
            </div>
        </td>
    </tr>
    {% endfor %}
</table>
<a href="{{ url_for('routes.crear_ruta') }}" class="btn btn-secondary">Crear Nueva Ruta</a>
{% endblock %}
