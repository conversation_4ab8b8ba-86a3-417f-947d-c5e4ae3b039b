# manage.py
import os
# from flask_migrate import Migrate # Se moverá dentro de create_my_app
from dotenv import load_dotenv
# import eventlet  # Comentado para entorno local sin eventlet
import ssl

load_dotenv()

# NO IMPORTAR MODELOS NI app, db, socketio AQUÍ ARRIBA TODAVÍA

def create_my_app():
    # Importar Flask app factory, db, socketio y Migrate DENTRO de la función
    from app import create_app, db, socketio
    from flask_migrate import Migrate

    app = create_app()  # Crear la instancia de la app
    migrate = Migrate(app, db) # Crear instancia de Migrate aquí

    # AHORA, importar los modelos DESPUÉS de que app y db estén inicializados
    from app.models import (
        Role, User, Child, Token, UserLocationLog,
        ChildNotification, Incident, OperatorRequest, AuditLog
        # SafeZone, ChildZoneAssignment, IncidentZone, PropertyPerimeter, GuardZone # Temporalmente comentados
    )
    # Importar otras dependencias necesarias para las funciones de este archivo
    from werkzeug.security import generate_password_hash
    from datetime import datetime

    @app.shell_context_processor
    def make_shell_context():
        return {
            'db': db,
            'Role': Role,
            'User': User,
            'Child': Child,
            'Token': Token,
            'UserLocationLog': UserLocationLog,
            'ChildNotification': ChildNotification,
            'Incident': Incident,
            'OperatorRequest': OperatorRequest,
            'AuditLog': AuditLog
            # 'SafeZone': SafeZone, 'ChildZoneAssignment': ChildZoneAssignment, 'IncidentZone': IncidentZone # Temporalmente comentados
        }

    @app.cli.command("init_data")
    def init_data():
        """Initializes the database with default data (roles and admin user)."""
        with app.app_context():
            # Create roles (if they don't exist)
            roles = {
                'ADMIN': 'Administrador con privilegios totales',
                'SUPERVISOR': 'Crea usuarios y asigna roles, audita incidencias',
                'OPERADOR': 'Gestiona incidencias reportadas, previa autorización de Padre',
                'PADRE': 'Crea hijos, define zonas seguras, reporta incidencias', # Mantenida tu descripción
            }
            for role_name, description in roles.items():
                role = Role.query.filter_by(role_name=role_name).first()
                if not role:
                    role = Role(role_name=role_name, description=description)
                    db.session.add(role)
                    print(f"Added role: {role_name}")

            db.session.commit()
            print("Roles initialized.")

            # Create admin user (if it doesn't exist)
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                admin_password = generate_password_hash('isaias52') # ¡USA UNA CONTRASEÑA FUERTE EN PRODUCCIÓN!
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    password_hash=admin_password,
                    first_name='Admin',
                    last_name='User',
                    active=True
                )
                admin_role = Role.query.filter_by(role_name='ADMIN').first()
                if admin_role:
                    admin_user.roles.append(admin_role)
                db.session.add(admin_user)
                db.session.commit()
                print("Admin user created.")
            else:
                print("Admin user already exists.")
    return app

if __name__ == '__main__':
    app = create_my_app() # Llama a la función para obtener la instancia de la app.

    # Obtener la instancia de SocketIO
    from app import socketio

    # Rutas de certificados (mantenidas como en tu versión)
    cert_path = '/etc/letsencrypt/live/patagoniaservers.com.ar/fullchain.pem'
    key_path = '/etc/letsencrypt/live/patagoniaservers.com.ar/privkey.pem'

    # Verificar si existen los certificados SSL
    ssl_available = os.path.exists(cert_path) and os.path.exists(key_path)

    if ssl_available:
        print(f"Certificados SSL encontrados en: {cert_path} y {key_path}")
    else:
        print(f"ADVERTENCIA: No se encontraron los archivos de certificado SSL en las rutas especificadas.")
        print("El servidor se iniciará en HTTP en lugar de HTTPS.")

    try:
        # OPCIÓN 1: Intentar con SSL directo (versiones nuevas de Flask-SocketIO)
        if ssl_available:
            print(f"Iniciando servidor SocketIO SEGURO (HTTPS) en https://0.0.0.0:5004")
            try:
                # Intentar método nuevo primero
                ssl_context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
                ssl_context.load_cert_chain(cert_path, key_path)
                socketio.run(app, host='0.0.0.0', port=5004, debug=False, ssl_context=ssl_context)
            except TypeError:
                # Fallback a método antiguo
                print("Intentando método SSL alternativo...")
                socketio.run(app, host='0.0.0.0', port=5004, debug=False, certfile=cert_path, keyfile=key_path)
        else:
            print(f"Iniciando servidor SocketIO INSEGURO (HTTP) en http://0.0.0.0:5004")
            socketio.run(app, host='0.0.0.0', port=5004, debug=False)

    except TypeError as e:
        if 'certfile' in str(e) or 'ssl_context' in str(e):
            print(f"⚠️  Flask-SocketIO no soporta SSL directo: {e}")
            print("🔄 Iniciando en HTTP (usar proxy reverso para HTTPS)...")
            print("💡 Recomendación: Configurar Nginx/Apache como proxy SSL")
            try:
                socketio.run(app, host='127.0.0.1', port=5004, debug=False)
            except Exception as fallback_error:
                print(f"Error también sin SSL: {fallback_error}")
                print("Iniciando con Flask básico...")
                app.run(host='127.0.0.1', port=5004, debug=False)
        else:
            raise e
    except FileNotFoundError:
         print(f"ERROR CRÍTICO: No se encontraron los archivos de certificado SSL.")
         print(f"Certificado esperado: {cert_path}")
         print(f"Clave esperada: {key_path}")
         print("Asegúrate de que las rutas sean correctas y los archivos existan.")
    except ssl.SSLError as e:
         print(f"ERROR de SSL: {e}")
         print("Verifica que los archivos de certificado y clave sean válidos y coincidan.")
    except Exception as e:
        print(f"Error inesperado al iniciar el servidor: {e}")
        print("Intentando iniciar sin SSL...")
        try:
            socketio.run(app, host='0.0.0.0', port=5004, debug=False)
        except Exception as fallback_error:
            print(f"Error también sin SSL: {fallback_error}")
            print("Iniciando con Flask básico...")
            app.run(host='0.0.0.0', port=5004, debug=False)