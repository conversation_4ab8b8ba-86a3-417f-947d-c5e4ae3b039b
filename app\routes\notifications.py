# app/routes/notifications.py

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app, g
from flask_login import login_required, current_user
from app import db
# from app.models import ChildNotification, ParentChild, User REMOVED
from app.models import ChildNotification, User, Child # ADDED Child
from app.forms.notification_forms import SendNotificationForm
from app.utils.encryption import Encryption
from datetime import datetime
import os
from .users import child_required  # Import the child_required decorator

notifications_bp = Blueprint('notifications', __name__, template_folder='templates/notifications')

@notifications_bp.route('/notificaciones', methods=['GET', 'POST'])
@login_required
def ver_notificaciones():
    # Obtener roles del usuario
    roles = [role.role_name for role in current_user.roles]
    if 'PADRE' in roles:
        # Solo se muestran las notificaciones que aún no han sido marcadas como leídas
        notifications = ChildNotification.query.filter_by(father_id=current_user.user_id, viewed_at=None).order_by(ChildNotification.created_at.desc()).all()
        return render_template('notifications/ver_notificaciones.html', notifications=notifications)
    # Removed HIJO logic from here.
    else:
        flash('No tienes permiso para ver esta sección.', 'danger')
        return redirect(url_for('users.dashboard'))

@notifications_bp.route('/marcar_leido/<int:notification_id>', methods=['POST'])
@login_required
def marcar_leido(notification_id):
    """
    Ruta para marcar una notificación como leída (visto).
    """
    notification = ChildNotification.query.get_or_404(notification_id)
    # Verificar que la notificación corresponde al padre logueado
    if notification.father_id != current_user.user_id:
        return jsonify({'message': 'No autorizado'}), 403
    # Marcar la notificación como leída estableciendo el timestamp actual
    notification.viewed_at = datetime.now()
    try:
        db.session.commit()
        return jsonify({'message': 'Notificación marcada como leída'}), 200
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error al marcar notificación {notification_id} como leída: {e}", exc_info=True)
        return jsonify({'message': 'Error al marcar notificación como leída'}), 500    

@notifications_bp.route('/send_notification', methods=['POST'])
# @login_required REMOVED
@child_required  # ADDED - Use the custom decorator
def send_notification():
    """
    Ruta para recibir y guardar notificaciones del hijo al padre.
    """
    data = request.get_json()
    if not data:
        return jsonify({'message': 'No JSON data received'}), 400

    notification_type = data.get('notification_type')
    if not notification_type:
        return jsonify({'message': 'Notification type is required'}), 400

    # child_id = current_user.user_id  # El usuario actual (Hijo) es quien envía la notificación REMOVED
    # parent_child = ParentChild.query.filter_by(child_id=child_id).first()  # Buscar la relación Padre-Hijo REMOVED
    # if not parent_child: REMOVED
    #     return jsonify({'message': 'Parent relationship not found for current user'}), 400 REMOVED

    # father_id = parent_child.parent_id  # Obtener el ID del padre de la relación REMOVED
    child_id = g.child.child_id  # Get child_id from cookie (g.child set by @child_required)
    father_id = g.child.parent_id # ADDED

    try:
        notification = ChildNotification(
            child_id=child_id,
            father_id=father_id,
            notification_type=notification_type,
            message_text=f'Notificación de tipo "{notification_type}" del hijo.'  # Mensaje de texto básico
        )
        db.session.add(notification)
        db.session.commit()
        return jsonify({'message': 'Notification sent successfully'}), 200
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error sending notification from child {child_id} to father {father_id}: {e}", exc_info=True)
        return jsonify({'message': 'Error sending notification'}), 500