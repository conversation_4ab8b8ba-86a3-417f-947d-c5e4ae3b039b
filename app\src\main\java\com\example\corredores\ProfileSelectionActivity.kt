package com.example.corredores

import android.content.Intent
import android.os.Bundle
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity
import com.example.corredores.utils.PreferenceManager

class ProfileSelectionActivity : AppCompatActivity() {

    private lateinit var preferenceManager: PreferenceManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_profile_selection)

        preferenceManager = PreferenceManager(this)

        // Si ya se seleccionó un perfil, redirigir directamente
        if (preferenceManager.hasSelectedProfile()) {
            redirectToAppropriateScreen()
            return
        }

        setupViews()
    }

    private fun setupViews() {
        val btnParent = findViewById<Button>(R.id.btn_parent)
        val btnChild = findViewById<Button>(R.id.btn_child)

        btnParent.setOnClickListener {
            selectProfile(PreferenceManager.USER_TYPE_PARENT)
        }

        btnChild.setOnClickListener {
            selectProfile(PreferenceManager.USER_TYPE_CHILD)
        }
    }

    private fun selectProfile(userType: String) {
        preferenceManager.setUserType(userType)

        val intent = when (userType) {
            PreferenceManager.USER_TYPE_PARENT -> {
                Intent(this, MainActivity::class.java)
            }
            PreferenceManager.USER_TYPE_CHILD -> {
                Intent(this, TokenActivity::class.java)
            }
            else -> return
        }

        startActivity(intent)
        finish()
    }

    private fun redirectToAppropriateScreen() {
        val intent = if (preferenceManager.isLoggedIn()) {
            Intent(this, MainActivity::class.java)
        } else {
            when (preferenceManager.getUserType()) {
                PreferenceManager.USER_TYPE_PARENT -> Intent(this, MainActivity::class.java)
                PreferenceManager.USER_TYPE_CHILD -> Intent(this, TokenActivity::class.java)
                else -> return // No debería llegar aquí
            }
        }
        startActivity(intent)
        finish()
    }
}
