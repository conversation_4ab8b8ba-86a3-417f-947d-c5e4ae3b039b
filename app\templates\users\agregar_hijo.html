<!-- app/templates/users/agregar_hijo.html -->
{% extends 'base.html' %}

{% block content %}
<h2>Agregar Hijo</h2>
<form method="POST" action="{{ url_for('users.agregar_hijo') }}">
    {{ form.hidden_tag() }}
    <div>
        {{ form.first_name.label }}<br>
        {{ form.first_name(size=64) }}<br>
        {% for error in form.first_name.errors %}
            <span style="color: red;">[{{ error }}]</span>
        {% endfor %}
    </div>
    <div>
        {{ form.last_name.label }}<br>
        {{ form.last_name(size=64) }}<br>
        {% for error in form.last_name.errors %}
            <span style="color: red;">[{{ error }}]</span>
        {% endfor %}
    </div>
    <div>
        {{ form.telefono_hijo.label }}<br>
        {{ form.telefono_hijo(size=20) }}<br>
        {% for error in form.telefono_hijo.errors %}
            <span style="color: red;">[{{ error }}]</span>
        {% endfor %}
    </div>
    <div>
        {{ form.submit() }}
    </div>
</form>

{% if token %}  {# ADDED - Display token if it exists #}
  <div class="alert alert-success border-0 shadow-sm">
    <div class="d-flex align-items-center mb-3">
      <i class="fas fa-check-circle text-success me-2" style="font-size: 1.5rem;"></i>
      <h5 class="mb-0 text-success">¡Hijo agregado exitosamente!</h5>
    </div>

    <p class="mb-3"><strong>Token de vinculación generado:</strong></p>

    <!-- Token Display Card -->
    <div class="card border-primary shadow-sm mb-3" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
      <div class="card-header bg-primary text-white">
        <h6 class="mb-0"><i class="fas fa-key me-2"></i>Token de Vinculación</h6>
      </div>
      <div class="card-body p-4">
        <div class="row align-items-center">
          <div class="col-md-8 mb-3 mb-md-0">
            <div class="token-container p-3 rounded" style="background-color: #ffffff; border: 2px solid #0d6efd; box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);">
              <input type="text"
                     id="tokenDisplay"
                     value="{{ token }}"
                     readonly
                     class="form-control-plaintext text-center fw-bold"
                     style="font-family: 'Courier New', monospace;
                            font-size: 1.4rem;
                            letter-spacing: 3px;
                            color: #212529 !important;
                            background-color: transparent !important;
                            border: none !important;
                            padding: 10px;
                            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);">
            </div>
          </div>
          <div class="col-md-4 text-center">
            <button id="copyButton"
                    class="btn btn-primary btn-lg w-100"
                    type="button"
                    style="min-height: 60px; font-size: 1.1rem;">
              <i class="fas fa-copy me-2"></i>
              <span>Copiar Token</span>
            </button>
          </div>
        </div>

        <!-- Token Info -->
        <div class="mt-3 p-2 bg-light rounded">
          <small class="text-muted">
            <i class="fas fa-clock me-1"></i>
            <strong>Válido por 24 horas</strong> •
            <i class="fas fa-shield-alt me-1"></i>
            <strong>Uso único</strong> •
            <i class="fas fa-mobile-alt me-1"></i>
            <strong>Para app móvil</strong>
          </small>
        </div>
      </div>
    </div>

    <!-- Instructions -->
    <div class="alert alert-info border-0 mb-0">
      <div class="d-flex align-items-start">
        <i class="fas fa-info-circle text-info me-2 mt-1"></i>
        <div>
          <strong>Instrucciones:</strong>
          <ul class="mb-0 mt-2">
            <li>Comparte este token con tu hijo</li>
            <li>El token expira en <strong>24 horas</strong></li>
            <li>Tu hijo debe ingresarlo en la aplicación móvil</li>
            <li>Una vez usado, el token se invalidará automáticamente</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      var copyButton = document.getElementById('copyButton');
      var tokenInput = document.getElementById('tokenDisplay');

      if (copyButton && tokenInput) {
        copyButton.addEventListener('click', function() {
          // Seleccionar el texto del token
          tokenInput.select();
          tokenInput.setSelectionRange(0, 99999); // Para dispositivos móviles

          // Función para mostrar feedback exitoso
          function showSuccess() {
            copyButton.innerHTML = '<i class="fas fa-check me-2"></i><span>¡Copiado!</span>';
            copyButton.classList.remove('btn-primary');
            copyButton.classList.add('btn-success');

            // Agregar efecto visual al contenedor del token
            var tokenContainer = tokenInput.closest('.token-container');
            tokenContainer.style.borderColor = '#198754';
            tokenContainer.style.backgroundColor = '#f8fff9';

            // Restaurar después de 3 segundos
            setTimeout(function() {
              copyButton.innerHTML = '<i class="fas fa-copy me-2"></i><span>Copiar Token</span>';
              copyButton.classList.remove('btn-success');
              copyButton.classList.add('btn-primary');

              // Restaurar estilo del contenedor
              tokenContainer.style.borderColor = '#0d6efd';
              tokenContainer.style.backgroundColor = '#ffffff';
            }, 3000);
          }

          // Intentar copiar usando la API moderna
          if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(tokenInput.value).then(function() {
              showSuccess();
            }).catch(function(err) {
              console.error('Error al copiar con clipboard API:', err);
              // Fallback al método tradicional
              fallbackCopy();
            });
          } else {
            // Fallback para navegadores que no soportan clipboard API
            fallbackCopy();
          }

          function fallbackCopy() {
            try {
              document.execCommand('copy');
              showSuccess();
            } catch (err) {
              console.error('Error al copiar:', err);
              // Mostrar mensaje de error
              copyButton.innerHTML = '<i class="fas fa-exclamation me-2"></i><span>Error</span>';
              copyButton.classList.remove('btn-primary');
              copyButton.classList.add('btn-danger');

              setTimeout(function() {
                copyButton.innerHTML = '<i class="fas fa-copy me-2"></i><span>Copiar Token</span>';
                copyButton.classList.remove('btn-danger');
                copyButton.classList.add('btn-primary');
              }, 3000);
            }
          }
        });

        // Agregar efecto hover al token
        tokenInput.addEventListener('mouseenter', function() {
          this.closest('.token-container').style.transform = 'scale(1.02)';
          this.closest('.token-container').style.transition = 'transform 0.2s ease';
        });

        tokenInput.addEventListener('mouseleave', function() {
          this.closest('.token-container').style.transform = 'scale(1)';
        });
      }
    });
  </script>
{% endif %}

{% endblock %}