package com.example.corredores

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.EditText
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.corredores.network.ApiService
import com.example.corredores.network.RetrofitClient
import com.example.corredores.utils.PreferenceManager
import kotlinx.coroutines.launch
import retrofit2.Response

/**
 * Actividad de login para padres
 * Autentica con folio y contraseña contra el servidor
 */
class LoginActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "LoginActivity"
    }

    private lateinit var editTextFolio: EditText
    private lateinit var editTextPassword: EditText
    private lateinit var buttonLogin: Button
    private lateinit var progressBar: ProgressBar
    private lateinit var textViewError: TextView
    
    private lateinit var preferenceManager: PreferenceManager
    private lateinit var apiService: ApiService

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_login)

        preferenceManager = PreferenceManager(this)
        apiService = RetrofitClient.getApiService()

        // Verificar si ya está logueado
        if (preferenceManager.isLoggedIn()) {
            redirectToMainActivity()
            return
        }

        initializeViews()
        setupLoginButton()
    }

    private fun initializeViews() {
        editTextFolio = findViewById(R.id.edit_text_folio)
        editTextPassword = findViewById(R.id.edit_text_password)
        buttonLogin = findViewById(R.id.button_login)
        progressBar = findViewById(R.id.progress_bar)
        textViewError = findViewById(R.id.text_view_error)
    }

    private fun setupLoginButton() {
        buttonLogin.setOnClickListener {
            val folio = editTextFolio.text.toString().trim()
            val password = editTextPassword.text.toString().trim()

            if (validateInput(folio, password)) {
                performLogin(folio, password)
            }
        }
    }

    private fun validateInput(folio: String, password: String): Boolean {
        if (folio.isEmpty()) {
            showError("Por favor ingrese su folio")
            return false
        }

        if (password.isEmpty()) {
            showError("Por favor ingrese su contraseña")
            return false
        }

        return true
    }

    private fun performLogin(folio: String, password: String) {
        showLoading(true)
        showError("")

        lifecycleScope.launch {
            try {
                val loginRequest = mapOf(
                    "folio" to folio,
                    "password" to password
                )

                val response: Response<Map<String, Any>> = apiService.login(loginRequest)

                if (response.isSuccessful) {
                    val responseBody = response.body()
                    if (responseBody != null && responseBody["status"] == "success") {
                        handleLoginSuccess(responseBody, folio)
                    } else {
                        val message = responseBody?.get("message") as? String ?: "Error de login"
                        showError(message)
                    }
                } else {
                    showError("Error de conexión: ${response.code()}")
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error en login", e)
                showError("Error de conexión. Verifique su internet.")
            } finally {
                showLoading(false)
            }
        }
    }

    private fun handleLoginSuccess(responseBody: Map<String, Any>, folio: String) {
        try {
            val token = responseBody["token"] as? String
            val userId = (responseBody["user_id"] as? Double)?.toInt()
            val userName = responseBody["user_name"] as? String

            if (token != null && userId != null) {
                // Guardar datos de sesión
                preferenceManager.setLoggedIn(true)
                preferenceManager.setAuthToken(token)
                preferenceManager.setUserData(userId, folio, userName)

                Log.d(TAG, "Login exitoso para padre: $userId")
                
                Toast.makeText(this, "Bienvenido ${userName ?: folio}", Toast.LENGTH_SHORT).show()
                
                redirectToMainActivity()
            } else {
                showError("Respuesta del servidor incompleta")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error procesando respuesta de login", e)
            showError("Error procesando respuesta del servidor")
        }
    }

    private fun redirectToMainActivity() {
        val intent = Intent(this, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    private fun showLoading(show: Boolean) {
        progressBar.visibility = if (show) android.view.View.VISIBLE else android.view.View.GONE
        buttonLogin.isEnabled = !show
        editTextFolio.isEnabled = !show
        editTextPassword.isEnabled = !show
    }

    private fun showError(message: String) {
        textViewError.text = message
        textViewError.visibility = if (message.isEmpty()) android.view.View.GONE else android.view.View.VISIBLE
    }

    override fun onBackPressed() {
        // Permitir volver a selección de perfil solo si no está logueado
        if (!preferenceManager.isLoggedIn()) {
            super.onBackPressed()
        }
    }
}
