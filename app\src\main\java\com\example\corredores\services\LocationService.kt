package com.example.corredores.services

import android.Manifest
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.location.Location
import android.os.BatteryManager
import android.os.Build
import android.os.IBinder
import android.os.Looper
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import com.example.corredores.MainActivity
import com.example.corredores.R
import com.example.corredores.models.LocationUpdateRequest
import com.example.corredores.network.NetworkClient
import com.example.corredores.utils.PreferenceManager
import com.google.android.gms.location.*
import kotlinx.coroutines.*

class LocationService : Service() {

    companion object {
        private const val TAG = "LocationService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "location_service_channel"
        private const val LOCATION_UPDATE_INTERVAL = 30000L // 30 segundos
        private const val FASTEST_UPDATE_INTERVAL = 15000L // 15 segundos

        fun startService(context: Context) {
            val intent = Intent(context, LocationService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        fun stopService(context: Context) {
            val intent = Intent(context, LocationService::class.java)
            context.stopService(intent)
        }

        fun requestImmediateLocation(context: Context) {
            val intent = Intent(context, LocationService::class.java)
            intent.action = "REQUEST_IMMEDIATE_LOCATION"
            context.startService(intent)
        }
    }

    private lateinit var fusedLocationClient: FusedLocationProviderClient
    private lateinit var locationRequest: LocationRequest
    private lateinit var locationCallback: LocationCallback
    private lateinit var preferenceManager: PreferenceManager

    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var demandPollingJob: Job? = null
    private var currentPollingInterval = 30000L // Intervalo dinámico de polling
    private var lastUrgentRequest = 0L // Timestamp de última solicitud urgente

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "LocationService created")

        preferenceManager = PreferenceManager(this)
        fusedLocationClient = LocationServices.getFusedLocationProviderClient(this)

        createNotificationChannel()
        createLocationRequest()
        createLocationCallback()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "LocationService started with action: ${intent?.action}")

        // Verificar que sea un hijo
        if (preferenceManager.getUserType() != PreferenceManager.USER_TYPE_CHILD) {
            Log.w(TAG, "Service started but user is not a child")
            stopSelf()
            return START_NOT_STICKY
        }

        when (intent?.action) {
            "REQUEST_IMMEDIATE_LOCATION" -> {
                Log.d(TAG, "🚨 Immediate location requested by parent")
                requestImmediateLocationNow()
                return START_NOT_STICKY
            }
            else -> {
                startForeground(NOTIFICATION_ID, createNotification())
                startLocationUpdates()
                registerConnectionWithServer()
                startDemandPolling()
                return START_STICKY // Reiniciar si el sistema mata el servicio
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "LocationService destroyed")
        stopLocationUpdates()
        demandPollingJob?.cancel()
        serviceScope.cancel()
    }

    override fun onBind(intent: Intent?): IBinder? = null

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Seguimiento de Ubicación",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Servicio de seguimiento de ubicación activo"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Seguimiento Activo")
            .setContentText("Tu ubicación está siendo compartida de forma segura")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }

    private fun createLocationRequest() {
        locationRequest = LocationRequest.Builder(
            Priority.PRIORITY_HIGH_ACCURACY,
            LOCATION_UPDATE_INTERVAL
        ).apply {
            setMinUpdateIntervalMillis(FASTEST_UPDATE_INTERVAL)
            setMaxUpdateDelayMillis(LOCATION_UPDATE_INTERVAL * 2)
        }.build()
    }

    private fun createLocationCallback() {
        locationCallback = object : LocationCallback() {
            override fun onLocationResult(locationResult: LocationResult) {
                super.onLocationResult(locationResult)

                locationResult.lastLocation?.let { location ->
                    Log.d(TAG, "New location: ${location.latitude}, ${location.longitude}")
                    sendLocationToServer(location)
                }
            }

            override fun onLocationAvailability(locationAvailability: LocationAvailability) {
                super.onLocationAvailability(locationAvailability)
                Log.d(TAG, "Location availability: ${locationAvailability.isLocationAvailable}")
            }
        }
    }

    private fun startLocationUpdates() {
        if (ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            Log.e(TAG, "Location permission not granted")
            stopSelf()
            return
        }

        fusedLocationClient.requestLocationUpdates(
            locationRequest,
            locationCallback,
            Looper.getMainLooper()
        )

        Log.d(TAG, "Location updates started")
    }

    private fun startDemandPolling() {
        demandPollingJob?.cancel()
        demandPollingJob = serviceScope.launch {
            while (true) {
                try {
                    // Usar intervalo dinámico (2s cuando hay solicitudes urgentes, 30s normal)
                    kotlinx.coroutines.delay(currentPollingInterval)

                    val childId = preferenceManager.getChildId()
                    if (childId != -1) {
                        // Renovar registro de conexión solo cada 30 segundos
                        if (currentPollingInterval >= 30000L) {
                            try {
                                NetworkClient.apiService.registerConnection(childId.toString())
                                Log.d(TAG, "🔄 Connection renewed for child $childId")
                            } catch (e: Exception) {
                                Log.w(TAG, "Failed to renew connection: ${e.message}")
                            }
                        }

                        // Verificar solicitudes pendientes
                        val response = NetworkClient.apiService.checkPendingLocationRequests(
                            childId = childId.toString()
                        )

                        if (response.isSuccessful) {
                            val responseBody = response.body()
                            when (responseBody?.status) {
                                "pending_request" -> {
                                    Log.d(TAG, "🚨 URGENT REQUEST detected - getting immediate location")
                                    requestImmediateLocationNow()

                                    // Marcar timestamp de solicitud urgente
                                    lastUrgentRequest = System.currentTimeMillis()

                                    // Cambiar a polling frecuente por si hay más solicitudes
                                    currentPollingInterval = 2000L
                                    Log.d(TAG, "⚡ Switching to fast polling (2 seconds)")
                                }
                                "no_request" -> {
                                    // Volver a polling normal si no hay solicitudes urgentes por 60 segundos
                                    val currentTime = System.currentTimeMillis()
                                    if (currentPollingInterval < 30000L &&
                                        (currentTime - lastUrgentRequest) > 60000L) {
                                        currentPollingInterval = 30000L
                                        Log.d(TAG, "🐌 Switching back to normal polling (30 seconds) - no urgent requests for 60s")
                                    }
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error checking for demand requests: ${e.message}")
                }
            }
        }

        Log.d(TAG, "🔄 Dynamic polling started (30s normal, 2s when urgent)")
    }

    private fun registerConnectionWithServer() {
        serviceScope.launch {
            try {
                val childId = preferenceManager.getChildId()
                if (childId == -1) {
                    Log.e(TAG, "Child ID not found for connection registration")
                    return@launch
                }

                val response = NetworkClient.apiService.registerConnection(
                    childId = childId.toString()
                )
                if (response.isSuccessful) {
                    Log.d(TAG, "✅ Connection registered with server for child $childId")
                } else {
                    Log.w(TAG, "Failed to register connection: ${response.code()}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error registering connection: ${e.message}")
            }
        }
    }

    private fun stopLocationUpdates() {
        fusedLocationClient.removeLocationUpdates(locationCallback)
        Log.d(TAG, "Location updates stopped")
    }

    private fun requestImmediateLocationNow() {
        if (ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            Log.e(TAG, "Location permission not granted for immediate request")
            return
        }

        Log.d(TAG, "🎯 Getting immediate location...")

        // Crear una solicitud de ubicación de alta prioridad para respuesta inmediata
        val immediateLocationRequest = LocationRequest.Builder(
            Priority.PRIORITY_HIGH_ACCURACY,
            0L // Inmediato
        ).apply {
            setMaxUpdateDelayMillis(5000L) // Máximo 5 segundos
            setMinUpdateIntervalMillis(0L)
            setWaitForAccurateLocation(false)
        }.build()

        // Callback para una sola ubicación
        val immediateCallback = object : LocationCallback() {
            override fun onLocationResult(locationResult: LocationResult) {
                super.onLocationResult(locationResult)
                locationResult.lastLocation?.let { location ->
                    Log.d(TAG, "🎯 Immediate location obtained: ${location.latitude}, ${location.longitude}")
                    sendLocationToServer(location, isImmediate = true)

                    // Remover el callback después de obtener la ubicación
                    fusedLocationClient.removeLocationUpdates(this)
                }
            }

            override fun onLocationAvailability(locationAvailability: LocationAvailability) {
                super.onLocationAvailability(locationAvailability)
                if (!locationAvailability.isLocationAvailable) {
                    Log.w(TAG, "🚨 Immediate location not available, trying last known location")
                    getLastKnownLocationImmediate()
                    fusedLocationClient.removeLocationUpdates(this)
                }
            }
        }

        // Solicitar ubicación inmediata
        fusedLocationClient.requestLocationUpdates(
            immediateLocationRequest,
            immediateCallback,
            Looper.getMainLooper()
        )

        // Timeout de seguridad - si no hay respuesta en 10 segundos, usar última ubicación conocida
        serviceScope.launch {
            kotlinx.coroutines.delay(10000)
            fusedLocationClient.removeLocationUpdates(immediateCallback)
            getLastKnownLocationImmediate()
        }
    }

    private fun getLastKnownLocationImmediate() {
        if (ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return
        }

        fusedLocationClient.lastLocation.addOnSuccessListener { location ->
            if (location != null) {
                Log.d(TAG, "🎯 Using last known location for immediate request: ${location.latitude}, ${location.longitude}")
                sendLocationToServer(location, isImmediate = true)
            } else {
                Log.e(TAG, "🚨 No location available for immediate request")
            }
        }
    }

    private fun sendLocationToServer(location: Location, isImmediate: Boolean = false) {
        val childId = preferenceManager.getChildId()
        if (childId == -1) {
            Log.e(TAG, "Child ID not found")
            return
        }

        serviceScope.launch {
            try {
                // Formatear timestamp en ISO 8601
                val timestamp = java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", java.util.Locale.getDefault()).format(java.util.Date())

                val locationData = LocationUpdateRequest(
                    latitude = location.latitude,
                    longitude = location.longitude,
                    accuracy = location.accuracy,
                    timestamp = timestamp
                )

                val response = NetworkClient.apiService.updateLocation(
                    childId = childId.toString(),
                    locationData = locationData
                )

                if (response.isSuccessful) {
                    val logMessage = if (isImmediate) {
                        "🎯 IMMEDIATE location sent successfully (requested by parent)"
                    } else {
                        "📍 Regular location sent successfully"
                    }
                    Log.d(TAG, logMessage)
                } else {
                    Log.e(TAG, "Failed to send location: ${response.code()}")
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error sending location: ${e.message}")
            }
        }
    }

    private fun getBatteryLevel(): Int {
        val batteryIntent = registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
        val level = batteryIntent?.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) ?: -1
        val scale = batteryIntent?.getIntExtra(BatteryManager.EXTRA_SCALE, -1) ?: -1

        return if (level != -1 && scale != -1) {
            (level * 100 / scale)
        } else {
            -1
        }
    }
}
