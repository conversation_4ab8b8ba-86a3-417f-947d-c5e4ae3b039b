package com.example.corredoresseguroshijo.services // Ubicado en el paquete 'services'

import android.Manifest
import android.app.*
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.location.Location
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.os.Looper
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.google.android.gms.location.*
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.util.concurrent.TimeUnit
import java.util.concurrent.Executors
import kotlinx.coroutines.*
// --- Importaciones Corregidas/Añadidas ---
import com.example.corredoresseguroshijo.DashboardActivity
import com.example.corredoresseguroshijo.MyApplication // Importar MyApplication para acceder al ID del canal
import com.example.corredoresseguroshijo.R // Importación de R
import com.example.corredoresseguroshijo.network.ApiService         // Importar interfaz centralizada
import com.example.corredoresseguroshijo.network.RetrofitClient     // Importar cliente centralizado
import com.example.corredoresseguroshijo.network.LocationLogRequest // Importar DTO correcto
import com.example.corredoresseguroshijo.network.SimpleApiResponse  // Importar DTO correcto
import com.example.corredoresseguroshijo.network.WebSocketManager   // Importar WebSocket Manager

// --- Servicio ---
class LocationForegroundService : Service() {

    private val TAG = "LocationService"

    // Constantes mejoradas para seguimiento en segundo plano
    private val NOTIFICATION_ID = 123 // ID único para la notificación del Foreground Service
    private val LOCATION_UPDATE_INTERVAL_MILLISECONDS = TimeUnit.SECONDS.toMillis(10) // Intervalo más frecuente (10 segundos)
    private val FASTEST_LOCATION_UPDATE_INTERVAL_MILLISECONDS = TimeUnit.SECONDS.toMillis(5) // Intervalo más rápido (5 segundos)
    private val MIN_DISTANCE_CHANGE_METERS = 3.0f // Mínimo cambio de distancia para registrar (3 metros)

    // Binder y LiveData
    private val binder = LocalBinder()
    private val _serviceStatusLiveData = MutableLiveData<String>()
    val serviceStatusLiveData: LiveData<String> get() = _serviceStatusLiveData // Exposición pública inmutable

    // Cliente de ubicación y Callback
    private lateinit var fusedLocationClient: FusedLocationProviderClient
    private lateinit var locationCallback: LocationCallback

    // Variables de estado
    private var currentLocation: Location? = null
    private var lastSentLocation: Location? = null // Para evitar enviar ubicaciones duplicadas
    private var currentChildId: Long = -1L

    // Scope de corrutinas para operaciones en segundo plano
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val backgroundExecutor = Executors.newSingleThreadExecutor()

    // WebSocket Manager para notificaciones inmediatas
    private lateinit var webSocketManager: WebSocketManager

    // --- Ciclo de Vida ---
    override fun onCreate() {
        super.onCreate()
        Log.i(TAG, "Servicio onCreate")
        fusedLocationClient = LocationServices.getFusedLocationProviderClient(this)
        setupLocationCallback()

        // Inicializar WebSocket Manager
        webSocketManager = WebSocketManager.getInstance()

        // createNotificationChannel() // <-- ELIMINADO: El canal se crea en MyApplication.onCreate()
        _serviceStatusLiveData.postValue(getString(R.string.gps_status_inactive))
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.i(TAG, "Servicio onStartCommand")
        currentChildId = intent?.getLongExtra("EXTRA_CHILD_ID", -1L) ?: -1L
        if (currentChildId == -1L) {
            Log.e(TAG, "Error: Child ID no proporcionado. Deteniendo servicio.")
            stopSelf() // Detener si no hay ID
            return START_NOT_STICKY // No reiniciar
        }
        Log.i(TAG, "Servicio iniciado para Child ID: $currentChildId")

        // Configurar WebSocket para este hijo
        setupWebSocket()

        // Intentar iniciar en primer plano (requiere permiso POST_NOTIFICATIONS en API 33+)
        try {
            val notification = createNotification() // Crear la notificación
            startForeground(NOTIFICATION_ID, notification) // Iniciar como servicio en primer plano
            Log.i(TAG, "Servicio iniciado en primer plano.")
            _serviceStatusLiveData.postValue(getString(R.string.gps_status_activating))
            startLocationUpdates() // Solicitar ubicaciones DESPUÉS de iniciar en primer plano
        } catch (e: Exception) {
            // Capturar posibles excepciones si startForeground falla (ej. por falta de permiso de notificación)
            Log.e(TAG, "Error al llamar a startForeground: ${e.message}", e)
            _serviceStatusLiveData.postValue("${getString(R.string.gps_status_error)} (Notif Perm?)")
            stopSelf() // Detener el servicio si no puede iniciar en primer plano
            return START_NOT_STICKY
        }

        return START_STICKY // Intentar reiniciar si el sistema lo mata
    }

     override fun onDestroy() {
         Log.i(TAG, "Servicio onDestroy")
         stopLocationUpdates() // Detener actualizaciones al destruir

         // Desconectar WebSocket
         webSocketManager.disconnect()

         // Cancelar todas las corrutinas
         serviceScope.cancel()
         backgroundExecutor.shutdown()

         _serviceStatusLiveData.postValue(getString(R.string.gps_status_inactive))
         // Considerar quitar la notificación si se detiene explícitamente
         // stopForeground(STOP_FOREGROUND_REMOVE) // o stopForeground(true) en APIs < 24
         super.onDestroy()
     }

    // --- Vinculación ---
    inner class LocalBinder : Binder() {
        fun getService(): LocationForegroundService = this@LocationForegroundService
    }
    override fun onBind(intent: Intent): IBinder {
        Log.d(TAG, "Servicio onBind")
        return binder
    }
    override fun onUnbind(intent: Intent?): Boolean {
        Log.d(TAG, "Servicio onUnbind")
        // Devolver true si quieres que se llame a onRebind la próxima vez que un cliente se enlace
        // después de que todos se hayan desenlazado. Normalmente false está bien.
        return false
    }

    // --- Lógica de Ubicación ---
     private fun setupLocationCallback() {
         locationCallback = object : LocationCallback() {
             override fun onLocationResult(locationResult: LocationResult) {
                 locationResult.locations.lastOrNull()?.let { location ->
                     Log.d(TAG, "Callback: Nueva ubicación: Lat ${location.latitude}, Lng ${location.longitude}, Acc ${location.accuracy}")

                     // Verificar si la ubicación es significativamente diferente
                     if (shouldSendLocation(location)) {
                         currentLocation = location
                         _serviceStatusLiveData.postValue(getString(R.string.gps_status_active))

                         // Enviar a la API en un hilo secundario para evitar bloquear el hilo principal
                         serviceScope.launch {
                             sendLocationToApi(location)
                         }

                         lastSentLocation = location // Actualizar última ubicación enviada
                     } else {
                         Log.d(TAG, "Ubicación muy similar a la anterior, no enviando")
                     }
                 } ?: Log.w(TAG,"Callback: LocationResult sin ubicaciones válidas.")
             }
             override fun onLocationAvailability(locationAvailability: LocationAvailability) {
                  Log.d(TAG, "Callback: Disponibilidad de ubicación: ${locationAvailability.isLocationAvailable}")
                  if (!locationAvailability.isLocationAvailable) {
                      _serviceStatusLiveData.postValue(getString(R.string.location_services_disabled)) // Actualizar estado
                  }
             }
         }
     }

     private fun startLocationUpdates() {
         // Comprobación de permisos (importante aunque redundante con la Activity)
         if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED &&
             ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
             Log.e(TAG, "startLocationUpdates: Faltan permisos de ubicación. Deteniendo servicio.")
             _serviceStatusLiveData.postValue(getString(R.string.gps_permission_denied))
              stopSelf() // Detener el servicio si los permisos se revocan mientras corre
             return
         }

         // Crear la solicitud de ubicación mejorada para segundo plano
         val locationRequest = com.google.android.gms.location.LocationRequest.Builder(
                Priority.PRIORITY_HIGH_ACCURACY, // Prioridad alta para mejor precisión
                LOCATION_UPDATE_INTERVAL_MILLISECONDS
            ).apply {
                setMinUpdateIntervalMillis(FASTEST_LOCATION_UPDATE_INTERVAL_MILLISECONDS)
                setWaitForAccurateLocation(false) // No bloquear esperando la primera ubicación precisa
                setMinUpdateDistanceMeters(MIN_DISTANCE_CHANGE_METERS) // Solo actualizar si se mueve más de 3 metros
                setMaxUpdateDelayMillis(LOCATION_UPDATE_INTERVAL_MILLISECONDS * 2) // Permitir batching hasta 20 segundos
                setGranularity(Granularity.GRANULARITY_PERMISSION_LEVEL) // Usar la mejor granularidad disponible
                setDurationMillis(Long.MAX_VALUE) // Duración indefinida
            }.build()

         try {
             Log.i(TAG, "Solicitando actualizaciones de ubicación con intervalo ${LOCATION_UPDATE_INTERVAL_MILLISECONDS}ms...")
             // Solicitar actualizaciones usando el cliente FusedLocationProvider
             fusedLocationClient.requestLocationUpdates(locationRequest, locationCallback, Looper.getMainLooper())
         } catch (e: SecurityException) {
             Log.e(TAG, "SecurityException al solicitar actualizaciones (¿permiso revocado?): ${e.message}")
             _serviceStatusLiveData.postValue(getString(R.string.gps_permission_denied))
             stopSelf()
         } catch (ex: Exception) {
              Log.e(TAG, "Excepción al solicitar actualizaciones: ", ex)
              _serviceStatusLiveData.postValue(getString(R.string.gps_status_error))
              // Considerar lógica de reintento o notificación de error más persistente
         }
     }

     private fun stopLocationUpdates() {
         Log.i(TAG, "Deteniendo actualizaciones de ubicación...") // Log mejorado
         try {
             // Detener las actualizaciones de ubicación
             fusedLocationClient.removeLocationUpdates(locationCallback)
                 .addOnSuccessListener { Log.d(TAG, "Actualizaciones de ubicación detenidas con éxito.") }
                 .addOnFailureListener { e -> Log.w(TAG, "Fallo al detener actualizaciones de ubicación.", e) }
         } catch (e: SecurityException) {
             // Aunque es menos probable aquí, capturar por si acaso
              Log.e(TAG, "SecurityException al detener actualizaciones: ${e.message}")
         } catch (ex: Exception) {
             Log.e(TAG, "Excepción general al detener actualizaciones: ", ex)
         }
     }

    // --- Función para determinar si enviar ubicación ---
    private fun shouldSendLocation(newLocation: Location): Boolean {
        val lastLocation = lastSentLocation

        // Si es la primera ubicación, siempre enviar
        if (lastLocation == null) {
            return true
        }

        // Calcular distancia entre ubicaciones
        val distance = lastLocation.distanceTo(newLocation)

        // Calcular tiempo transcurrido
        val timeDifference = newLocation.time - lastLocation.time
        val timeDifferenceSeconds = timeDifference / 1000

        // Enviar si:
        // 1. Se movió más de la distancia mínima
        // 2. Ha pasado más de 30 segundos (para asegurar actualizaciones regulares)
        // 3. La precisión mejoró significativamente
        return distance >= MIN_DISTANCE_CHANGE_METERS ||
               timeDifferenceSeconds >= 30 ||
               (newLocation.hasAccuracy() && lastLocation.hasAccuracy() &&
                newLocation.accuracy < lastLocation.accuracy - 5)
    }

    // --- Lógica de API ---
    private suspend fun sendLocationToApi(location: Location) = withContext(Dispatchers.IO) {
        if (currentChildId == -1L) {
            Log.w(TAG, "sendLocationToApi: Ignorando envío, Child ID inválido.")
            return@withContext
        }

        // Formatear timestamp a ISO 8601 UTC
        val timestampISO = try {
             java.time.format.DateTimeFormatter.ISO_INSTANT
                 .format(java.time.Instant.ofEpochMilli(location.time))
         } catch (e: Exception) {
             Log.e(TAG, "Error formateando timestamp: ${e.message}. Usando now()")
             java.time.format.DateTimeFormatter.ISO_INSTANT.format(java.time.Instant.now()) // Fallback
         }


        // Crear objeto de solicitud DTO
        val request = LocationLogRequest(
            latitude = location.latitude,
            longitude = location.longitude,
            timestamp = timestampISO,
            accuracy = if (location.hasAccuracy()) location.accuracy else null
        )

        Log.d(TAG, "Enviando a API: Lat=${request.latitude}, Lng=${request.longitude}, Time=${request.timestamp}, Acc=${request.accuracy ?: "N/A"}, ChildID=$currentChildId")

        // Realizar la llamada usando el cliente Retrofit centralizado
        RetrofitClient.instance.logLocation(request, currentChildId.toString()).enqueue(object : Callback<SimpleApiResponse> {

            override fun onResponse(call: Call<SimpleApiResponse>, response: Response<SimpleApiResponse>) {
                if (response.isSuccessful && response.body()?.status == "success") {
                    Log.i(TAG, "API OK: Ubicación enviada. Msg: ${response.body()?.message}")
                    // Podrías actualizar LiveData con más detalle si quieres
                    // _serviceStatusLiveData.postValue("${getString(R.string.gps_status_active)} (Enviada ${timestampISO.substring(11, 19)})")
                } else {
                    // Error HTTP o error lógico en la respuesta de la API
                    val errorMsg = response.errorBody()?.string() ?: response.message()
                    Log.e(TAG, "API ERROR ${response.code()}: Falla al enviar ubicación. ${errorMsg}")
                    // Actualizar estado para reflejar el error de envío
                    _serviceStatusLiveData.postValue("${getString(R.string.gps_status_active)} (Error Envío API)")
                }
            }

            override fun onFailure(call: Call<SimpleApiResponse>, t: Throwable) {
                // Error de Red/Conectividad o error al procesar la respuesta
                Log.e(TAG, "API FALLO: ${t.message}", t)
                 _serviceStatusLiveData.postValue("${getString(R.string.gps_status_active)} (Error Red)")
                 // TODO: Implementar cola de reintentos si la red falla
            }
        })
    }


    // --- Notificación del Foreground Service ---
     // createNotificationChannel() // <-- ELIMINADO DE AQUÍ

     private fun createNotification(): Notification {
         Log.d(TAG,"Creando notificación de Foreground Service...")

         // Intent para abrir DashboardActivity al tocar la notificación
         val notificationIntent = Intent(this, DashboardActivity::class.java).apply {
             putExtra("EXTRA_CHILD_ID", currentChildId) // Pasar ID por si acaso
             // Flags para controlar el comportamiento de la pila de actividades
             flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP
         }

         // PendingIntent para envolver el Intent
         val pendingIntent: PendingIntent = PendingIntent.getActivity(
             this,
             0, // Request code (puede ser 0 si no necesitas distinguir intents)
             notificationIntent,
             // Flag importante para seguridad y compatibilidad
             PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
         )

         // Construir la notificación usando NotificationCompat para compatibilidad
         val builder = NotificationCompat.Builder(this, MyApplication.LOCATION_SERVICE_CHANNEL_ID) // Usar ID del canal de MyApplication
             .setContentTitle(getString(R.string.foreground_service_notification_title))
             .setContentText(getString(R.string.foreground_service_notification_text))
             .setSmallIcon(android.R.drawable.ic_menu_mylocation) // Usa el R de android, no el de tu app
             .setContentIntent(pendingIntent) // Acción al tocar
             .setOngoing(true) // Hacerla persistente (no deslizable)
             .setCategory(NotificationCompat.CATEGORY_SERVICE) // Categoría semántica
             .setPriority(NotificationCompat.PRIORITY_LOW) // Prioridad (afecta visibilidad en versiones antiguas)
             // Opcional: .setTicker("Rastreando ubicación...") // Texto que aparece brevemente en barra de estado antigua

         return builder.build()
     }

     // EL MÉTODO EXPLÍCITO getServiceStatusLiveData() FUE ELIMINADO, SE USA LA PROPIEDAD

     // --- WebSocket Integration ---
     private fun setupWebSocket() {
         if (currentChildId != -1L) {
             // Inicializar WebSocket con el ID del hijo
             webSocketManager.initialize(currentChildId.toInt())

             // Configurar callback para solicitudes de ubicación inmediata
             webSocketManager.onLocationRequested = {
                 Log.d(TAG, "🚨 IMMEDIATE LOCATION REQUEST received via WebSocket")
                 requestImmediateLocation()
             }

             // Conectar WebSocket
             webSocketManager.connect()
             Log.d(TAG, "✅ WebSocket configured for child $currentChildId")
         } else {
             Log.e(TAG, "Cannot setup WebSocket: invalid child ID")
         }
     }

     private fun requestImmediateLocation() {
         Log.d(TAG, "🎯 Processing immediate location request...")

         // Verificar permisos
         if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
             Log.e(TAG, "Cannot get immediate location: missing permissions")
             return
         }

         try {
             // Solicitar ubicación actual inmediatamente
             fusedLocationClient.getCurrentLocation(
                 Priority.PRIORITY_HIGH_ACCURACY,
                 null
             ).addOnSuccessListener { location ->
                 if (location != null) {
                     Log.d(TAG, "🎯 IMMEDIATE location obtained: ${location.latitude}, ${location.longitude}")

                     // Enviar via WebSocket inmediatamente
                     webSocketManager.sendLocationResponse(
                         location.latitude,
                         location.longitude,
                         if (location.hasAccuracy()) location.accuracy else 0f
                     )

                     // También enviar via API REST como backup
                     serviceScope.launch {
                         sendLocationToApi(location)
                     }

                     // Actualizar ubicación actual
                     currentLocation = location
                     lastSentLocation = location

                 } else {
                     Log.w(TAG, "Immediate location request returned null")
                 }
             }.addOnFailureListener { exception ->
                 Log.e(TAG, "Failed to get immediate location: ${exception.message}")
             }

         } catch (e: SecurityException) {
             Log.e(TAG, "SecurityException getting immediate location: ${e.message}")
         }
     }
}