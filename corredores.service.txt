[Unit]
Description=Aplicacion Corredores_Seguros
After=network.target

[Service]
User=root
Group=root  # Corregido: 'Group' no 'Groupr'
WorkingDirectory=/home/<USER>/corredores
Environment="FERNET_KEY=sqz3lI_ppLDUSqm-zJoxncch8cYjbj84qxjT43XDojo="
ExecStart=/home/<USER>/corredores/corre/bin/python /home/<USER>/corredores/manage.py # Se mantiene igual.
Restart=on-failure
Environment=PYTHONUNBUFFERED=1

[Install]
WantedBy=multi-user.target