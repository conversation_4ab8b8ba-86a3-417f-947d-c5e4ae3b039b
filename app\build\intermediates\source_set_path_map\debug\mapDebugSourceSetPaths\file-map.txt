com.example.corredores.app-lifecycle-service-2.7.0-0 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\060432f54d80cc8e659316157dc70d71\transformed\lifecycle-service-2.7.0\res
com.example.corredores.app-core-viewtree-1.0.0-1 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07d177d4395d04d87ca62bf88051dbbc\transformed\core-viewtree-1.0.0\res
com.example.corredores.app-startup-runtime-1.1.1-2 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\095ec3180b911c35eacd1feab6e0eef8\transformed\startup-runtime-1.1.1\res
com.example.corredores.app-lifecycle-livedata-2.7.0-3 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0aa488a77f7e697087baeb6ea75979bf\transformed\lifecycle-livedata-2.7.0\res
com.example.corredores.app-material-1.12.0-4 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2da938e0e51246f58401e1e70a8344\transformed\material-1.12.0\res
com.example.corredores.app-lifecycle-process-2.7.0-5 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101cca20c1e85df7a5f9f46711f93017\transformed\lifecycle-process-2.7.0\res
com.example.corredores.app-recyclerview-1.1.0-6 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1822edcf5d2328903154cc41cade1db2\transformed\recyclerview-1.1.0\res
com.example.corredores.app-lifecycle-livedata-core-2.7.0-7 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19590ff9a3e0873f1d96063fe131eb36\transformed\lifecycle-livedata-core-2.7.0\res
com.example.corredores.app-room-runtime-2.5.0-8 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\res
com.example.corredores.app-ads-adservices-1.0.0-beta05-9 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f85ba276f1370d20237343255144de\transformed\ads-adservices-1.0.0-beta05\res
com.example.corredores.app-emoji2-views-helper-1.3.0-10 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35d799fceb32dbef952e474ebfc145ee\transformed\emoji2-views-helper-1.3.0\res
com.example.corredores.app-appcompat-1.7.0-11 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3638d6c7c3d7b87d5f2eabc07b8b0e57\transformed\appcompat-1.7.0\res
com.example.corredores.app-core-1.16.0-12 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\res
com.example.corredores.app-transition-1.5.0-13 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c73a9538e3525628e2bf6c7b18e4ece\transformed\transition-1.5.0\res
com.example.corredores.app-fragment-1.5.4-14 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fd2b2904cc709cf74ecc96ad3a8f907\transformed\fragment-1.5.4\res
com.example.corredores.app-firebase-messaging-23.4.0-15 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\res
com.example.corredores.app-savedstate-1.2.1-16 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\636079ac1538b7c7227947a9db213969\transformed\savedstate-1.2.1\res
com.example.corredores.app-play-services-basement-18.1.0-17 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea606996c8ba6337c1770e421181b43\transformed\play-services-basement-18.1.0\res
com.example.corredores.app-emoji2-1.3.0-18 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\res
com.example.corredores.app-activity-1.10.1-19 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a86e451e335fe170cc22e2bbb8f32c2\transformed\activity-1.10.1\res
com.example.corredores.app-annotation-experimental-1.4.1-20 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c808468632754ca24252791f884893f\transformed\annotation-experimental-1.4.1\res
com.example.corredores.app-firebase-common-20.4.2-21 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\res
com.example.corredores.app-constraintlayout-2.2.1-22 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\93edbca5700ad9cb25d78eb02bade720\transformed\constraintlayout-2.2.1\res
com.example.corredores.app-appcompat-resources-1.7.0-23 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\967147dcae910194fa274d2c250149cc\transformed\appcompat-resources-1.7.0\res
com.example.corredores.app-work-runtime-ktx-2.9.0-24 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bd61a142146d3441faa191edd4584bb\transformed\work-runtime-ktx-2.9.0\res
com.example.corredores.app-drawerlayout-1.1.1-25 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e0f0a33a4832529aa72c6af15bff48c\transformed\drawerlayout-1.1.1\res
com.example.corredores.app-cardview-1.0.0-26 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ecd105970e5114bb9554f865bbee88c\transformed\cardview-1.0.0\res
com.example.corredores.app-play-services-base-18.1.0-27 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee22392c0511e6aba80092990f3de4a\transformed\play-services-base-18.1.0\res
com.example.corredores.app-lifecycle-viewmodel-savedstate-2.7.0-28 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\acfe9f8b774d2c354443b3078d546139\transformed\lifecycle-viewmodel-savedstate-2.7.0\res
com.example.corredores.app-sqlite-2.3.0-29 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b741871d3d36ce368591da2830fa6c16\transformed\sqlite-2.3.0\res
com.example.corredores.app-sqlite-framework-2.3.0-30 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9951833a5599c2e5d44cc8e9213addc\transformed\sqlite-framework-2.3.0\res
com.example.corredores.app-coordinatorlayout-1.1.0-31 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba55085bc3cc0f7fd03bf666ca981f5a\transformed\coordinatorlayout-1.1.0\res
com.example.corredores.app-room-ktx-2.5.0-32 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcb3250c1492934d3ece20c483063ed7\transformed\room-ktx-2.5.0\res
com.example.corredores.app-core-runtime-2.2.0-33 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bda673c447e21be587edbfdcc61f30bc\transformed\core-runtime-2.2.0\res
com.example.corredores.app-profileinstaller-1.4.0-34 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\res
com.example.corredores.app-lifecycle-runtime-2.7.0-35 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf26778305f000d2d1525106f6580765\transformed\lifecycle-runtime-2.7.0\res
com.example.corredores.app-ads-adservices-java-1.0.0-beta05-36 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d67d231637ce3b2a99ecff45dd34195d\transformed\ads-adservices-java-1.0.0-beta05\res
com.example.corredores.app-tracing-1.2.0-37 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d73aef2def23a6c1775a61c1e9f05979\transformed\tracing-1.2.0\res
com.example.corredores.app-lifecycle-runtime-ktx-2.7.0-38 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3e428614568415b5436ae0870877c8\transformed\lifecycle-runtime-ktx-2.7.0\res
com.example.corredores.app-work-runtime-2.9.0-39 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\res
com.example.corredores.app-lifecycle-livedata-core-ktx-2.7.0-40 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df67eaaf0b48836253fb504b6c9a6346\transformed\lifecycle-livedata-core-ktx-2.7.0\res
com.example.corredores.app-core-ktx-1.16.0-41 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e354fd7c956ed6175d4f117257206ed7\transformed\core-ktx-1.16.0\res
com.example.corredores.app-lifecycle-viewmodel-ktx-2.7.0-42 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5dbcc4bae08e2d37d02b09ba5c09d41\transformed\lifecycle-viewmodel-ktx-2.7.0\res
com.example.corredores.app-webkit-1.8.0-43 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6f715e2c0250f74384a9309ebf14495\transformed\webkit-1.8.0\res
com.example.corredores.app-viewpager2-1.0.0-44 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbb63f8e3ee649ebb59cd4bb90450e99\transformed\viewpager2-1.0.0\res
com.example.corredores.app-lifecycle-viewmodel-2.7.0-45 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe4373c4482e7e798dd3da2e28e74cfc\transformed\lifecycle-viewmodel-2.7.0\res
com.example.corredores.app-play-services-measurement-api-21.5.0-46 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\res
com.example.corredores.app-pngs-47 C:\Users\<USER>\AndroidStudioProjects\corredores\app\build\generated\res\pngs\debug
com.example.corredores.app-res-48 C:\Users\<USER>\AndroidStudioProjects\corredores\app\build\generated\res\processDebugGoogleServices
com.example.corredores.app-resValues-49 C:\Users\<USER>\AndroidStudioProjects\corredores\app\build\generated\res\resValues\debug
com.example.corredores.app-packageDebugResources-50 C:\Users\<USER>\AndroidStudioProjects\corredores\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.corredores.app-packageDebugResources-51 C:\Users\<USER>\AndroidStudioProjects\corredores\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.corredores.app-debug-52 C:\Users\<USER>\AndroidStudioProjects\corredores\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.corredores.app-debug-53 C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\debug\res
com.example.corredores.app-main-54 C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res
