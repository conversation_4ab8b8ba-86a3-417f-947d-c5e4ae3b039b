<!-- ver_notificaciones.html -->
{% extends 'base.html' %}

{% block content %}
<h2>Mis Notificaciones</h2>
<table border="1">
    <tr>
        <th>Hijo</th>
        <th>Tipo de Notificación</th>
        <th>Mensaje</th>
        <th>Fecha</th>
        <th>Visto</th>
        <th>Acciones</th>
    </tr>
    {% for notification in notifications %}
    <tr id="notification-{{ notification.notification_id }}">
        <td>{{ notification.child.first_name }} {{ notification.child.last_name }}</td>
        <td>{{ notification.notification_type }}</td>
        <td>{{ notification.message_text }}</td>
        <td>{{ notification.created_at }}</td>
        <td>{{ notification.viewed_at or 'No' }}</td>
        <td>
            {% if not notification.viewed_at %}
            <button onclick="marcarLeido({{ notification.notification_id }})" class="btn btn-success btn-sm">Marcar como leído</button>
            {% else %}
            -
            {% endif %}
        </td>
    </tr>
    {% endfor %}
</table>
{% endblock %}

{% block scripts %}
  {{ super() }}
  <script>
    // Función para marcar la notificación como leída
    function marcarLeido(notificationId) {
      fetch('/marcar_leido/' + notificationId, {
          method: 'POST',
          headers: {
              'Content-Type': 'application/json',
              'X-CSRFToken': '{{ csrf_token() }}'
          }
      })
      .then(response => response.json())
      .then(data => {
          console.log(data.message);
          // Se remueve la fila de la tabla para que la notificación no se siga mostrando
          var row = document.getElementById('notification-' + notificationId);
          if (row) {
              row.parentNode.removeChild(row);
          }
      })
      .catch(error => {
          console.error('Error al marcar notificación como leída:', error);
      });
    }
  </script>
{% endblock %}
