# 📱 Corredores Seguros - App Android

Aplicación móvil para seguimiento familiar con roles duales (Padre/Hijo) y notificaciones push en tiempo real.

## 📋 **DESCRIPCIÓN**

**Corredores Seguros Android** es la aplicación móvil que complementa el sistema de seguimiento familiar. Permite a los padres recibir notificaciones push y a los hijos enviar su ubicación de forma automática y segura.

### **🎯 CARACTERÍSTICAS PRINCIPALES:**

#### **👨‍👧‍👦 ROL PADRE:**
- **🔐 Login seguro** con folio y contraseña
- **📊 Dashboard web** integrado en la app
- **🔔 Notificaciones push** automáticas de Firebase
- **⚡ Solicitud de ubicación** inmediata de hijos
- **🚪 Logout seguro** con limpieza de datos

#### **👶 ROL HIJO:**
- **🔗 Vinculación** con token de 16 caracteres
- **📍 Seguimiento automático** 24/7 en segundo plano
- **📤 Notificaciones** bidireccionales al padre
- **⚡ Respuesta inmediata** a solicitudes de ubicación
- **🔋 Optimización de batería** automática

---

## 🏗️ **ARQUITECTURA**

```
┌─────────────────────────────────────────────────────────┐
│                📱 CORREDORES SEGUROS APP                │
├─────────────────────────────────────────────────────────┤
│  🎯 ProfileSelectionActivity (Selección Irreversible)   │
├─────────────────┬───────────────────────────────────────┤
│  👨‍👧‍👦 ROL PADRE  │              👶 ROL HIJO              │
├─────────────────┼───────────────────────────────────────┤
│ LoginActivity   │ TokenActivity                         │
│ ↓               │ ↓                                     │
│ MainActivity    │ MainActivity                          │
│ ├─WebView       │ ├─WebView (Dashboard Hijo)            │
│ ├─FCMManager    │ ├─LocationService (24/7)              │
│ └─Logout        │ ├─WebAppInterface                     │
│                 │ └─Polling Dinámico                    │
└─────────────────┴───────────────────────────────────────┘
```

---

## 🚀 **INSTALACIÓN**

### **📋 REQUISITOS:**
- Android 7.0+ (API 24+)
- Google Play Services
- Permisos de ubicación
- Conexión a internet

### **🔧 DESARROLLO:**

#### **1. 📥 CLONAR REPOSITORIO:**
```bash
git clone <repository-url>
cd corredores-seguros-android
```

#### **2. 🔥 CONFIGURAR FIREBASE:**
```bash
# Descargar google-services.json desde Firebase Console
# Colocar en: app/google-services.json
```

#### **3. 🏗️ BUILD:**
```bash
# Abrir en Android Studio
# Sync Project with Gradle Files
# Build → Make Project
```

#### **4. 📱 INSTALAR:**
```bash
# Conectar dispositivo Android
# Run → Run 'app'
```

---

## 🎯 **CONFIGURACIÓN**

### **🔧 VARIABLES DE CONFIGURACIÓN:**

#### **📡 Servidor:**
```kotlin
// En NetworkClient.kt
private const val BASE_URL = "https://patagoniaservers.com.ar:5004/"
```

#### **🔥 Firebase:**
```kotlin
// google-services.json debe estar en app/
// Configuración automática via plugin
```

#### **📍 Ubicación:**
```kotlin
// En LocationService.kt
private const val LOCATION_UPDATE_INTERVAL = 30000L // 30 segundos
private const val FASTEST_UPDATE_INTERVAL = 15000L  // 15 segundos
```

---

## 🎮 **USO DE LA APLICACIÓN**

### **🚀 PRIMER USO:**

1. **📱 Instalar** la aplicación
2. **🎯 Seleccionar rol** (Padre o Hijo) - **IRREVERSIBLE**
3. **🔐 Autenticarse** según el rol seleccionado
4. **✅ Comenzar** a usar las funcionalidades

### **👨‍👧‍👦 FLUJO PADRE:**

```
1. Seleccionar "Padre" → LoginActivity
2. Ingresar folio + contraseña → Autenticación servidor
3. MainActivity carga dashboard web → FCM se registra automáticamente
4. Recibir notificaciones push → Ver detalles en dashboard
5. Solicitar ubicación inmediata → Respuesta en 2-5 segundos
6. Logout → Desregistra FCM → Vuelve a selección de rol
```

### **👶 FLUJO HIJO:**

```
1. Seleccionar "Hijo" → TokenActivity
2. Ingresar token de 16 caracteres → Vinculación automática
3. MainActivity carga dashboard hijo → LocationService inicia
4. Envío automático ubicación cada 30s → Funciona en segundo plano
5. Responder solicitudes inmediatas del padre → Automático
6. Enviar notificaciones al padre → Botones predefinidos
```

---

## 🔧 **COMPONENTES TÉCNICOS**

### **📱 ACTIVIDADES:**

#### **🎯 ProfileSelectionActivity:**
```kotlin
// Selección inicial de rol (Padre/Hijo)
// Decisión irreversible guardada en PreferenceManager
// Redirige a LoginActivity o TokenActivity
```

#### **🔐 LoginActivity (Solo Padres):**
```kotlin
// Autenticación con folio + contraseña
// Comunicación con servidor Flask via API REST
// Guarda token de autenticación localmente
```

#### **🔗 TokenActivity (Solo Hijos):**
```kotlin
// Vinculación con token de 16 caracteres
// Validación en servidor y obtención de child_id
// Inicia LocationService automáticamente
```

#### **🏠 MainActivity:**
```kotlin
// Actividad principal con lógica dual según rol
// setupParentView() → WebView + FCM
// setupChildView() → WebView + LocationService + WebAppInterface
```

### **🔧 SERVICIOS:**

#### **📍 LocationService (Solo Hijos):**
```kotlin
// Servicio en primer plano permanente
// Envío automático de ubicación cada 30s
// Polling dinámico para solicitudes urgentes (30s normal, 2s urgente)
// Respuesta inmediata a solicitudes del padre
// Optimización de batería y reconexión automática
```

#### **🔔 MyFirebaseMessagingService (Solo Padres):**
```kotlin
// Recepción de notificaciones push de Firebase
// Registro/desregistro automático de tokens FCM
// Notificaciones nativas con sonido, vibración e iconos
// Canales de notificación personalizados para Android 8.0+
```

### **🌐 INTERFACES:**

#### **🔗 WebAppInterface (Solo Hijos):**
```kotlin
// Puente JavaScript ↔ Android nativo
@JavascriptInterface sendNotification()        // Enviar notificaciones al padre
@JavascriptInterface requestImmediateLocation() // Responder solicitudes urgentes
@JavascriptInterface getChildId()              // Obtener ID del hijo
@JavascriptInterface isLocationServiceActive() // Estado del GPS
```

#### **🔥 FCMManager (Solo Padres):**
```kotlin
// Gestión completa de tokens Firebase
// initializeForParent() → Registro automático en servidor
// unregisterToken() → Limpieza en logout
// Manejo de errores y reconexión automática
```

---

## 📊 **FUNCIONALIDADES DETALLADAS**

### **🔔 NOTIFICACIONES PUSH (Padres):**

#### **📥 Tipos de Notificaciones:**
- **⚠️ `zone_early_warning`** - Advertencia temprana (5 min antes)
- **⏰ `zone_late_arrival`** - Llegada tardía a zona
- **🚨 `zone_missed`** - Zona no visitada en tiempo esperado

#### **🎨 Personalización:**
```kotlin
// Sonido personalizado, vibración, iconos según tipo
// Notificación expandible para mensajes largos
// Intent para abrir dashboard al tocar notificación
```

### **📍 SEGUIMIENTO DE UBICACIÓN (Hijos):**

#### **⚡ Modos de Envío:**
- **🔄 Automático**: Cada 30 segundos en segundo plano
- **🚨 Inmediato**: Respuesta a solicitud del padre (2-5s)
- **🔋 Optimizado**: Ajuste dinámico según demanda

#### **🎯 Precisión:**
```kotlin
// Priority.PRIORITY_HIGH_ACCURACY para máxima precisión
// FusedLocationProviderClient para mejor rendimiento
// Fallback a última ubicación conocida si GPS falla
```

### **📤 NOTIFICACIONES AL PADRE (Hijos):**

#### **🎛️ Botones Predefinidos:**
- **✅ "Estoy Bien"** - Confirmación de estado
- **🆘 "Ayuda"** - Solicitud de asistencia
- **⏰ "Demorado"** - Aviso de retraso
- **📝 "Otro"** - Mensaje personalizado

---

## 🔒 **SEGURIDAD Y PRIVACIDAD**

### **🛡️ MEDIDAS DE SEGURIDAD:**
- **🔐 Tokens únicos** por dispositivo y sesión
- **🔒 Comunicación HTTPS** con certificados SSL
- **🎯 Selección de rol irreversible** - previene uso indebido
- **🧹 Limpieza automática** de datos en logout (solo padres)
- **📍 Encriptación** de datos de ubicación en tránsito

### **🔑 GESTIÓN DE PERMISOS:**
```xml
<!-- Permisos esenciales -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
<uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
```

---

## 🧪 **TESTING Y DEBUG**

### **📱 LOGS IMPORTANTES:**

#### **🔔 FCM (Padres):**
```
FCMService: Token FCM obtenido: [token]
FCMService: Notificación recibida: [título]
FCMManager: Token FCM registrado exitosamente
```

#### **📍 Ubicación (Hijos):**
```
LocationService: New location: lat, lng
LocationService: 🚨 URGENT REQUEST detected
LocationService: 🎯 Immediate location obtained
```

#### **🌐 WebApp Interface:**
```
WebAppInterface: Sending notification: [tipo]
WebAppInterface: 🚨 Immediate location requested by parent
```

### **🔧 DEBUG EN ANDROID STUDIO:**
```bash
# Filtrar logs por tag
adb logcat -s "FCMService"
adb logcat -s "LocationService" 
adb logcat -s "WebAppInterface"

# Ver todos los logs de la app
adb logcat | grep "com.example.corredores"
```

---

## 📦 **DEPENDENCIAS PRINCIPALES**

```kotlin
// Firebase
implementation(platform("com.google.firebase:firebase-bom:32.7.0"))
implementation("com.google.firebase:firebase-messaging-ktx")
implementation("com.google.firebase:firebase-analytics-ktx")

// Ubicación
implementation("com.google.android.gms:play-services-location:21.0.1")

// Networking
implementation("com.squareup.retrofit2:retrofit:2.9.0")
implementation("com.squareup.retrofit2:converter-gson:2.9.0")

// UI
implementation("androidx.appcompat:appcompat:1.6.1")
implementation("androidx.constraintlayout:constraintlayout:2.1.4")

// Corrutinas
implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
```

---

## 🚀 **BUILD Y RELEASE**

### **🏗️ BUILD VARIANTS:**
```kotlin
buildTypes {
    debug {
        applicationIdSuffix ".debug"
        debuggable true
    }
    release {
        minifyEnabled true
        proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
    }
}
```

### **📦 GENERAR APK:**
```bash
# Debug
./gradlew assembleDebug

# Release
./gradlew assembleRelease
```

---

## 🤝 **CONTRIBUCIÓN**

1. Fork del repositorio
2. Crear rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. Seguir convenciones de código Kotlin
4. Agregar tests unitarios
5. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
6. Push a la rama (`git push origin feature/nueva-funcionalidad`)
7. Crear Pull Request

---

## 📄 **LICENCIA**

Este proyecto está bajo la Licencia MIT. Ver `LICENSE` para más detalles.

---

## 📞 **SOPORTE**

- **Email**: <EMAIL>
- **Documentación**: [Wiki del proyecto]
- **Issues**: [GitHub Issues]

---

## 🔄 **CHANGELOG**

### **v2.1.0** (Actual)
- ✅ Notificaciones push Firebase Cloud Messaging
- ✅ Ubicación bajo demanda (2-5s respuesta)
- ✅ Polling dinámico inteligente
- ✅ Optimización de batería automática

### **v2.0.0**
- ✅ Sistema dual Padre/Hijo
- ✅ Selección de rol irreversible
- ✅ WebView integrado con dashboard
- ✅ Seguimiento 24/7 en segundo plano

### **v1.0.0**
- ✅ Seguimiento básico de ubicación
- ✅ Vinculación con tokens
- ✅ Notificaciones bidireccionales
