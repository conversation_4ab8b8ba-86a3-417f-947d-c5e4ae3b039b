// app/static/js/agent_simulator.js
// Versión Completa: Flujo con movilidad + GPS Real (Login-Logout) + ID DNI funcionando + WS Corregido

// --- Estado Global del Simulador ---

let currentAgentToken = null;
let currentAgentFolio = null;
let currentShiftId = null;
let selectedMobilityType = 'Peatonal';

// ✅ MOVER ARRIBA la definición
let apiBaseUrl = '/api'; // URL base API REST

// --- Coordenadas y Estado GPS ---
let currentLatitude = -40.8134; // Posición inicial (Viedma default)
let currentLongitude = -62.9967; // Posición inicial (Viedma default)
let lastKnownAccuracy = null; // Última precisión conocida del GPS real
let isUsingRealGps = false; // Flag: true si se obtuvo GPS real exitosamente
let realGpsWatchId = null; // ID del watcher de geolocalización
// --- Fin Estado GPS ---
let map = null; // Instancia del mapa Leaflet
let agentMarker = null; // Marcador del agente
let agentSocket = null; // <<< AÑADIDO >>> Instancia del WebSocket del agente
const API_BASE_URL_WS = 'wss://patagoniaservers.com.ar:5005'; // <<< AÑADIDO >>> URL Base para WS (ajusta si es necesario)

// --- Configuración de Envío GPS ---
const GPS_SEND_INTERVAL_MS = 10000; // Enviar cada 10 segundos
let gpsSendIntervalId = null; // ID del intervalo de envío

// --- Elementos del DOM (se asignan en init) ---
let loginSection, noveltiesSection, mobilitySelectionSection, activeShiftSection;
let loginFormSim, loginErrorSim, agentFolioSimSelect, agentPasswordSimInput;
let noveltiesListDiv, confirmNoveltiesBtn, logoutBtnNovelties;
let mobilityOptionsDiv, mobilityErrorDiv, confirmMobilityBtn, logoutBtnMobility;
let statusBar, currentAgentFolioSpan, currentMobilityDisplaySpan, endShiftBtn, logoutBtnActive;
let currentGpsDisplay;
let identifyPersonModal, identifyVehicleModal, allNoveltiesModal; // Instancias Bootstrap Modal
let identifyPersonForm, identifyVehicleForm;
let identifyPersonError, identifyVehicleError;
let identifyPersonBtn, identifyVehicleBtn, viewNoveltiesBtn;

/** Función principal de inicialización */
function initAgentSimulator() {
    console.log("[INIT] Iniciando Simulador de Agente (GPS + Login + WS)...");

    // <<< NUEVO >>> Verificar disponibilidad de AndroidInterface
    console.log("[INIT] Verificando AndroidInterface...");
    if (typeof AndroidInterface !== 'undefined') {
        console.log("[INIT] ✅ AndroidInterface está disponible");
        if (typeof AndroidInterface.onLoginSuccess === 'function') {
            console.log("[INIT] ✅ AndroidInterface.onLoginSuccess está disponible");
        } else {
            console.log("[INIT] ❌ AndroidInterface.onLoginSuccess NO está disponible");
        }
    } else {
        console.log("[INIT] ❌ AndroidInterface NO está disponible (probablemente navegador web)");
    }

     // --- Restaurar sesión persistente si existe token y folio ---
     currentAgentToken = localStorage.getItem('agentToken');
     currentAgentFolio = localStorage.getItem('agentFolio');

     if (currentAgentToken && currentAgentFolio) {
         console.log(`[AUTOLOGIN] Intentando restaurar sesión para el agente: ${currentAgentFolio}`);
         axios.defaults.headers.common['Authorization'] = `Bearer ${currentAgentToken}`;

         // <<< NUEVO >>> Notificar a Android del token existente
         if (typeof AndroidInterface !== 'undefined' && AndroidInterface.onLoginSuccess) {
             console.log("[AUTOLOGIN] Notificando token existente a Android...");
             AndroidInterface.onLoginSuccess(currentAgentToken);
         } else {
             console.log("[AUTOLOGIN] AndroidInterface no disponible (probablemente navegador web)");
         }

         axios.get(`${apiBaseUrl}/shifts/current`)
             .then(response => {
                 const shiftData = response.data;
                 if (shiftData?.id) {
                     currentShiftId = shiftData.id;
                     connectAgentWebSocket();
                     initializeAndStartRealGps();
                     updateStatus(`Turno ${currentShiftId} ACTIVO (${shiftData.mobility_type || 'N/A'}) - Restaurado`, true);
                     if (currentAgentFolioSpan) currentAgentFolioSpan.textContent = currentAgentFolio;
                     if (currentMobilityDisplaySpan) currentMobilityDisplaySpan.textContent = shiftData.mobility_type || 'N/A';
                     showSection('active-shift');
                     updateActionButtonState(true);
                 } else {
                     console.warn("[AUTOLOGIN] El token es válido pero no hay turno activo.");
                     showSection('login');
                 }
             })
             .catch(err => {
                 console.warn("[AUTOLOGIN] Falló la restauración automática:", err.response?.data?.msg || err.message);
                 localStorage.removeItem('agentToken');
                 localStorage.removeItem('agentFolio');
                 currentAgentToken = null;
                 currentAgentFolio = null;
                 delete axios.defaults.headers.common['Authorization'];
                 showSection('login');
             });
     } else {
         showSection('login'); // Mostrar login solo si no hay token
     }

    // Configurar URL Base API REST
    apiBaseUrl = document.body.dataset.apiBaseUrl || '/api';
    if (apiBaseUrl.endsWith('/')) apiBaseUrl = apiBaseUrl.slice(0, -1);
    console.log("[INIT] REST API Base URL:", apiBaseUrl);
    console.log("[INIT] WebSocket Base URL:", API_BASE_URL_WS); // Log WS URL

    // --- Asignación de Elementos DOM ---
    loginSection = document.getElementById('login-section');
    noveltiesSection = document.getElementById('novelties-section');
    mobilitySelectionSection = document.getElementById('mobility-selection-section');
    activeShiftSection = document.getElementById('active-shift-section');
    loginFormSim = document.getElementById('login-form-sim');
    loginErrorSim = document.getElementById('login-error-sim');
    agentFolioSimSelect = document.getElementById('agent_folio_sim');
    agentPasswordSimInput = document.getElementById('agent_password_sim');
    noveltiesListDiv = document.getElementById('novelties-list');
    confirmNoveltiesBtn = document.getElementById('confirm-novelties-btn');
    logoutBtnNovelties = document.getElementById('logout-btn-novelties');
    mobilityOptionsDiv = document.getElementById('mobility-options');
    mobilityErrorDiv = document.getElementById('mobility-error');
    confirmMobilityBtn = document.getElementById('confirm-mobility-btn');
    logoutBtnMobility = document.getElementById('logout-btn-mobility');
    statusBar = document.getElementById('status-bar');
    currentAgentFolioSpan = document.getElementById('current-agent-folio');
    currentMobilityDisplaySpan = document.getElementById('current-mobility-display');
    endShiftBtn = document.getElementById('end-shift-btn');
    logoutBtnActive = document.getElementById('logout-btn-active');
    currentGpsDisplay = document.getElementById('current-gps-display');
    identifyPersonBtn = document.getElementById('identify-person-btn');
    identifyVehicleBtn = document.getElementById('identify-vehicle-btn');
    viewNoveltiesBtn = document.getElementById('view-novelties-btn');

    // Inicializar modales Bootstrap
    const identifyPersonModalElement = document.getElementById('identifyPersonModal');
    const identifyVehicleModalElement = document.getElementById('identifyVehicleModal');
    const allNoveltiesModalElement = document.getElementById('allNoveltiesModal');
    if (identifyPersonModalElement) identifyPersonModal = new bootstrap.Modal(identifyPersonModalElement);
    if (identifyVehicleModalElement) identifyVehicleModal = new bootstrap.Modal(identifyVehicleModalElement);
    if (allNoveltiesModalElement) allNoveltiesModal = new bootstrap.Modal(allNoveltiesModalElement);

    // Obtener formularios de identificación
    identifyPersonForm = document.getElementById('identify-person-form');
    identifyVehicleForm = document.getElementById('identify-vehicle-form');
    identifyPersonError = document.getElementById('identify-person-error');
    identifyVehicleError = document.getElementById('identify-vehicle-error');

    // Inicializar mapa Leaflet
    const mapElement = document.getElementById('sim-map');
    if (mapElement) {
        console.log("[INIT] Inicializando mapa con coords por defecto:", currentLatitude, currentLongitude);
        map = L.map(mapElement).setView([currentLatitude, currentLongitude], 14);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19, attribution: '© OpenStreetMap'
         }).addTo(map);
         agentMarker = L.marker([currentLatitude, currentLongitude]).addTo(map).bindPopup("Ubicación Inicial");
         updateGpsDisplay();
    } else {
        console.error("[INIT] Elemento del mapa 'sim-map' no encontrado.");
    }

    // --- Añadir Listeners Generales ---
    if (loginFormSim) loginFormSim.addEventListener('submit', handleLoginSubmit);
    if (confirmNoveltiesBtn) confirmNoveltiesBtn.addEventListener('click', handleConfirmNovelties);
    if (confirmMobilityBtn) confirmMobilityBtn.addEventListener('click', handleConfirmMobilityAndStartShift);
    if (endShiftBtn) endShiftBtn.addEventListener('click', handleEndShift);
    if (logoutBtnNovelties) logoutBtnNovelties.addEventListener('click', handleLogout);
    if (logoutBtnMobility) logoutBtnMobility.addEventListener('click', handleLogout);
    if (logoutBtnActive) logoutBtnActive.addEventListener('click', handleLogout);
    if (viewNoveltiesBtn) viewNoveltiesBtn.addEventListener('click', fetchAndShowAllNovelties);

    // Listeners para identificaciones
    if (identifyPersonBtn) identifyPersonBtn.addEventListener('click', () => identifyPersonModal?.show());
    if (identifyVehicleBtn) identifyVehicleBtn.addEventListener('click', () => identifyVehicleModal?.show());
    if (identifyPersonForm) identifyPersonForm.addEventListener('submit', handleIdentifyPersonSubmit);
    if (identifyVehicleForm) identifyVehicleForm.addEventListener('submit', handleIdentifyVehicleSubmit);

    console.log("[INIT] Simulador inicializado correctamente.");
}

// --- Funciones de Utilidad ---

/** Muestra solo la sección especificada y oculta las demás */
function showSection(sectionName) {
    const sections = [loginSection, noveltiesSection, mobilitySelectionSection, activeShiftSection];
    sections.forEach(section => {
        if (section) section.style.display = 'none';
    });

    switch (sectionName) {
        case 'login':
            if (loginSection) loginSection.style.display = 'block';
            break;
        case 'novelties':
            if (noveltiesSection) noveltiesSection.style.display = 'block';
            break;
        case 'mobility-selection':
            if (mobilitySelectionSection) mobilitySelectionSection.style.display = 'block';
            break;
        case 'active-shift':
            if (activeShiftSection) activeShiftSection.style.display = 'block';
            break;
        default:
            console.warn(`[UI] Sección desconocida: ${sectionName}`);
    }
}

/** Actualiza la barra de estado */
function updateStatus(message, isActive = false) {
    if (statusBar) {
        statusBar.textContent = message;
        statusBar.className = isActive ? 'alert alert-success' : 'alert alert-warning';
    }
}

/** Muestra un error en el formulario de login */
function showLoginError(message) {
    if (loginErrorSim) {
        loginErrorSim.textContent = message;
        loginErrorSim.style.display = message ? 'block' : 'none';
    }
}

/** Muestra un error en la selección de movilidad */
function showMobilityError(message) {
    if (mobilityErrorDiv) {
        mobilityErrorDiv.textContent = message;
        mobilityErrorDiv.style.display = message ? 'block' : 'none';
    }
}

/** Actualiza el display de coordenadas GPS */
function updateGpsDisplay() {
    if (currentGpsDisplay) {
        const gpsStatus = isUsingRealGps ? "GPS Real" : "GPS Simulado";
        const accuracy = lastKnownAccuracy ? ` (±${lastKnownAccuracy.toFixed(1)}m)` : "";
        currentGpsDisplay.innerHTML = `
            <strong>Lat:</strong> ${currentLatitude.toFixed(6)}<br>
            <strong>Lng:</strong> ${currentLongitude.toFixed(6)}<br>
            <strong>Estado:</strong> ${gpsStatus}${accuracy}
        `;
    }
}

/** Actualiza el estado de los botones de acción */
function updateActionButtonState(isShiftActive) {
    const buttons = [identifyPersonBtn, identifyVehicleBtn, viewNoveltiesBtn];
    buttons.forEach(btn => {
        if (btn) btn.disabled = !isShiftActive;
    });
}

// --- Manejo de Login ---

/** Maneja el envío del formulario de login */
function handleLoginSubmit(event) {
    event.preventDefault();
    console.log("[LOGIN] Intento de login...");
    showLoginError('');
    const folio = agentFolioSimSelect.value;
    const password = agentPasswordSimInput.value;
    if (!folio || !password) { showLoginError("Selecciona agente e ingresa la contraseña."); return; }
    const submitButton = loginFormSim.querySelector('button[type="submit"]');
    submitButton.disabled = true; submitButton.textContent = 'Ingresando...';

    axios.post(`${apiBaseUrl}/auth/login`, { folio, password })
        .then(response => {
            if (response.data.access_token && response.data.user) {
                currentAgentToken = response.data.access_token;
                currentAgentFolio = response.data.user.folio;
                localStorage.setItem('agentToken', currentAgentToken);
                localStorage.setItem('agentFolio', currentAgentFolio);
                axios.defaults.headers.common['Authorization'] = `Bearer ${currentAgentToken}`;
                console.log("[LOGIN] Login exitoso para:", currentAgentFolio);
                console.log("[LOGIN] Token presente:", currentAgentToken);

                // <<< NUEVO >>> Notificar a Android si está disponible
                if (typeof AndroidInterface !== 'undefined' && AndroidInterface.onLoginSuccess) {
                    console.log("[LOGIN] Notificando token a Android...");
                    AndroidInterface.onLoginSuccess(currentAgentToken);
                } else {
                    console.log("[LOGIN] AndroidInterface no disponible (probablemente navegador web)");
                }

                // <<< MODIFICADO >>> Conectar WebSocket DESPUÉS de obtener token
                connectAgentWebSocket();

                console.log("[LOGIN] Llamando a initializeAndStartRealGps...");
                initializeAndStartRealGps(); // Iniciar intento de GPS REAL

                console.log("[LOGIN] Llamando a fetchUnreadNovelties...");
                fetchUnreadNovelties(); // Esto mostrará la sección de novedades
            } else {
                // Error si la respuesta no tiene token o user
                throw new Error(response.data.msg || "Respuesta de login inválida (faltan datos).");
            }
        })
        .catch(error => {
             console.error('[LOGIN] Login API error:', error.response || error);
             let msg = "Error en la conexión o respuesta del servidor.";
             if (error.response?.data?.msg) msg = error.response.data.msg;
             else if (error.response?.status === 401) msg = "Folio o contraseña incorrectos.";
             else if (error.message) msg = error.message;
             showLoginError(msg);
        })
        .finally(() => {
            submitButton.disabled = false; submitButton.textContent = 'Ingresar';
        });
}

// <<< AÑADIDO >>> Función para conectar WebSocket del Agente (Corregida)
function connectAgentWebSocket() {
    if (!currentAgentToken) {
        console.error("[WS_AGENT] No hay token para conectar WebSocket.");
        return;
    }
    if (agentSocket && agentSocket.connected) {
        console.log("[WS_AGENT] WebSocket ya conectado.");
        return;
    }

    console.log("[WS_AGENT] Intentando conectar WebSocket a:", API_BASE_URL_WS + '/live');
    // Conectar al NAMESPACE /live
    agentSocket = io(API_BASE_URL_WS + '/live', { // Asegúrate que la URL y namespace son correctos
        secure: true,
        transports: ['websocket'],
        auth: { token: currentAgentToken } // Pasar token en la opción 'auth'
    });

    // --- Listeners básicos del Socket ---
    agentSocket.on('connect', () => {
        console.log('[WS_AGENT] Conectado exitosamente al WebSocket /live!');
    });

    agentSocket.on('connect_error', (error) => {
        console.error('[WS_AGENT] Error de conexión WebSocket:', error);
        agentSocket = null; // Limpiar en caso de error de conexión persistente
        // Podrías añadir lógica de reintento aquí o notificar al usuario
    });

    agentSocket.on('auth_error', (error) => { // Espera 'auth_error' del servidor
        console.error('[WS_AGENT] Error de autenticación WebSocket:', error.msg || error);
        alert(`Error de autenticación en tiempo real: ${error.msg || 'Desconocido'}. La sesión se cerrará.`);
        handleLogout(); // Forzar logout si la autenticación WS falla
    });

    agentSocket.on('disconnect', (reason) => {
        console.warn('[WS_AGENT] WebSocket desconectado:', reason);
        // Podrías añadir lógica de reconexión automática aquí
    });
}

// --- Manejo de Novedades ---

/** Obtiene y muestra las novedades no leídas */
function fetchUnreadNovelties() {
    if (!currentAgentToken) return;
    console.log("[NOVELTIES] Obteniendo novedades no leídas...");

    axios.get(`${apiBaseUrl}/novelties/unread`)
        .then(response => {
            const novelties = response.data;
            console.log(`[NOVELTIES] ${novelties.length} novedades no leídas encontradas.`);
            displayNovelties(novelties);
            showSection('novelties');
        })
        .catch(error => {
            console.error('[NOVELTIES] Error obteniendo novedades:', error.response || error);
            // Si no hay novedades o hay error, ir directo a selección de movilidad
            console.log("[NOVELTIES] Saltando a selección de movilidad...");
            showMobilitySelection();
        });
}

/** Muestra las novedades en la interfaz */
function displayNovelties(novelties) {
    if (!noveltiesListDiv) return;

    if (novelties.length === 0) {
        noveltiesListDiv.innerHTML = '<p class="text-muted">No hay novedades pendientes.</p>';
        // Auto-continuar si no hay novedades
        setTimeout(() => showMobilitySelection(), 1000);
        return;
    }

    let html = '<div class="row">';
    novelties.forEach(novelty => {
        html += `
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input novelty-checkbox" type="checkbox" value="${novelty.id}" id="novelty-${novelty.id}">
                            <label class="form-check-label" for="novelty-${novelty.id}">
                                <strong>${novelty.title || 'Sin título'}</strong>
                            </label>
                        </div>
                        <p class="card-text mt-2">${novelty.description || 'Sin descripción'}</p>
                        <small class="text-muted">Creado: ${dayjs(novelty.created_at).format('DD/MM/YYYY HH:mm')}</small>
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';
    noveltiesListDiv.innerHTML = html;
}

/** Confirma lectura de Novedades y pasa a seleccionar movilidad */
function handleConfirmNovelties() {
    if (!currentAgentToken) return;
    confirmNoveltiesBtn.disabled = true;
    confirmNoveltiesBtn.textContent = 'Marcando...';
    const checkedNovelties = noveltiesListDiv.querySelectorAll('.novelty-checkbox:checked');
    const promises = [];
    checkedNovelties.forEach(cb => {
        const noveltyId = cb.value;
        if (noveltyId) {
             promises.push(
                 axios.post(`${apiBaseUrl}/novelties/${noveltyId}/read`)
                    .then(() => console.log(`[NOVELTY] Novedad ${noveltyId} marcada como leída.`))
                    .catch(err => console.warn(`[NOVELTY] Error marcando ${noveltyId}:`, err.response?.data?.msg || err.message))
            );
        }
    });

    Promise.allSettled(promises)
        .then(() => {
            console.log("[NOVELTIES] Proceso de marcado completado.");
            showMobilitySelection();
        })
        .finally(() => {
            confirmNoveltiesBtn.disabled = false;
            confirmNoveltiesBtn.textContent = 'Confirmar Lectura y Continuar';
        });
}

// --- Selección de Movilidad ---

/** Muestra la sección de selección de movilidad */
function showMobilitySelection() {
    console.log("[MOBILITY] Mostrando selección de movilidad...");
    if (mobilityOptionsDiv) {
        mobilityOptionsDiv.innerHTML = `
            <div class="row">
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-body text-center">
                            <input type="radio" class="form-check-input" name="mobilityType" value="Peatonal" id="mobility-peatonal" checked>
                            <label for="mobility-peatonal" class="form-check-label">
                                <i class="fas fa-walking fa-2x d-block mb-2"></i>
                                <strong>Peatonal</strong>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-body text-center">
                            <input type="radio" class="form-check-input" name="mobilityType" value="Vehicular" id="mobility-vehicular">
                            <label for="mobility-vehicular" class="form-check-label">
                                <i class="fas fa-car fa-2x d-block mb-2"></i>
                                <strong>Vehicular</strong>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-body text-center">
                            <input type="radio" class="form-check-input" name="mobilityType" value="Motocicleta" id="mobility-moto">
                            <label for="mobility-moto" class="form-check-label">
                                <i class="fas fa-motorcycle fa-2x d-block mb-2"></i>
                                <strong>Motocicleta</strong>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    showSection('mobility-selection');
}

/** Confirma movilidad e inicia el turno en el backend */
function handleConfirmMobilityAndStartShift() {
    if (!currentAgentToken) return;
    showMobilityError('');
    const selectedRadio = document.querySelector('input[name="mobilityType"]:checked');
    if (!selectedRadio || !selectedRadio.value) { showMobilityError("Debes seleccionar un modo de trabajo."); return; }
    selectedMobilityType = selectedRadio.value;
    console.log(`Movilidad seleccionada: ${selectedMobilityType}. Iniciando turno...`);
    confirmMobilityBtn.disabled = true; confirmMobilityBtn.textContent = 'Iniciando Turno...';
    startShift(selectedMobilityType);
}

/** Registra el inicio del turno en el backend e inicia seguimiento GPS */
function startShift(mobilityType) {
    if (!mobilityType) {
        console.error("startShift llamado sin mobilityType");
        showMobilityError("Error interno: Falta tipo de movilidad.");
        confirmMobilityBtn.disabled = false;
        confirmMobilityBtn.textContent = 'Confirmar Modo e Iniciar Turno';
        return;
    }
    const payload = {
        mobility_type: mobilityType,
        unit_id: 'SIMULATED_UNIT'
    };

    axios.post(`${apiBaseUrl}/shifts/start`, payload)
        .then(response => {
            const shiftData = response.data.shift;
            if (shiftData?.id) {
                currentShiftId = shiftData.id;
                console.log(`Turno backend iniciado: ${currentShiftId}, Modo: ${mobilityType}`);
                if (currentAgentFolioSpan) currentAgentFolioSpan.textContent = currentAgentFolio || 'N/A';
                if (currentMobilityDisplaySpan) currentMobilityDisplaySpan.textContent = mobilityType;

                initializeAndStartRealGps();
                updateStatus(`Turno ${currentShiftId} ACTIVO (${mobilityType})`, true);
                updateActionButtonState(true);
                showSection('active-shift');
            } else {
                throw new Error("Respuesta del servidor no contiene ID de turno válido.");
            }
        })
        .catch(error => {
            console.error('[SHIFT_START] Error iniciando turno:', error.response || error);
            let msg = "Error iniciando turno en el servidor.";
            if (error.response?.data?.msg) msg = error.response.data.msg;
            showMobilityError(msg);
        })
        .finally(() => {
            confirmMobilityBtn.disabled = false;
            confirmMobilityBtn.textContent = 'Confirmar Modo e Iniciar Turno';
        });
}

// --- Manejo de GPS ---

/** Inicializa y comienza el seguimiento GPS real */
function initializeAndStartRealGps() {
    console.log("[GPS] Inicializando GPS real...");

    if (!navigator.geolocation) {
        console.warn("[GPS] Geolocalización no soportada por el navegador.");
        isUsingRealGps = false;
        updateGpsDisplay();
        startGpsSending();
        return;
    }

    // Opciones para geolocalización de alta precisión
    const gpsOptions = {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 5000
    };

    // Obtener posición inicial
    navigator.geolocation.getCurrentPosition(
        (position) => {
            console.log("[GPS] Posición inicial obtenida:", position.coords);
            updateLocationFromGps(position.coords);
            startContinuousGpsTracking(gpsOptions);
        },
        (error) => {
            console.warn("[GPS] Error obteniendo posición inicial:", error.message);
            isUsingRealGps = false;
            updateGpsDisplay();
            startGpsSending();
        },
        gpsOptions
    );
}

/** Inicia el seguimiento continuo de GPS */
function startContinuousGpsTracking(options) {
    if (realGpsWatchId) {
        navigator.geolocation.clearWatch(realGpsWatchId);
    }

    realGpsWatchId = navigator.geolocation.watchPosition(
        (position) => {
            updateLocationFromGps(position.coords);
        },
        (error) => {
            console.warn("[GPS] Error en seguimiento continuo:", error.message);
            // No cambiar isUsingRealGps aquí para evitar oscilaciones
        },
        options
    );

    startGpsSending();
}

/** Actualiza la ubicación desde coordenadas GPS */
function updateLocationFromGps(coords) {
    currentLatitude = coords.latitude;
    currentLongitude = coords.longitude;
    lastKnownAccuracy = coords.accuracy;
    isUsingRealGps = true;

    // Actualizar mapa si existe
    if (map && agentMarker) {
        agentMarker.setLatLng([currentLatitude, currentLongitude]);
        map.setView([currentLatitude, currentLongitude], map.getZoom());
        agentMarker.bindPopup(`GPS Real: ${currentLatitude.toFixed(6)}, ${currentLongitude.toFixed(6)}`).openPopup();
    }

    updateGpsDisplay();
    console.log(`[GPS] Ubicación actualizada: ${currentLatitude.toFixed(6)}, ${currentLongitude.toFixed(6)} (±${lastKnownAccuracy?.toFixed(1)}m)`);
}

/** Inicia el envío periódico de coordenadas GPS */
function startGpsSending() {
    if (gpsSendIntervalId) {
        clearInterval(gpsSendIntervalId);
    }

    gpsSendIntervalId = setInterval(() => {
        sendRealGpsPoint(currentLatitude, currentLongitude, lastKnownAccuracy);
    }, GPS_SEND_INTERVAL_MS);

    console.log(`[GPS] Envío periódico iniciado cada ${GPS_SEND_INTERVAL_MS}ms`);
}

/** Envía las coordenadas reales al backend */
function sendRealGpsPoint(lat, lon, acc) {
    if (!currentAgentToken || !currentShiftId) {
        return;
    }

    const payload = {
        latitude: lat,
        longitude: lon,
        accuracy: acc !== null ? parseFloat(acc.toFixed(1)) : null
    };

    axios.post(`${apiBaseUrl}/shifts/track`, payload)
        .then(response => {
            const gpsStatus = isUsingRealGps ? "GPS Real OK" : "GPS Simulado/Error";
            if (statusBar && statusBar.textContent.includes("Error Envío GPS")) {
                updateStatus(`Turno ${currentShiftId} ACTIVO (${selectedMobilityType}) - ${gpsStatus}`, true);
            }
        })
        .catch(error => {
            console.error('[GPS_SEND] Error enviando coordenadas:', error.response || error);
            updateStatus(`Turno ${currentShiftId} ACTIVO (${selectedMobilityType}) - Error Envío GPS`, false);
        });
}

// --- Manejo de Logout ---

/** Maneja el logout del agente */
function handleLogout() {
    console.log("[LOGOUT] Iniciando logout...");

    // Detener GPS
    if (realGpsWatchId) {
        navigator.geolocation.clearWatch(realGpsWatchId);
        realGpsWatchId = null;
    }
    if (gpsSendIntervalId) {
        clearInterval(gpsSendIntervalId);
        gpsSendIntervalId = null;
    }

    // Cerrar WebSocket
    if (agentSocket) {
        agentSocket.disconnect();
        agentSocket = null;
    }

    // Limpiar estado global
    currentAgentToken = null;
    currentAgentFolio = null;
    localStorage.removeItem('agentToken');
    localStorage.removeItem('agentFolio');
    currentShiftId = null;
    selectedMobilityType = 'Peatonal';
    isUsingRealGps = false;
    lastKnownAccuracy = null;
    currentLatitude = -40.8134;
    currentLongitude = -62.9967;

    // Limpiar headers de Axios
    if (axios) axios.defaults.headers.common['Authorization'] = '';

    // Resetear UI
    updateStatus("Desconectado", false);
    updateActionButtonState(false);
    showSection('login');

    console.log("[LOGOUT] Logout completado.");
}

// --- Manejo de Turnos ---

/** Maneja el fin del turno */
function handleEndShift() {
    if (!currentAgentToken || !currentShiftId) return;

    console.log("[SHIFT_END] Finalizando turno...");
    endShiftBtn.disabled = true;
    endShiftBtn.textContent = 'Finalizando...';

    axios.post(`${apiBaseUrl}/shifts/end`)
        .then(response => {
            console.log("[SHIFT_END] Turno finalizado exitosamente");
            currentShiftId = null;

            // Detener GPS
            if (realGpsWatchId) {
                navigator.geolocation.clearWatch(realGpsWatchId);
                realGpsWatchId = null;
            }
            if (gpsSendIntervalId) {
                clearInterval(gpsSendIntervalId);
                gpsSendIntervalId = null;
            }

            updateStatus("Turno finalizado", false);
            updateActionButtonState(false);
            showSection('login');
        })
        .catch(error => {
            console.error('[SHIFT_END] Error finalizando turno:', error.response || error);
            alert("Error finalizando turno. Intenta nuevamente.");
        })
        .finally(() => {
            endShiftBtn.disabled = false;
            endShiftBtn.textContent = 'Finalizar Turno';
        });
}

// --- Manejo de Identificaciones ---

/** Maneja el envío del formulario de identificación de persona */
function handleIdentifyPersonSubmit(event) {
    event.preventDefault();
    if (!currentAgentToken || !currentShiftId) return;

    const formData = new FormData(identifyPersonForm);
    const payload = {
        dni_raw_string: formData.get('dni_raw_string') || '',
        dni_number: formData.get('dni_number') || '',
        last_name: formData.get('last_name') || '',
        first_names: formData.get('first_names') || '',
        notes: formData.get('notes') || '',
        gender: formData.get('gender') || '',
        dob: formData.get('dob') || '',
        latitude: currentLatitude,
        longitude: currentLongitude
    };

    // Validación básica
    if (!payload.dni_raw_string && !payload.dni_number && !(payload.last_name && payload.first_names)) {
        showIdentifyPersonError("Ingresa los datos del DNI o al menos Apellido y Nombres.");
        return;
    }

    showIdentifyPersonError('');
    const submitBtn = identifyPersonForm.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.textContent = 'Identificando...';

    axios.post(`${apiBaseUrl}/identifications/person`, payload)
        .then(response => {
            console.log("[IDENTIFY_PERSON] Identificación exitosa:", response.data);

            // Emitir WebSocket si está conectado
            if (agentSocket && agentSocket.connected && currentAgentFolio) {
                agentSocket.emit('update_identification', {
                    folio: currentAgentFolio,
                    type: 'person',
                    latitude: currentLatitude,
                    longitude: currentLongitude,
                    identification_data: {
                        dni: response.data.dni_number || payload.dni_number || 'S/DNI',
                        last_name: response.data.last_name || payload.last_name || '',
                        first_names: response.data.first_names || payload.first_names || ''
                    }
                });
            }

            identifyPersonModal?.hide();
            identifyPersonForm.reset();
            alert("Persona identificada exitosamente");
        })
        .catch(error => {
            console.error('[IDENTIFY_PERSON] Error:', error.response || error);
            let msg = "Error identificando persona.";
            if (error.response?.data?.msg) msg = error.response.data.msg;
            showIdentifyPersonError(msg);
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = 'Identificar Persona';
        });
}

/** Maneja el envío del formulario de identificación de vehículo */
function handleIdentifyVehicleSubmit(event) {
    event.preventDefault();
    if (!currentAgentToken || !currentShiftId) return;

    const formData = new FormData(identifyVehicleForm);
    const payload = {
        plate: formData.get('plate') || '',
        brand: formData.get('brand') || '',
        model: formData.get('model') || '',
        color: formData.get('color') || '',
        notes: formData.get('notes') || '',
        latitude: currentLatitude,
        longitude: currentLongitude
    };

    if (!payload.plate) {
        showIdentifyVehicleError("La patente es obligatoria.");
        return;
    }

    showIdentifyVehicleError('');
    const submitBtn = identifyVehicleForm.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.textContent = 'Identificando...';

    axios.post(`${apiBaseUrl}/identifications/vehicle`, payload)
        .then(response => {
            console.log("[IDENTIFY_VEHICLE] Identificación exitosa:", response.data);

            // Emitir WebSocket si está conectado
            if (agentSocket && agentSocket.connected && currentAgentFolio) {
                agentSocket.emit('update_identification', {
                    folio: currentAgentFolio,
                    type: 'vehicle',
                    latitude: currentLatitude,
                    longitude: currentLongitude,
                    identification_data: {
                        plate: response.data.plate || payload.plate || 'S/DOM',
                        brand: response.data.brand || payload.brand || '',
                        model: response.data.model || payload.model || ''
                    }
                });
            }

            identifyVehicleModal?.hide();
            identifyVehicleForm.reset();
            alert("Vehículo identificado exitosamente");
        })
        .catch(error => {
            console.error('[IDENTIFY_VEHICLE] Error:', error.response || error);
            let msg = "Error identificando vehículo.";
            if (error.response?.data?.msg) msg = error.response.data.msg;
            showIdentifyVehicleError(msg);
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = 'Identificar Vehículo';
        });
}

/** Muestra error en identificación de persona */
function showIdentifyPersonError(message) {
    if (identifyPersonError) {
        identifyPersonError.textContent = message;
        identifyPersonError.style.display = message ? 'block' : 'none';
    }
}

/** Muestra error en identificación de vehículo */
function showIdentifyVehicleError(message) {
    if (identifyVehicleError) {
        identifyVehicleError.textContent = message;
        identifyVehicleError.style.display = message ? 'block' : 'none';
    }
}

// --- Manejo de Novedades (Ver todas) ---

/** Obtiene y muestra todas las novedades en modal */
function fetchAndShowAllNovelties() {
    if (!currentAgentToken) return;

    axios.get(`${apiBaseUrl}/novelties`)
        .then(response => {
            const novelties = response.data;
            displayAllNoveltiesModal(novelties);
            allNoveltiesModal?.show();
        })
        .catch(error => {
            console.error('[ALL_NOVELTIES] Error:', error.response || error);
            alert("Error cargando novedades");
        });
}

/** Muestra todas las novedades en el modal */
function displayAllNoveltiesModal(novelties) {
    const modalBody = document.querySelector('#allNoveltiesModal .modal-body');
    if (!modalBody) return;

    if (novelties.length === 0) {
        modalBody.innerHTML = '<p class="text-muted">No hay novedades disponibles.</p>';
        return;
    }

    let html = '';
    novelties.forEach(novelty => {
        const isRead = novelty.is_read ? 'text-muted' : '';
        const readBadge = novelty.is_read ? '<span class="badge bg-success">Leída</span>' : '<span class="badge bg-warning">No leída</span>';

        html += `
            <div class="card mb-3 ${isRead}">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <h6 class="card-title">${novelty.title || 'Sin título'}</h6>
                        ${readBadge}
                    </div>
                    <p class="card-text">${novelty.description || 'Sin descripción'}</p>
                    <small class="text-muted">
                        Creado: ${dayjs(novelty.created_at).format('DD/MM/YYYY HH:mm')}
                        ${novelty.read_at ? ` | Leído: ${dayjs(novelty.read_at).format('DD/MM/YYYY HH:mm')}` : ''}
                    </small>
                </div>
            </div>
        `;
    });

    modalBody.innerHTML = html;
}

// --- Función de Prueba del Android Interface ---

/** Función para probar el AndroidInterface después de la carga completa */
function testAndroidInterface() {
    console.log("[TEST] Probando AndroidInterface...");

    if (typeof AndroidInterface !== 'undefined' && AndroidInterface.onLoginSuccess) {
        console.log("[TEST] Enviando token de prueba a Android...");
        try {
            // Obtener token actual si existe
            const testToken = localStorage.getItem('agentToken') || 'test_token_123';
            AndroidInterface.onLoginSuccess(testToken);
            console.log("[TEST] ✅ Token enviado exitosamente a Android");
        } catch (error) {
            console.error("[TEST] ❌ Error enviando token a Android:", error);
        }
    } else {
        console.log("[TEST] ❌ AndroidInterface no disponible para prueba");
    }
}

// Ejecutar prueba después de 2 segundos de carga
setTimeout(testAndroidInterface, 2000);
