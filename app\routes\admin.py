# app/routes/admin.py

from flask import Blueprint, render_template, redirect, url_for, flash
from flask_login import login_required, current_user
from app.decorators import role_required
from app.forms import CreateUserForm
from app.models import User, Role
from app import db

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/dashboard')
@login_required
@role_required('ADMIN')
def dashboard():
    return render_template('admin/dashboard.html')

@admin_bp.route('/crear_supervisor', methods=['GET', 'POST'])
@login_required
@role_required('ADMIN')
def crear_supervisor():
    form = CreateUserForm()
    if form.validate_on_submit():
        # Verificar si el usuario ya existe
        existing_user = User.query.filter(
            (User.username == form.username.data) | (User.email == form.email.data)
        ).first()
        if existing_user:
            flash('El usuario o correo ya existe. Por favor, elige otro.', 'danger')
            return redirect(url_for('admin.crear_supervisor'))
        
        # Crear el nuevo usuario
        new_user = User(
            username=form.username.data,
            email=form.email.data,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            phone=form.phone.data
        )
        new_user.set_password(form.password.data)
        
        # Asignar el rol 'SUPERVISOR'
        supervisor_role = Role.query.filter_by(role_name='SUPERVISOR').first()
        if supervisor_role:
            new_user.roles.append(supervisor_role)
        else:
            flash('El rol SUPERVISOR no existe.', 'danger')
            return redirect(url_for('admin.crear_supervisor'))
        
        # Agregar y confirmar los cambios en la base de datos
        db.session.add(new_user)
        db.session.commit()
        
        flash(f"Supervisor '{new_user.username}' creado exitosamente.", 'success')
        return redirect(url_for('admin.dashboard'))
    
    return render_template('admin/crear_supervisor.html', form=form)
