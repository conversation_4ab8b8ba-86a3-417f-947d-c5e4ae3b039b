/**
 * Puente JavaScript para comunicación con la aplicación Android
 * Agregar este script al template dashboard_hijo.html
 */

// Detectar si estamos en la aplicación Android
const isAndroidApp = typeof AndroidApp !== 'undefined';

console.log('Android App detected:', isAndroidApp);

if (isAndroidApp) {
    console.log('Configurando puente Android...');
    
    // Sobrescribir las funciones de notificación para usar la interfaz nativa
    window.sendNotificationToParent = function(notificationType, message = '') {
        console.log('Enviando notificación via Android:', notificationType);
        
        try {
            const result = AndroidApp.sendNotification(notificationType, message);
            console.log('Resultado de notificación:', result);
            return JSON.parse(result);
        } catch (e) {
            console.error('Error enviando notificación via Android:', e);
            return { success: false, message: 'Error de comunicación' };
        }
    };
    
    // Sobrescribir la función de ubicación para usar el servicio nativo
    window.getCurrentLocation = function() {
        console.log('Obteniendo ubicación via Android...');
        
        // La app Android ya está enviando ubicaciones automáticamente
        // Solo necesitamos indicar que el GPS está activo
        return {
            success: true,
            message: 'GPS activo via aplicación Android'
        };
    };
    
    // Función para obtener el child_id desde la app
    window.getChildId = function() {
        try {
            return AndroidApp.getChildId();
        } catch (e) {
            console.error('Error obteniendo child_id:', e);
            return null;
        }
    };
    
    // Verificar si el servicio de ubicación está activo
    window.isLocationServiceActive = function() {
        try {
            return AndroidApp.isLocationServiceActive();
        } catch (e) {
            console.error('Error verificando servicio de ubicación:', e);
            return false;
        }
    };
    
    // Callback para cuando se envía una notificación
    window.notificationSent = function(success) {
        console.log('Notificación enviada:', success);
        
        // Actualizar la UI según el resultado
        const statusElement = document.querySelector('.notification-status');
        if (statusElement) {
            if (success) {
                statusElement.textContent = '✅ Notificación enviada';
                statusElement.className = 'notification-status success';
            } else {
                statusElement.textContent = '❌ Error al enviar notificación';
                statusElement.className = 'notification-status error';
            }
            
            // Limpiar el mensaje después de 3 segundos
            setTimeout(() => {
                statusElement.textContent = '';
                statusElement.className = 'notification-status';
            }, 3000);
        }
    };
    
    // Sobrescribir las funciones existentes del dashboard
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM cargado, configurando eventos Android...');
        
        // Actualizar el estado del GPS
        const gpsStatus = document.querySelector('.gps-status');
        if (gpsStatus) {
            if (window.isLocationServiceActive()) {
                gpsStatus.innerHTML = 'GPS Logging: <span style="color: green;">Active</span> - Ubicación compartida via app Android';
            }
        }
        
        // Interceptar clicks en botones de notificación
        const notificationButtons = document.querySelectorAll('[onclick*="sendNotification"]');
        notificationButtons.forEach(button => {
            const originalOnclick = button.getAttribute('onclick');
            
            // Extraer el tipo de notificación del onclick original
            const match = originalOnclick.match(/sendNotification\(['"]([^'"]+)['"]/);
            if (match) {
                const notificationType = match[1];
                
                button.onclick = function(e) {
                    e.preventDefault();
                    console.log('Interceptando click de notificación:', notificationType);
                    
                    // Usar la función nativa de Android
                    window.sendNotificationToParent(notificationType);
                    
                    return false;
                };
            }
        });
        
        // Interceptar formularios de notificación
        const notificationForms = document.querySelectorAll('form[action*="send_notification"]');
        notificationForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(form);
                const notificationType = formData.get('notification_type') || 'custom';
                const message = formData.get('message_text') || '';
                
                console.log('Interceptando envío de formulario:', notificationType, message);
                
                // Usar la función nativa de Android
                window.sendNotificationToParent(notificationType, message);
                
                return false;
            });
        });
        
        // Deshabilitar la geolocalización web ya que usamos la nativa
        if (navigator.geolocation) {
            const originalGetCurrentPosition = navigator.geolocation.getCurrentPosition;
            navigator.geolocation.getCurrentPosition = function(success, error, options) {
                console.log('Geolocalización web interceptada, usando servicio Android');
                
                // Simular una posición exitosa
                if (success) {
                    success({
                        coords: {
                            latitude: 0,
                            longitude: 0,
                            accuracy: 10
                        },
                        timestamp: Date.now()
                    });
                }
            };
        }
    });
    
} else {
    console.log('No estamos en la aplicación Android, usando funciones web normales');
}

// Agregar estilos para los estados de notificación
const style = document.createElement('style');
style.textContent = `
    .notification-status {
        padding: 10px;
        margin: 10px 0;
        border-radius: 5px;
        font-weight: bold;
        text-align: center;
    }
    .notification-status.success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    .notification-status.error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
`;
document.head.appendChild(style);

console.log('Puente Android configurado completamente');
