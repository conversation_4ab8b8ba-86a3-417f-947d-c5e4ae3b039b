 <!-- app/templates/users/gestionar_hijos.html -->
 {% extends 'base.html' %}

 {% block content %}
   <h1>Gestionar Hijos</h1>
   <table class="table">
     <thead>
       <tr>
         <th>ID</th>
         <th>Nombre</th>
         <th>Apellido</th>
         <th>Acciones</th>
       </tr>
     </thead>
     <tbody>
       {% for child in children %}
         <tr>
           <td>{{ child.child_id }}</td>
           <td>{{ child.first_name }}</td>
           <td>{{ child.last_name }}</td>
           <td>
             <a href="{{ url_for('users.editar_hijo', child_id=child.child_id) }}" class="btn btn-primary btn-sm">Editar</a>
             <form action="{{ url_for('users.eliminar_hijo', child_id=child.child_id) }}" method="POST" style="display:inline;">
               <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
               <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('¿Estás seguro de eliminar este hijo?');">Eliminar</button>
             </form>
           </td>
         </tr>
       {% endfor %}
     </tbody>
   </table>
 {% endblock %}
 