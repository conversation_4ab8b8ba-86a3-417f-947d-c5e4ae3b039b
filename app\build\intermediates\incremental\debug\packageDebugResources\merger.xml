<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res"><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="activity_login" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_main_child" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\layout\activity_main_child.xml" qualifiers="" type="layout"/><file name="activity_main_parent" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\layout\activity_main_parent.xml" qualifiers="" type="layout"/><file name="activity_profile_selection" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\layout\activity_profile_selection.xml" qualifiers="" type="layout"/><file name="activity_splash" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="activity_token" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\layout\activity_token.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary_color">#2196F3</color><color name="accent_color">#FF9800</color><color name="background_color">#F5F5F5</color><color name="text_primary">#212121</color><color name="text_secondary">#757575</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Corredores</string><string name="loading">Cargando...</string><string name="error_connection">Error de conexión</string><string name="error_permissions">Permisos requeridos</string><string name="checking_permissions">Verificando permisos...</string><string name="select_profile">Selecciona tu perfil</string><string name="profile_parent">Padre</string><string name="profile_child">Hijo</string><string name="select">Seleccionar</string><string name="login">Iniciar Sesión</string><string name="folio">Folio</string><string name="password">Contraseña</string><string name="change_profile">Cambiar perfil</string><string name="enter_token">Ingresa el token</string><string name="token">Token</string><string name="connect">Conectar</string><string name="permission_location_title">Permisos de Ubicación</string><string name="permission_location_message">Esta aplicación necesita acceso a tu ubicación para funcionar correctamente.</string><string name="permission_background_title">Ubicación en Segundo Plano</string><string name="permission_background_message">Para un seguimiento continuo, necesitamos acceso a la ubicación en segundo plano.</string><string name="gps_disabled_title">GPS Deshabilitado</string><string name="gps_disabled_message">Por favor, habilita el GPS para continuar.</string><string name="allow">Permitir</string><string name="exit">Salir</string><string name="settings">Configuración</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.Corredores" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.Corredores" parent="Base.Theme.Corredores"/></file><file path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.Corredores" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\build\generated\res\processDebugGoogleServices"><file path="C:\Users\<USER>\AndroidStudioProjects\corredores\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com</string><string name="gcm_defaultSenderId" translatable="false">123456789012</string><string name="google_api_key" translatable="false">AIzaSyDummyKeyForTemporaryUse123456789012345</string><string name="google_app_id" translatable="false">1:123456789012:android:abcdef1234567890abcdef</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyDummyKeyForTemporaryUse123456789012345</string><string name="google_storage_bucket" translatable="false">corredores-seguros-temp.appspot.com</string><string name="project_id" translatable="false">corredores-seguros-temp</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>