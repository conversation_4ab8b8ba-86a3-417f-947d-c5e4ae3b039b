-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:2:1-97:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:2:1-97:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:2:1-97:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:2:1-97:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2da938e0e51246f58401e1e70a8344\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\93edbca5700ad9cb25d78eb02bade720\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\967147dcae910194fa274d2c250149cc\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3638d6c7c3d7b87d5f2eabc07b8b0e57\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbb63f8e3ee649ebb59cd4bb90450e99\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79364b3e190ffce9f2dea0a1da6759a7\transformed\play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee22392c0511e6aba80092990f3de4a\transformed\play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b42534c1ac90d64a4f8f7e11550c6cfd\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3f8bb670ae22105397c8335fe93c8bd\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb3e29ff08c07d3daf20d3f651cc8eee\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:15:1-41:12
MERGED from [androidx.webkit:webkit:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6f715e2c0250f74384a9309ebf14495\transformed\webkit-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35d799fceb32dbef952e474ebfc145ee\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e0f0a33a4832529aa72c6af15bff48c\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba55085bc3cc0f7fd03bf666ca981f5a\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c73a9538e3525628e2bf6c7b18e4ece\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bee524264b6054cb37707b6a7fac0cf0\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db346f513417c3ce5b708b5c5a9189f3\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a319b3c553e8ff493eba9a02f3e67d8d\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bd61a142146d3441faa191edd4584bb\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:17:1-145:12
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bb51bc14d126169e2ad1a1340f8326b\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0a34a75fcc25ba26072520862f515e0\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998bd82a33703232c8d116bebe281ded\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6aa9534116cd506754db97828c361ce2\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eaaf152c12373f2f89641935564da2a8\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5737cdfcf2118fdd50ca4f4332956ac\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1822edcf5d2328903154cc41cade1db2\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20d46420d97e351d7735ec8379a46b9f\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df67eaaf0b48836253fb504b6c9a6346\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\060432f54d80cc8e659316157dc70d71\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0aa488a77f7e697087baeb6ea75979bf\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19590ff9a3e0873f1d96063fe131eb36\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101cca20c1e85df7a5f9f46711f93017\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf26778305f000d2d1525106f6580765\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\acfe9f8b774d2c354443b3078d546139\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\636079ac1538b7c7227947a9db213969\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe4373c4482e7e798dd3da2e28e74cfc\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3e428614568415b5436ae0870877c8\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5dbcc4bae08e2d37d02b09ba5c09d41\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcb3250c1492934d3ece20c483063ed7\transformed\room-ktx-2.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d67d231637ce3b2a99ecff45dd34195d\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f85ba276f1370d20237343255144de\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6a709064629cb6841c3f579a88108f5\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\108e8ff76d198e53371c5c0cc08de0fc\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d33dc9df7056a5cc3da3a2f633312e4\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dadc04ed2230b2d1189f5e1acba49bdf\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ecfdb2e8fe388fbc3bdb49113026973d\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9eb20b8d50cf080e1d9f117bbd0cc6e\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df2b181811aa594d3aa507b5c71cec2c\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c991df00d522f98e9d793f316fd1baf8\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea606996c8ba6337c1770e421181b43\transformed\play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fd2b2904cc709cf74ecc96ad3a8f907\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a86e451e335fe170cc22e2bbb8f32c2\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e354fd7c956ed6175d4f117257206ed7\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07d177d4395d04d87ca62bf88051dbbc\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e16c9886a5aca2cccfc433075ae7f19d\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ecd105970e5114bb9554f865bbee88c\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0773a4b518b1ace8239e66fc72f6925\transformed\firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\095ec3180b911c35eacd1feab6e0eef8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d73aef2def23a6c1775a61c1e9f05979\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\609a67b0ce1ff57138f6d6eb42472158\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4255dc82103757f0fee75d7adebc614f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9951833a5599c2e5d44cc8e9213addc\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bda673c447e21be587edbfdcc61f30bc\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79130a54814058327ed4bab63fb1123c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:15:1-33:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd1d6560a6a3e77e92af3163a068a7a5\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b81aab4cb0d3fabdf7da10392cc10b5\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a36122275415b171d4a041932ae5346\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\629bd9b8d35e1fae891a3284a78b6369\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c20d3e50f2d03b00a44b9f34a59ec44\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b741871d3d36ce368591da2830fa6c16\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c808468632754ca24252791f884893f\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:6:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:7:5-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:7:22-78
uses-permission#android.permission.ACCESS_BACKGROUND_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:8:5-85
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:8:22-82
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0a34a75fcc25ba26072520862f515e0\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0a34a75fcc25ba26072520862f515e0\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\108e8ff76d198e53371c5c0cc08de0fc\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\108e8ff76d198e53371c5c0cc08de0fc\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df2b181811aa594d3aa507b5c71cec2c\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df2b181811aa594d3aa507b5c71cec2c\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:11:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:12:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0a34a75fcc25ba26072520862f515e0\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0a34a75fcc25ba26072520862f515e0\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\108e8ff76d198e53371c5c0cc08de0fc\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\108e8ff76d198e53371c5c0cc08de0fc\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df2b181811aa594d3aa507b5c71cec2c\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df2b181811aa594d3aa507b5c71cec2c\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:12:22-76
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:15:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:15:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:16:5-86
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:16:22-83
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:17:5-68
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0a34a75fcc25ba26072520862f515e0\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0a34a75fcc25ba26072520862f515e0\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\108e8ff76d198e53371c5c0cc08de0fc\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\108e8ff76d198e53371c5c0cc08de0fc\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df2b181811aa594d3aa507b5c71cec2c\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df2b181811aa594d3aa507b5c71cec2c\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:17:22-65
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:18:5-95
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:18:22-92
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:21:5-81
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:21:22-78
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:23:5-95:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:23:5-95:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2da938e0e51246f58401e1e70a8344\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2da938e0e51246f58401e1e70a8344\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\93edbca5700ad9cb25d78eb02bade720\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\93edbca5700ad9cb25d78eb02bade720\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79364b3e190ffce9f2dea0a1da6759a7\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79364b3e190ffce9f2dea0a1da6759a7\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee22392c0511e6aba80092990f3de4a\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee22392c0511e6aba80092990f3de4a\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b42534c1ac90d64a4f8f7e11550c6cfd\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b42534c1ac90d64a4f8f7e11550c6cfd\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3f8bb670ae22105397c8335fe93c8bd\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3f8bb670ae22105397c8335fe93c8bd\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb3e29ff08c07d3daf20d3f651cc8eee\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb3e29ff08c07d3daf20d3f651cc8eee\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bb51bc14d126169e2ad1a1340f8326b\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bb51bc14d126169e2ad1a1340f8326b\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0a34a75fcc25ba26072520862f515e0\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0a34a75fcc25ba26072520862f515e0\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998bd82a33703232c8d116bebe281ded\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998bd82a33703232c8d116bebe281ded\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101cca20c1e85df7a5f9f46711f93017\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101cca20c1e85df7a5f9f46711f93017\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f85ba276f1370d20237343255144de\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f85ba276f1370d20237343255144de\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6a709064629cb6841c3f579a88108f5\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6a709064629cb6841c3f579a88108f5\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dadc04ed2230b2d1189f5e1acba49bdf\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dadc04ed2230b2d1189f5e1acba49bdf\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ecfdb2e8fe388fbc3bdb49113026973d\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ecfdb2e8fe388fbc3bdb49113026973d\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9eb20b8d50cf080e1d9f117bbd0cc6e\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9eb20b8d50cf080e1d9f117bbd0cc6e\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c991df00d522f98e9d793f316fd1baf8\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c991df00d522f98e9d793f316fd1baf8\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea606996c8ba6337c1770e421181b43\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea606996c8ba6337c1770e421181b43\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\095ec3180b911c35eacd1feab6e0eef8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\095ec3180b911c35eacd1feab6e0eef8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4255dc82103757f0fee75d7adebc614f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4255dc82103757f0fee75d7adebc614f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79130a54814058327ed4bab63fb1123c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79130a54814058327ed4bab63fb1123c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:30:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:28:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:26:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:29:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:32:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:27:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:24:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:31:9-48
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:25:9-65
activity#com.example.corredores.SplashActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:35:9-43:20
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:37:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:38:13-52
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:36:13-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:39:13-42:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:40:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:40:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:41:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:41:27-74
activity#com.example.corredores.ProfileSelectionActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:46:9-48:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:48:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:47:13-53
activity#com.example.corredores.LoginActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:51:9-53:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:53:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:52:13-42
activity#com.example.corredores.TokenActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:56:9-58:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:58:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:57:13-42
activity#com.example.corredores.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:61:9-63:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:63:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:62:13-41
service#com.example.corredores.services.LocationService
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:66:9-70:56
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:68:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:69:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:70:13-53
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:67:13-53
service#com.example.corredores.services.MyFirebaseMessagingService
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:73:9-80:19
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:75:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:76:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:74:13-64
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:77:13-79:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:78:17-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:78:25-75
receiver#com.example.corredores.receivers.BootReceiver
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:83:9-93:20
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:85:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:86:13-36
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:84:13-51
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.PACKAGE_REPLACED+data:scheme:package
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:87:13-92:29
	android:priority
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:87:28-51
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:88:17-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:88:25-76
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:89:17-84
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:89:25-81
action#android.intent.action.PACKAGE_REPLACED
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:90:17-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:90:25-78
data
ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:91:17-50
	android:scheme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml:91:23-47
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2da938e0e51246f58401e1e70a8344\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2da938e0e51246f58401e1e70a8344\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\93edbca5700ad9cb25d78eb02bade720\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\93edbca5700ad9cb25d78eb02bade720\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\967147dcae910194fa274d2c250149cc\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\967147dcae910194fa274d2c250149cc\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3638d6c7c3d7b87d5f2eabc07b8b0e57\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3638d6c7c3d7b87d5f2eabc07b8b0e57\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbb63f8e3ee649ebb59cd4bb90450e99\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbb63f8e3ee649ebb59cd4bb90450e99\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79364b3e190ffce9f2dea0a1da6759a7\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79364b3e190ffce9f2dea0a1da6759a7\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee22392c0511e6aba80092990f3de4a\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee22392c0511e6aba80092990f3de4a\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b42534c1ac90d64a4f8f7e11550c6cfd\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b42534c1ac90d64a4f8f7e11550c6cfd\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3f8bb670ae22105397c8335fe93c8bd\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3f8bb670ae22105397c8335fe93c8bd\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb3e29ff08c07d3daf20d3f651cc8eee\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb3e29ff08c07d3daf20d3f651cc8eee\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [androidx.webkit:webkit:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6f715e2c0250f74384a9309ebf14495\transformed\webkit-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6f715e2c0250f74384a9309ebf14495\transformed\webkit-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35d799fceb32dbef952e474ebfc145ee\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35d799fceb32dbef952e474ebfc145ee\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e0f0a33a4832529aa72c6af15bff48c\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e0f0a33a4832529aa72c6af15bff48c\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba55085bc3cc0f7fd03bf666ca981f5a\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba55085bc3cc0f7fd03bf666ca981f5a\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c73a9538e3525628e2bf6c7b18e4ece\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c73a9538e3525628e2bf6c7b18e4ece\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bee524264b6054cb37707b6a7fac0cf0\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bee524264b6054cb37707b6a7fac0cf0\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db346f513417c3ce5b708b5c5a9189f3\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db346f513417c3ce5b708b5c5a9189f3\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a319b3c553e8ff493eba9a02f3e67d8d\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a319b3c553e8ff493eba9a02f3e67d8d\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bd61a142146d3441faa191edd4584bb\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bd61a142146d3441faa191edd4584bb\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bb51bc14d126169e2ad1a1340f8326b\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bb51bc14d126169e2ad1a1340f8326b\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0a34a75fcc25ba26072520862f515e0\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0a34a75fcc25ba26072520862f515e0\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998bd82a33703232c8d116bebe281ded\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998bd82a33703232c8d116bebe281ded\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6aa9534116cd506754db97828c361ce2\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6aa9534116cd506754db97828c361ce2\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eaaf152c12373f2f89641935564da2a8\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eaaf152c12373f2f89641935564da2a8\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5737cdfcf2118fdd50ca4f4332956ac\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5737cdfcf2118fdd50ca4f4332956ac\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1822edcf5d2328903154cc41cade1db2\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1822edcf5d2328903154cc41cade1db2\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20d46420d97e351d7735ec8379a46b9f\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20d46420d97e351d7735ec8379a46b9f\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df67eaaf0b48836253fb504b6c9a6346\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df67eaaf0b48836253fb504b6c9a6346\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\060432f54d80cc8e659316157dc70d71\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\060432f54d80cc8e659316157dc70d71\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0aa488a77f7e697087baeb6ea75979bf\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0aa488a77f7e697087baeb6ea75979bf\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19590ff9a3e0873f1d96063fe131eb36\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19590ff9a3e0873f1d96063fe131eb36\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101cca20c1e85df7a5f9f46711f93017\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101cca20c1e85df7a5f9f46711f93017\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf26778305f000d2d1525106f6580765\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf26778305f000d2d1525106f6580765\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\acfe9f8b774d2c354443b3078d546139\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\acfe9f8b774d2c354443b3078d546139\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\636079ac1538b7c7227947a9db213969\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\636079ac1538b7c7227947a9db213969\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe4373c4482e7e798dd3da2e28e74cfc\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe4373c4482e7e798dd3da2e28e74cfc\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3e428614568415b5436ae0870877c8\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3e428614568415b5436ae0870877c8\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5dbcc4bae08e2d37d02b09ba5c09d41\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5dbcc4bae08e2d37d02b09ba5c09d41\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcb3250c1492934d3ece20c483063ed7\transformed\room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcb3250c1492934d3ece20c483063ed7\transformed\room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d67d231637ce3b2a99ecff45dd34195d\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d67d231637ce3b2a99ecff45dd34195d\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f85ba276f1370d20237343255144de\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f85ba276f1370d20237343255144de\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6a709064629cb6841c3f579a88108f5\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6a709064629cb6841c3f579a88108f5\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\108e8ff76d198e53371c5c0cc08de0fc\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\108e8ff76d198e53371c5c0cc08de0fc\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d33dc9df7056a5cc3da3a2f633312e4\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d33dc9df7056a5cc3da3a2f633312e4\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dadc04ed2230b2d1189f5e1acba49bdf\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dadc04ed2230b2d1189f5e1acba49bdf\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ecfdb2e8fe388fbc3bdb49113026973d\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ecfdb2e8fe388fbc3bdb49113026973d\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9eb20b8d50cf080e1d9f117bbd0cc6e\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9eb20b8d50cf080e1d9f117bbd0cc6e\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df2b181811aa594d3aa507b5c71cec2c\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df2b181811aa594d3aa507b5c71cec2c\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c991df00d522f98e9d793f316fd1baf8\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c991df00d522f98e9d793f316fd1baf8\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea606996c8ba6337c1770e421181b43\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea606996c8ba6337c1770e421181b43\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fd2b2904cc709cf74ecc96ad3a8f907\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fd2b2904cc709cf74ecc96ad3a8f907\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a86e451e335fe170cc22e2bbb8f32c2\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a86e451e335fe170cc22e2bbb8f32c2\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e354fd7c956ed6175d4f117257206ed7\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e354fd7c956ed6175d4f117257206ed7\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07d177d4395d04d87ca62bf88051dbbc\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07d177d4395d04d87ca62bf88051dbbc\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e16c9886a5aca2cccfc433075ae7f19d\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e16c9886a5aca2cccfc433075ae7f19d\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ecd105970e5114bb9554f865bbee88c\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ecd105970e5114bb9554f865bbee88c\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0773a4b518b1ace8239e66fc72f6925\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0773a4b518b1ace8239e66fc72f6925\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\095ec3180b911c35eacd1feab6e0eef8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\095ec3180b911c35eacd1feab6e0eef8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d73aef2def23a6c1775a61c1e9f05979\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d73aef2def23a6c1775a61c1e9f05979\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\609a67b0ce1ff57138f6d6eb42472158\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\609a67b0ce1ff57138f6d6eb42472158\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4255dc82103757f0fee75d7adebc614f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4255dc82103757f0fee75d7adebc614f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9951833a5599c2e5d44cc8e9213addc\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9951833a5599c2e5d44cc8e9213addc\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bda673c447e21be587edbfdcc61f30bc\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bda673c447e21be587edbfdcc61f30bc\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79130a54814058327ed4bab63fb1123c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79130a54814058327ed4bab63fb1123c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd1d6560a6a3e77e92af3163a068a7a5\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd1d6560a6a3e77e92af3163a068a7a5\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b81aab4cb0d3fabdf7da10392cc10b5\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b81aab4cb0d3fabdf7da10392cc10b5\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a36122275415b171d4a041932ae5346\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a36122275415b171d4a041932ae5346\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\629bd9b8d35e1fae891a3284a78b6369\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\629bd9b8d35e1fae891a3284a78b6369\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c20d3e50f2d03b00a44b9f34a59ec44\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c20d3e50f2d03b00a44b9f34a59ec44\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b741871d3d36ce368591da2830fa6c16\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b741871d3d36ce368591da2830fa6c16\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c808468632754ca24252791f884893f\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c808468632754ca24252791f884893f\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\corredores\app\src\main\AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:23:9-29:19
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b42534c1ac90d64a4f8f7e11550c6cfd\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b42534c1ac90d64a4f8f7e11550c6cfd\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb3e29ff08c07d3daf20d3f651cc8eee\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb3e29ff08c07d3daf20d3f651cc8eee\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79130a54814058327ed4bab63fb1123c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79130a54814058327ed4bab63fb1123c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:25:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:24:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar
ADDED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:26:13-28:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f76e6d94aebbd66ac9c0ae91fadae7d\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:27:17-129
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:23:22-74
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\108e8ff76d198e53371c5c0cc08de0fc\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\108e8ff76d198e53371c5c0cc08de0fc\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45476f9a77a5f555f50648131bed2726\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:61:17-119
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee22392c0511e6aba80092990f3de4a\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee22392c0511e6aba80092990f3de4a\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee22392c0511e6aba80092990f3de4a\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee22392c0511e6aba80092990f3de4a\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar
ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b42534c1ac90d64a4f8f7e11550c6cfd\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b42534c1ac90d64a4f8f7e11550c6cfd\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b42534c1ac90d64a4f8f7e11550c6cfd\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0a34a75fcc25ba26072520862f515e0\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0a34a75fcc25ba26072520862f515e0\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9eb20b8d50cf080e1d9f117bbd0cc6e\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9eb20b8d50cf080e1d9f117bbd0cc6e\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df2b181811aa594d3aa507b5c71cec2c\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df2b181811aa594d3aa507b5c71cec2c\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df2b181811aa594d3aa507b5c71cec2c\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df2b181811aa594d3aa507b5c71cec2c\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df2b181811aa594d3aa507b5c71cec2c\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df2b181811aa594d3aa507b5c71cec2c\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffb2c066020dcb8519a3ed48b43ddfcf\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\959f409f3dda69925eaccc58cb3ee1b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb3e29ff08c07d3daf20d3f651cc8eee\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb3e29ff08c07d3daf20d3f651cc8eee\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb3e29ff08c07d3daf20d3f651cc8eee\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9310199844d1d1ef88b70c794078a021\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101cca20c1e85df7a5f9f46711f93017\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101cca20c1e85df7a5f9f46711f93017\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\095ec3180b911c35eacd1feab6e0eef8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\095ec3180b911c35eacd1feab6e0eef8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753887b7094b4841d5bae77ee9b271a6\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe34445a8a29aa67ba4eb23600a3e5d\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0a34a75fcc25ba26072520862f515e0\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0a34a75fcc25ba26072520862f515e0\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b595c55564bcbb5ce4b448afffca0e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.corredores.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.corredores.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38844c610817e56ea2d6d5568752a816\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101cca20c1e85df7a5f9f46711f93017\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101cca20c1e85df7a5f9f46711f93017\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101cca20c1e85df7a5f9f46711f93017\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f85ba276f1370d20237343255144de\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f85ba276f1370d20237343255144de\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f85ba276f1370d20237343255144de\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea606996c8ba6337c1770e421181b43\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea606996c8ba6337c1770e421181b43\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ea606996c8ba6337c1770e421181b43\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccf5c5226670a29ec4c35724152a24b9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d7ba3216fdc0e14cd140392109b1d7c\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79130a54814058327ed4bab63fb1123c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79130a54814058327ed4bab63fb1123c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79130a54814058327ed4bab63fb1123c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6525759ea110d94663cd4ba02e9cbb0\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc7ae8745555d448c5bafd0170b8d76f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
