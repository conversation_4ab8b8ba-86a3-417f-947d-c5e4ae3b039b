/* app/static/css/style.css */

/* -----------------------------------------
   1. Fuente (opcional): Montserrat vía Google Fonts
-------------------------------------------- */
@import url('https://fonts.googleapis.com/css?family=Montserrat:400,700&display=swap');

/* -----------------------------------------
   2. Reset básico y configuración general
-------------------------------------------- */
html, body {
    margin: 0;
    padding: 0;
    height: 100%; /* Para eventualmente centrar vertical o manejar 100% de la ventana */
    font-family: 'Montserrat', sans-serif;
    background-color: #f2f2f2;
}

/* -----------------------------------------
   3. <PERSON>cabezado y Logo
-------------------------------------------- */
header {
    background-color: #004d40; /* Ajusta el color a tu gusto */
    padding: 10px 0;
}

/* 
   .header-container:
   - Establecemos un contenedor tipo "flex" en columna
   - Centra el contenido horizontalmente con align-items
   - margin: 0 auto para que no se pegue a la izquierda
*/
.header-container {
    display: flex;
    flex-direction: column;
    align-items: center; 
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo-container {
    flex-shrink: 0;
    margin-bottom: 10px;
}

.logo {
    width: 500px; 
    height: auto;
}

/* -----------------------------------------
   4. Navegación
-------------------------------------------- */
nav ul {
    list-style: none;
    display: flex;
    justify-content: center; /* Centra el menú horizontal */
    margin: 0;
    padding: 0;
}

nav ul li {
    margin: 0 15px;
}

nav ul li a {
    color: #fff;
    text-decoration: none;
    font-weight: 600;
    padding: 10px;
    transition: background-color 0.3s;
}

nav ul li a:hover {
    background-color: #00695c; 
    border-radius: 4px; 
}

/* -----------------------------------------
   5. Contenido principal (main)
   - Centrado horizontal con margin: 0 auto
   - text-align: center para centrar el texto y elementos inline
-------------------------------------------- */
main {
    width: 90%;       /* Ocupa 90% del ancho de la pantalla en dispositivos grandes */
    max-width: 600px; /* Ancho máximo */
    margin: 20px auto;
    padding: 0 20px;
    text-align: center;
    /* Si quieres un fondo blanco:
       background-color: #fff;
       border-radius: 8px; (opcional)
    */
}

/* -----------------------------------------
   6. Footer
-------------------------------------------- */
footer {
    background-color: #004d40;
    color: #fff;
    text-align: center;
    padding: 15px 0;
}

/* -----------------------------------------
   7. Formularios
   - Se centran con margin: 0 auto
   - Se uniforman campos y botones
-------------------------------------------- */
form {
    background-color: #fff;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 5px;
    max-width: 400px;  /* Ancho máximo del formulario */
    margin: 0 auto;    /* Centrado horizontal */
    text-align: left;  /* Etiquetas e inputs alineados a la izquierda */
}

form div {
    margin-bottom: 15px;
}

form label {
    display: inline-block;
    margin-bottom: 5px;
    font-weight: 600;
}

form input[type="text"],
form input[type="email"],
form input[type="password"],
form select,
form textarea {
    width: 100%;
    padding: 8px;
    box-sizing: border-box; 
    border: 1px solid #ccc;
    border-radius: 4px;
}

/* Botones de formulario */
form input[type="submit"],
form button[type="submit"] {
    background-color: #00695c;
    color: #fff;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-radius: 4px;
    font-weight: 600;
    transition: background-color 0.3s;
}

form input[type="submit"]:hover,
form button[type="submit"]:hover {
    background-color: #004d40;
}

/* -----------------------------------------
   8. Tablas
-------------------------------------------- */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background-color: #fff;
    margin: 0 auto; /* Si quieres que se vea centrada dentro del main (aunque width=100% ocupa todo) */
}

table th,
table td {
    border: 1px solid #ccc;
    padding: 10px;
    text-align: left;
}

table th {
    background-color: #00695c;
    color: #fff;
}

table tr:nth-child(even) {
    background-color: #f9f9f9;
}

/* -----------------------------------------
   9. Mensajes de Error (validaciones)
-------------------------------------------- */
span[style*="color: red"] {
    color: #d32f2f !important;
    font-weight: bold;
}

/* -----------------------------------------
   10. Ajustes Responsivos
-------------------------------------------- */
@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        align-items: center;
    }

    nav ul {
        flex-direction: column;
        margin-top: 10px;
        align-items: center;
    }

    nav ul li {
        margin: 5px 0;
    }

    main {
        width: 95%;
        margin: 20px auto;
        padding: 0 10px;
    }

    form {
        max-width: 100%; 
    }
}

/* -----------------------------------------
   11. Estilos para enlaces tipo botón
-------------------------------------------- */
.btn {
    display: inline-block;  /* Para que se comporte como un botón (bloque en línea) */
    padding: 10px 20px;     /* Espaciado interno */
    margin: 5px;            /* Separación entre botones */
    background-color: #00695c; /* Color principal */
    color: #fff;            /* Texto en blanco */
    border: none;           /* Sin borde */
    border-radius: 4px;     /* Bordes redondeados */
    text-decoration: none;  /* Quitar subrayado si es un <a> */
    font-weight: 600;       /* Negrita */
    transition: background-color 0.3s;
    cursor: pointer;        /* Manito al pasar */
}

/* Hover genérico para .btn */
.btn:hover {
    background-color: #004d40; /* Color oscuro al pasar el mouse */
    text-decoration: none;     /* Asegura que no salga subrayado */
}

/* Botón primario (puede quedar igual que .btn si no quieres cambios) */
.btn-primary {
    background-color: #00695c;
}
.btn-primary:hover {
    background-color: #004d40;
}

/* Botón secundario, con un color algo diferente 
   (puede ser un tono distinto, por ejemplo #008975, 
    y al hover #005f50, para mostrar variación) 
*/
.btn-secondary {
    background-color: #008975; 
}
.btn-secondary:hover {
    background-color: #005f50;
}