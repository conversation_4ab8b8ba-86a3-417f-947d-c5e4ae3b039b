package com.example.corredoresseguroshijo.network

import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

// --- Cliente Retrofit Centralizado ---
object RetrofitClient {

    // URL base del servidor Flask
    private const val BASE_URL = "https://patagoniaservers.com.ar:5004/"

    // Interceptor para logging (útil para debugging)
    private val loggingInterceptor = HttpLoggingInterceptor().apply {
        level = HttpLoggingInterceptor.Level.BODY // Mostrar cuerpo completo de requests/responses
    }

    // Cliente HTTP con configuraciones personalizadas
    private val okHttpClient = OkHttpClient.Builder()
        .addInterceptor(loggingInterceptor) // Agregar logging
        .connectTimeout(30, TimeUnit.SECONDS) // Timeout de conexión
        .readTimeout(30, TimeUnit.SECONDS)    // Timeout de lectura
        .writeTimeout(30, TimeUnit.SECONDS)   // Timeout de escritura
        .build()

    // Instancia de Retrofit configurada
    private val retrofit = Retrofit.Builder()
        .baseUrl(BASE_URL)
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create()) // Convertidor JSON con Gson
        .build()

    // Instancia pública de ApiService
    val instance: ApiService = retrofit.create(ApiService::class.java)
}
