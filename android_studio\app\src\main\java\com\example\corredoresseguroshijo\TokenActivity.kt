package com.example.corredores

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.EditText
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.corredores.network.ApiService
import com.example.corredores.network.RetrofitClient
import com.example.corredores.utils.PreferenceManager
import kotlinx.coroutines.launch
import retrofit2.Response

/**
 * Actividad de vinculación para hijos
 * Vincula el dispositivo usando un token de 16 caracteres
 */
class TokenActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "TokenActivity"
        private const val TOKEN_LENGTH = 16
    }

    private lateinit var editTextToken: EditText
    private lateinit var buttonLink: Button
    private lateinit var progressBar: ProgressBar
    private lateinit var textViewError: TextView
    
    private lateinit var preferenceManager: PreferenceManager
    private lateinit var apiService: ApiService

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_token)

        preferenceManager = PreferenceManager(this)
        apiService = RetrofitClient.getApiService()

        // Verificar si ya está vinculado
        if (preferenceManager.getChildId() != -1L) {
            redirectToMainActivity()
            return
        }

        initializeViews()
        setupLinkButton()
    }

    private fun initializeViews() {
        editTextToken = findViewById(R.id.edit_text_token)
        buttonLink = findViewById(R.id.button_link)
        progressBar = findViewById(R.id.progress_bar)
        textViewError = findViewById(R.id.text_view_error)
    }

    private fun setupLinkButton() {
        buttonLink.setOnClickListener {
            val token = editTextToken.text.toString().trim()

            if (validateToken(token)) {
                performLinking(token)
            }
        }
    }

    private fun validateToken(token: String): Boolean {
        if (token.isEmpty()) {
            showError("Por favor ingrese el token de vinculación")
            return false
        }

        if (token.length != TOKEN_LENGTH) {
            showError("El token debe tener exactamente $TOKEN_LENGTH caracteres")
            return false
        }

        return true
    }

    private fun performLinking(token: String) {
        showLoading(true)
        showError("")

        lifecycleScope.launch {
            try {
                val linkRequest = mapOf(
                    "token" to token
                )

                val response: Response<Map<String, Any>> = apiService.linkDevice(linkRequest)

                if (response.isSuccessful) {
                    val responseBody = response.body()
                    if (responseBody != null && responseBody["status"] == "success") {
                        handleLinkingSuccess(responseBody)
                    } else {
                        val message = responseBody?.get("message") as? String ?: "Error de vinculación"
                        showError(message)
                    }
                } else {
                    when (response.code()) {
                        400 -> showError("Token inválido o expirado")
                        404 -> showError("Token no encontrado")
                        409 -> showError("Este dispositivo ya está vinculado")
                        else -> showError("Error de conexión: ${response.code()}")
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error en vinculación", e)
                showError("Error de conexión. Verifique su internet.")
            } finally {
                showLoading(false)
            }
        }
    }

    private fun handleLinkingSuccess(responseBody: Map<String, Any>) {
        try {
            val childId = (responseBody["child_id"] as? Double)?.toLong()
            val childName = responseBody["child_name"] as? String

            if (childId != null) {
                // Guardar datos de vinculación
                preferenceManager.setChildId(childId)
                preferenceManager.setLinked(true)

                Log.d(TAG, "Vinculación exitosa para hijo: $childId")
                
                Toast.makeText(this, "Dispositivo vinculado exitosamente", Toast.LENGTH_SHORT).show()
                
                redirectToMainActivity()
            } else {
                showError("Respuesta del servidor incompleta")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error procesando respuesta de vinculación", e)
            showError("Error procesando respuesta del servidor")
        }
    }

    private fun redirectToMainActivity() {
        val intent = Intent(this, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    private fun showLoading(show: Boolean) {
        progressBar.visibility = if (show) android.view.View.VISIBLE else android.view.View.GONE
        buttonLink.isEnabled = !show
        editTextToken.isEnabled = !show
    }

    private fun showError(message: String) {
        textViewError.text = message
        textViewError.visibility = if (message.isEmpty()) android.view.View.GONE else android.view.View.VISIBLE
    }

    override fun onBackPressed() {
        // Permitir volver a selección de perfil solo si no está vinculado
        if (preferenceManager.getChildId() == -1L) {
            super.onBackPressed()
        }
    }
}
