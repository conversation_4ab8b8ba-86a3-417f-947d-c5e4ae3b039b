"""
Servicio para enviar notificaciones push usando Firebase Cloud Messaging (FCM)
"""

import requests
import json
import time
from datetime import datetime
from flask import current_app
from app.models import FCMToken, User
from typing import List, Optional


class FCMService:
    """
    Servicio para enviar notificaciones push a dispositivos móviles usando FCM
    """
    
    def __init__(self):
        # TODO: Configurar Firebase Server Key en variables de entorno
        # Por ahora usamos un placeholder
        self.server_key = "YOUR_FIREBASE_SERVER_KEY"  # Reemplazar con la clave real
        self.fcm_url = "https://fcm.googleapis.com/fcm/send"
    
    def send_notification_to_father(self, father_id: int, title: str, body: str, 
                                   notification_type: str = "zone_notification", 
                                   child_name: str = "") -> bool:
        """
        Envía una notificación push a todos los dispositivos de un padre
        
        Args:
            father_id: ID del padre
            title: Tí<PERSON>lo de la notificación
            body: Cuerpo de la notificación
            notification_type: Tipo de notificación (zone_notification, etc.)
            child_name: Nombre del hijo (opcional)
            
        Returns:
            bool: True si se envió exitosamente, False en caso contrario
        """
        try:
            # Obtener tokens FCM activos del padre
            tokens = self._get_active_tokens(father_id)
            
            if not tokens:
                current_app.logger.warning(f"No se encontraron tokens FCM activos para padre {father_id}")
                return False
            
            # Preparar payload de la notificación
            notification_data = {
                "title": title,
                "body": body,
                "icon": "ic_notification",
                "sound": "default",
                "click_action": "FLUTTER_NOTIFICATION_CLICK"
            }
            
            data_payload = {
                "type": notification_type,
                "child_name": child_name,
                "timestamp": str(int(time.time()))
            }
            
            success_count = 0
            
            # Enviar a cada token
            for token in tokens:
                if self._send_to_token(token.token, notification_data, data_payload):
                    success_count += 1
                    # Actualizar último uso del token
                    token.last_used = datetime.utcnow()
                else:
                    # Marcar token como inactivo si falla
                    token.is_active = False
            
            # Guardar cambios en la base de datos
            from app import db
            db.session.commit()
            
            current_app.logger.info(f"Notificación FCM enviada a {success_count}/{len(tokens)} dispositivos del padre {father_id}")
            
            return success_count > 0
            
        except Exception as e:
            current_app.logger.error(f"Error enviando notificación FCM a padre {father_id}: {e}")
            return False
    
    def send_zone_notification(self, father_id: int, child_name: str, 
                              zone_name: str, notification_type: str) -> bool:
        """
        Envía notificaciones específicas de zonas
        
        Args:
            father_id: ID del padre
            child_name: Nombre del hijo
            zone_name: Nombre de la zona
            notification_type: Tipo de notificación (zone_early_warning, zone_late_arrival, zone_missed)
            
        Returns:
            bool: True si se envió exitosamente
        """
        # Configurar mensajes según el tipo
        messages = {
            "zone_early_warning": {
                "title": f"⚠️ Advertencia Temprana",
                "body": f"{child_name} debería estar llegando a {zone_name} pronto"
            },
            "zone_late_arrival": {
                "title": f"⏰ Llegada Tardía",
                "body": f"{child_name} llegó tarde a {zone_name}"
            },
            "zone_missed": {
                "title": f"🚨 Zona No Visitada",
                "body": f"{child_name} no llegó a {zone_name} en el tiempo esperado"
            }
        }
        
        message_config = messages.get(notification_type, {
            "title": "📍 Notificación de Zona",
            "body": f"Nueva notificación sobre {child_name}"
        })
        
        return self.send_notification_to_father(
            father_id=father_id,
            title=message_config["title"],
            body=message_config["body"],
            notification_type=notification_type,
            child_name=child_name
        )
    
    def _get_active_tokens(self, user_id: int) -> List[FCMToken]:
        """
        Obtiene todos los tokens FCM activos de un usuario
        """
        return FCMToken.query.filter_by(
            user_id=user_id,
            is_active=True
        ).all()
    
    def _send_to_token(self, token: str, notification: dict, data: dict) -> bool:
        """
        Envía una notificación a un token específico
        
        Args:
            token: Token FCM del dispositivo
            notification: Datos de la notificación
            data: Datos adicionales
            
        Returns:
            bool: True si se envió exitosamente
        """
        try:
            headers = {
                'Authorization': f'key={self.server_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'to': token,
                'notification': notification,
                'data': data,
                'priority': 'high',
                'content_available': True
            }
            
            response = requests.post(
                self.fcm_url,
                headers=headers,
                data=json.dumps(payload),
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success', 0) > 0:
                    current_app.logger.debug(f"Notificación FCM enviada exitosamente a token: {token[:20]}...")
                    return True
                else:
                    current_app.logger.warning(f"FCM falló para token {token[:20]}...: {result}")
                    return False
            else:
                current_app.logger.error(f"Error HTTP {response.status_code} enviando FCM: {response.text}")
                return False
                
        except Exception as e:
            current_app.logger.error(f"Excepción enviando FCM a token {token[:20]}...: {e}")
            return False


# Instancia global del servicio
fcm_service = FCMService()


# Funciones de conveniencia
def send_zone_notification_to_father(father_id: int, child_name: str, 
                                    zone_name: str, notification_type: str) -> bool:
    """
    Función de conveniencia para enviar notificaciones de zona
    """
    return fcm_service.send_zone_notification(father_id, child_name, zone_name, notification_type)


def send_custom_notification_to_father(father_id: int, title: str, body: str, 
                                     notification_type: str = "custom") -> bool:
    """
    Función de conveniencia para enviar notificaciones personalizadas
    """
    return fcm_service.send_notification_to_father(father_id, title, body, notification_type)
