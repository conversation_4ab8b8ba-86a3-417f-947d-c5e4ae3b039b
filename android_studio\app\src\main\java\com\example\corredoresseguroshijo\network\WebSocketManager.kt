package com.example.corredoresseguroshijo.network

import android.util.Log
import io.socket.client.IO
import io.socket.client.Socket
import io.socket.emitter.Emitter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.net.URISyntaxException

class WebSocketManager private constructor() {
    
    companion object {
        private const val TAG = "WebSocketManager"
        private const val SERVER_URL = "https://patagoniaservers.com.ar:5004"
        
        @Volatile
        private var INSTANCE: WebSocketManager? = null
        
        fun getInstance(): WebSocketManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: WebSocketManager().also { INSTANCE = it }
            }
        }
    }
    
    private var socket: Socket? = null
    private var childId: Int = -1
    private var isConnected = false
    private val scope = CoroutineScope(Dispatchers.IO)
    
    // Callback para cuando se recibe solicitud de ubicación inmediata
    var onLocationRequested: (() -> Unit)? = null
    
    fun initialize(childId: Int) {
        this.childId = childId
        Log.d(TAG, "Initializing WebSocket for child $childId")
        
        try {
            val options = IO.Options().apply {
                secure = true
                forceNew = true
                reconnection = true
                reconnectionAttempts = 5
                reconnectionDelay = 1000
                timeout = 10000
            }
            
            socket = IO.socket(SERVER_URL, options)
            setupEventListeners()
            
        } catch (e: URISyntaxException) {
            Log.e(TAG, "Error creating socket: ${e.message}")
        }
    }
    
    private fun setupEventListeners() {
        socket?.apply {
            
            // Evento de conexión
            on(Socket.EVENT_CONNECT) {
                Log.d(TAG, "✅ WebSocket connected")
                isConnected = true
                registerChild()
            }
            
            // Evento de desconexión
            on(Socket.EVENT_DISCONNECT) {
                Log.d(TAG, "❌ WebSocket disconnected")
                isConnected = false
            }
            
            // Evento de error de conexión
            on(Socket.EVENT_CONNECT_ERROR) { args ->
                Log.e(TAG, "❌ WebSocket connection error: ${args.contentToString()}")
                isConnected = false
            }
            
            // Confirmación de registro
            on("registration_confirmed") { args ->
                if (args.isNotEmpty()) {
                    val data = args[0] as JSONObject
                    Log.d(TAG, "✅ Registration confirmed: ${data.optString("message")}")
                }
            }
            
            // Solicitud de ubicación inmediata del servidor
            on("request_immediate_location") { args ->
                if (args.isNotEmpty()) {
                    val data = args[0] as JSONObject
                    val urgent = data.optBoolean("urgent", false)
                    val message = data.optString("message", "")
                    
                    Log.d(TAG, "🚨 IMMEDIATE LOCATION REQUEST via WebSocket: $message")
                    
                    if (urgent) {
                        // Ejecutar callback en el hilo principal
                        scope.launch(Dispatchers.Main) {
                            onLocationRequested?.invoke()
                        }
                    }
                }
            }
            
            // Confirmación de ubicación recibida
            on("location_received") { args ->
                if (args.isNotEmpty()) {
                    val data = args[0] as JSONObject
                    Log.d(TAG, "✅ Location received confirmation: ${data.optString("message")}")
                }
            }
            
            // Manejo de errores
            on("error") { args ->
                if (args.isNotEmpty()) {
                    val data = args[0] as JSONObject
                    Log.e(TAG, "❌ WebSocket error: ${data.optString("message")}")
                }
            }
        }
    }
    
    fun connect() {
        if (socket?.connected() != true) {
            Log.d(TAG, "Connecting to WebSocket...")
            socket?.connect()
        } else {
            Log.d(TAG, "WebSocket already connected")
        }
    }
    
    fun disconnect() {
        Log.d(TAG, "Disconnecting WebSocket...")
        socket?.disconnect()
        isConnected = false
    }
    
    private fun registerChild() {
        if (childId != -1) {
            val data = JSONObject().apply {
                put("child_id", childId)
            }
            
            Log.d(TAG, "Registering child $childId with WebSocket server")
            socket?.emit("child_register", data)
        } else {
            Log.e(TAG, "Cannot register: childId not set")
        }
    }
    
    fun sendLocationResponse(latitude: Double, longitude: Double, accuracy: Float) {
        if (isConnected && childId != -1) {
            val data = JSONObject().apply {
                put("child_id", childId)
                put("latitude", latitude)
                put("longitude", longitude)
                put("accuracy", accuracy)
            }
            
            Log.d(TAG, "📍 Sending location response via WebSocket: $latitude, $longitude")
            socket?.emit("location_response", data)
        } else {
            Log.w(TAG, "Cannot send location: WebSocket not connected or childId not set")
        }
    }
    
    fun isConnected(): Boolean {
        return isConnected && socket?.connected() == true
    }
    
    fun destroy() {
        Log.d(TAG, "Destroying WebSocket connection")
        socket?.disconnect()
        socket?.off()
        socket = null
        isConnected = false
        INSTANCE = null
    }
}
