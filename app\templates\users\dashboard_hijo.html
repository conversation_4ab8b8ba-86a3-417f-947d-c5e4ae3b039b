<!-- app/templates/users/dashboard_hijo.html -->
{% extends 'base.html' %}

{% block content %}
  <h1>Dashboard del Hijo</h1>
  <p>Bienvenido, {{ g.child.first_name }} {{ g.child.last_name }}!</p>  {# Use g.child #}
  <div id="gps-status">GPS Logging: Inactive</div>  {# GPS Logging Status Display #}
    {# ---  Nuevos Botones de Notificación para el Hijo --- #}
    <div class="hijo-notifications">
        <h3>Enviar Notificación a Padre</h3>
        <button onclick="sendNotification('estoy_bien')" class="btn btn-success btn-sm">Estoy Bien</button>
        <button onclick="sendNotification('ayuda')" class="btn btn-warning btn-sm">Ayuda</button>
        <button onclick="sendNotification('demorado')" class="btn btn-info btn-sm">Demorado</button>
        <button onclick="sendNotification('otro')" class="btn btn-secondary btn-sm">Otro</button>
        <div id="notification-status"></div>  {# Div para mensajes de estado de la notificación #}
    </div>
{% endblock %}

{% block scripts %}
  {{ super() }} {# Import base scripts #}
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      if ("geolocation" in navigator) {
        var gpsStatusDiv = document.getElementById('gps-status');
        var watchId;
        let loggingInterval;

        function logLocation(position) {
          var locationData = {
            
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            timestamp: new Date().getFullYear() + '-' +  // Año (YYYY)
                      ('0' + (new Date().getMonth() + 1)).slice(-2) + '-' + // Mes (MM, con padding cero)
                      ('0' + new Date().getDate()).slice(-2) + ' ' +      // Día (DD, con padding cero)
                      ('0' + new Date().getHours()).slice(-2) + ':' +     // Hora (HH, con padding cero)
                      ('0' + new Date().getMinutes()).slice(-2) + ':' +   // Minutos (MM, con padding cero)
                      ('0' + new Date().getSeconds()).slice(-2)          // Segundos (SS, con padding cero)
          };

          fetch('/log_location', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify(locationData)
          })
          .then(response => {
            if (response.ok) {
              gpsStatusDiv.textContent = 'GPS Logging: Active - Location logged successfully!';
              console.log('Ubicación registrada exitosamente:', locationData);
            } else {
              gpsStatusDiv.textContent = 'GPS Logging: Active - Error logging location.';
              console.error('Error al registrar ubicación:', response.statusText);
            }
          })
          .catch(error => {
            gpsStatusDiv.textContent = 'GPS Logging: Active - Error sending location data.';
            console.error('Error al enviar datos de ubicación:', error);
          });
        }

        function handleLocationError(error) {
            console.log("handleLocationError() function called!"); // DEBUG - A. Check if handleLocationError is called
          gpsStatusDiv.textContent = 'GPS Logging: Inactive - Location access denied or unavailable.';
          console.error('Geolocation error:', error.message);
        }

        function startLogging() {
            console.log("startLogging() function called!");
            gpsStatusDiv.textContent = 'GPS Logging: Activating...';
            console.log("Calling navigator.geolocation.watchPosition()");
            loggingInterval = setInterval(function() {
                navigator.geolocation.getCurrentPosition(logLocation, handleLocationError, {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 5000
                });
            }, 5000);  // <-- CAMBIAR INTERVALO A 5000 (5 segundos)
            gpsStatusDiv.textContent = 'GPS Logging: Active.';
            console.log('GPS Logging activado con intervalo de 5 segundos.'); // Mensaje actualizado
        }
        function stopLogging() {
            console.log("stopLogging() function called!"); // DEBUG - E. Check if stopLogging is called
            if (loggingInterval) {
                clearInterval(loggingInterval);
                loggingInterval = null;
                gpsStatusDiv.textContent = 'GPS Logging: Inactive.';
                console.log('F. GPS Logging detenido.'); // DEBUG - F. Check GPS Logging stopped
            }
        }

        console.log("Before calling startLogging() - OUTSIDE startLogging function!"); // DEBUG - Check BEFORE startLogging() is called - OUTSIDE FUNCTION
        startLogging(); // Iniciar GPS Logging al cargar el dashboard
        console.log("After calling startLogging()");


        // Botón de stop (opcional - pruebas/control)
        const stopGpsButton = document.createElement('button');
        stopGpsButton.textContent = 'Detener GPS Logging';
        stopGpsButton.className = 'btn btn-danger btn-sm';
        stopGpsButton.addEventListener('click', stopLogging);


        //Insertar el boton de stop en el dashboard (ajusta el selector si es necesario)
         let hijoActionsDiv = document.querySelector('.hijo-actions');
        if (!hijoActionsDiv) {
            hijoActionsDiv = document.createElement('div');
            hijoActionsDiv.className = 'hijo-actions';
            document.querySelector('.block.content').appendChild(hijoActionsDiv); // Asegúrate de que este selector es correcto para tu dashboard hijo
        }
        hijoActionsDiv.appendChild(stopGpsButton);

      } else {
          console.log("Geolocation NOT supported in browser"); // DEBUG - Check geolocation support
        gpsStatusDiv.textContent = 'GPS Logging: No soportado en este navegador.';
        console.error("Geolocalización no soportada en este navegador.");
      }
    });
    // --- Función para enviar notificaciones del hijo al padre ---
    function sendNotification(notificationType) {
      var notificationStatusDiv = document.getElementById('notification-status'); // Div para mostrar estado
      notificationStatusDiv.textContent = 'Enviando notificación...'; // Mensaje inicial

      fetch('/send_notification', {  // Realiza la petición POST a la ruta correspondiente
          method: 'POST',
          headers: {
              'Content-Type': 'application/json',
              'X-CSRFToken': '{{ csrf_token() }}'  // Incluye el token CSRF
          },
          body: JSON.stringify({ notification_type: notificationType })
      })
      .then(response => {
          // Verificar el estado de la respuesta antes de parsear a JSON
          if (!response.ok) {
              return response.json().then(data => {
                  // Se lanza un error con el mensaje obtenido del servidor o un mensaje por defecto
                  throw new Error(data.message || 'Error al enviar la notificación');
              });
          }
          return response.json();
      })
      .then(data => {
          // Si la respuesta fue exitosa, se actualiza el estado y se muestra el mensaje
          notificationStatusDiv.textContent = 'Notificación enviada exitosamente.';
          console.log('Notificación enviada:', data.message);
      })
      .catch(error => {
          // Se maneja el error actualizando el estado y mostrando el mensaje de error
          notificationStatusDiv.textContent = 'Error al enviar la notificación.';
          console.error('Error al enviar notificación:', error);
      });
    }
  </script>
{% endblock %}