package com.example.corredoresseguroshijo.utils

import android.content.Context
import android.content.SharedPreferences

/**
 * Manager para manejar SharedPreferences de la aplicación
 * Incluye soporte para padres e hijos, y tokens FCM
 */
class PreferenceManager(context: Context) {

    companion object {
        private const val PREF_NAME = "CorredoresHijoPrefs"
        private const val KEY_CHILD_ID = "child_id"
        private const val KEY_IS_LINKED = "is_linked"
        private const val KEY_LAST_LOCATION_LAT = "last_location_lat"
        private const val KEY_LAST_LOCATION_LNG = "last_location_lng"
        private const val KEY_LAST_LOCATION_TIME = "last_location_time"
        private const val KEY_FCM_TOKEN_REGISTERED = "fcm_token_registered"
        private const val KEY_USER_TYPE = "user_type"
        private const val KEY_IS_LOGGED_IN = "is_logged_in"
        private const val KEY_AUTH_TOKEN = "auth_token"
        private const val KEY_USER_ID = "user_id"
        private const val KEY_USER_FOLIO = "user_folio"
        private const val KEY_USER_NAME = "user_name"
        
        const val USER_TYPE_PARENT = "parent"
        const val USER_TYPE_CHILD = "child"
    }

    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)

    // === MÉTODOS PARA HIJOS (EXISTENTES) ===
    
    fun setChildId(childId: Long) {
        sharedPreferences.edit().putLong(KEY_CHILD_ID, childId).apply()
    }

    fun getChildId(): Long {
        return sharedPreferences.getLong(KEY_CHILD_ID, -1L)
    }

    fun setLinked(isLinked: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_IS_LINKED, isLinked).apply()
    }

    fun isLinked(): Boolean {
        return sharedPreferences.getBoolean(KEY_IS_LINKED, false)
    }

    fun setLastLocation(latitude: Double, longitude: Double, timestamp: Long) {
        sharedPreferences.edit()
            .putFloat(KEY_LAST_LOCATION_LAT, latitude.toFloat())
            .putFloat(KEY_LAST_LOCATION_LNG, longitude.toFloat())
            .putLong(KEY_LAST_LOCATION_TIME, timestamp)
            .apply()
    }

    fun getLastLocationLatitude(): Double {
        return sharedPreferences.getFloat(KEY_LAST_LOCATION_LAT, 0f).toDouble()
    }

    fun getLastLocationLongitude(): Double {
        return sharedPreferences.getFloat(KEY_LAST_LOCATION_LNG, 0f).toDouble()
    }

    fun getLastLocationTime(): Long {
        return sharedPreferences.getLong(KEY_LAST_LOCATION_TIME, 0L)
    }

    // === MÉTODOS PARA PADRES (NUEVOS) ===
    
    fun setUserType(userType: String) {
        sharedPreferences.edit().putString(KEY_USER_TYPE, userType).apply()
    }

    fun getUserType(): String? {
        return sharedPreferences.getString(KEY_USER_TYPE, null)
    }

    fun setLoggedIn(isLoggedIn: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_IS_LOGGED_IN, isLoggedIn).apply()
    }

    fun isLoggedIn(): Boolean {
        return sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false)
    }

    fun setAuthToken(token: String) {
        sharedPreferences.edit().putString(KEY_AUTH_TOKEN, token).apply()
    }

    fun getAuthToken(): String? {
        return sharedPreferences.getString(KEY_AUTH_TOKEN, null)
    }

    fun setUserData(userId: Int, folio: String, name: String?) {
        sharedPreferences.edit()
            .putInt(KEY_USER_ID, userId)
            .putString(KEY_USER_FOLIO, folio)
            .putString(KEY_USER_NAME, name)
            .apply()
    }

    fun getUserId(): Int {
        return sharedPreferences.getInt(KEY_USER_ID, -1)
    }

    fun getUserFolio(): String? {
        return sharedPreferences.getString(KEY_USER_FOLIO, null)
    }

    fun getUserName(): String? {
        return sharedPreferences.getString(KEY_USER_NAME, null)
    }

    // === MÉTODOS PARA FCM (NUEVOS) ===
    
    fun setFCMTokenRegistered(registered: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_FCM_TOKEN_REGISTERED, registered).apply()
    }

    fun isFCMTokenRegistered(): Boolean {
        return sharedPreferences.getBoolean(KEY_FCM_TOKEN_REGISTERED, false)
    }

    // === MÉTODOS DE UTILIDAD ===
    
    fun hasSelectedProfile(): Boolean {
        return !getUserType().isNullOrEmpty()
    }

    fun clearAllData() {
        sharedPreferences.edit().clear().apply()
    }

    fun clearParentData() {
        sharedPreferences.edit()
            .remove(KEY_USER_TYPE)
            .remove(KEY_IS_LOGGED_IN)
            .remove(KEY_AUTH_TOKEN)
            .remove(KEY_USER_ID)
            .remove(KEY_USER_FOLIO)
            .remove(KEY_USER_NAME)
            .remove(KEY_FCM_TOKEN_REGISTERED)
            .apply()
    }

    fun clearChildData() {
        sharedPreferences.edit()
            .remove(KEY_CHILD_ID)
            .remove(KEY_IS_LINKED)
            .remove(KEY_LAST_LOCATION_LAT)
            .remove(KEY_LAST_LOCATION_LNG)
            .remove(KEY_LAST_LOCATION_TIME)
            .apply()
    }
}
