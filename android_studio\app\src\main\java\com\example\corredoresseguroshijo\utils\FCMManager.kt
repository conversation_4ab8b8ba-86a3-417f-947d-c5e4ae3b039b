package com.example.corredoresseguroshijo.utils

import android.content.Context
import android.util.Log
import com.google.firebase.messaging.FirebaseMessaging
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.io.IOException

/**
 * Manager para manejar Firebase Cloud Messaging
 * Registra tokens FCM y los envía al servidor
 */
class FCMManager(private val context: Context) {

    companion object {
        private const val TAG = "FCMManager"
        private const val SERVER_URL = "https://patagoniaservers.com.ar:5004"
    }

    private val preferenceManager = PreferenceManager(context)
    private val client = OkHttpClient()

    /**
     * Inicializa FCM para un padre logueado
     * Obtiene el token y lo registra en el servidor
     */
    fun initializeForParent() {
        // Solo proceder si es un padre logueado
        if (preferenceManager.getUserType() != PreferenceManager.USER_TYPE_PARENT || 
            !preferenceManager.isLoggedIn()) {
            Log.d(TAG, "Usuario no es padre logueado, saltando inicialización FCM")
            return
        }

        Log.d(TAG, "Inicializando FCM para padre...")

        // Obtener token FCM
        FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
            if (!task.isSuccessful) {
                Log.w(TAG, "Error obteniendo token FCM", task.exception)
                return@addOnCompleteListener
            }

            // Obtener nuevo token
            val token = task.result
            Log.d(TAG, "Token FCM obtenido: $token")

            // Registrar token en el servidor
            registerTokenWithServer(token)
        }
    }

    /**
     * Registra el token FCM en el servidor
     */
    private fun registerTokenWithServer(token: String) {
        val userId = preferenceManager.getUserId()
        val authToken = preferenceManager.getAuthToken()

        if (userId == -1 || authToken.isNullOrEmpty()) {
            Log.e(TAG, "No se puede registrar token - datos de usuario incompletos")
            return
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val json = JSONObject().apply {
                    put("fcm_token", token)
                    put("device_type", "android")
                    put("user_id", userId)
                }

                val requestBody = json.toString().toRequestBody("application/json".toMediaType())

                val request = Request.Builder()
                    .url("$SERVER_URL/api/register_fcm_token")
                    .post(requestBody)
                    .addHeader("Authorization", "Bearer $authToken")
                    .addHeader("Content-Type", "application/json")
                    .build()

                Log.d(TAG, "Enviando token FCM al servidor...")

                client.newCall(request).enqueue(object : Callback {
                    override fun onFailure(call: Call, e: IOException) {
                        Log.e(TAG, "Error enviando token FCM al servidor", e)
                    }

                    override fun onResponse(call: Call, response: Response) {
                        response.use {
                            if (response.isSuccessful) {
                                Log.d(TAG, "Token FCM registrado exitosamente en el servidor")
                                
                                // Guardar que el token fue registrado
                                preferenceManager.setFCMTokenRegistered(true)
                            } else {
                                Log.e(TAG, "Error del servidor al registrar token FCM: ${response.code}")
                            }
                        }
                    }
                })

            } catch (e: Exception) {
                Log.e(TAG, "Error preparando request para token FCM", e)
            }
        }
    }

    /**
     * Desregistra el token FCM del servidor (al hacer logout)
     */
    fun unregisterToken() {
        val authToken = preferenceManager.getAuthToken()
        
        if (authToken.isNullOrEmpty()) {
            Log.d(TAG, "No hay token de auth, saltando desregistro FCM")
            return
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val request = Request.Builder()
                    .url("$SERVER_URL/api/unregister_fcm_token")
                    .delete()
                    .addHeader("Authorization", "Bearer $authToken")
                    .build()

                Log.d(TAG, "Desregistrando token FCM del servidor...")

                client.newCall(request).enqueue(object : Callback {
                    override fun onFailure(call: Call, e: IOException) {
                        Log.e(TAG, "Error desregistrando token FCM", e)
                    }

                    override fun onResponse(call: Call, response: Response) {
                        response.use {
                            if (response.isSuccessful) {
                                Log.d(TAG, "Token FCM desregistrado exitosamente")
                            } else {
                                Log.e(TAG, "Error del servidor al desregistrar token FCM: ${response.code}")
                            }
                        }
                    }
                })

            } catch (e: Exception) {
                Log.e(TAG, "Error desregistrando token FCM", e)
            }
        }

        // Limpiar flag local
        preferenceManager.setFCMTokenRegistered(false)
    }

    /**
     * Verifica si las notificaciones están habilitadas
     */
    fun areNotificationsEnabled(): Boolean {
        // TODO: Verificar permisos de notificación del sistema
        return true
    }

    /**
     * Solicita permisos de notificación (Android 13+)
     */
    fun requestNotificationPermission() {
        // TODO: Implementar solicitud de permisos para Android 13+
        Log.d(TAG, "Solicitando permisos de notificación...")
    }
}
