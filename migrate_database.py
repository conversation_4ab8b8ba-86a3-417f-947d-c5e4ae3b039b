#!/usr/bin/env python3
"""
Script para migrar la base de datos y agregar las tablas/columnas
necesarias para el sistema de notificaciones de zonas.
"""

import os
import sys
from dotenv import load_dotenv

# Cargar variables de entorno
load_dotenv()

# Agregar el directorio raíz al path para importar la app
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def run_migration():
    """Ejecutar la migración de base de datos"""
    try:
        # Importar la app y db
        from app import create_app, db
        
        app = create_app()
        
        with app.app_context():
            print("🔧 Iniciando migración de base de datos...")
            
            # Ejecutar las migraciones SQL
            migration_queries = [
                # 1. Agregar columnas a child_zone_assignments
                """
                ALTER TABLE child_zone_assignments 
                ADD COLUMN early_warning_minutes INT DEFAULT 5,
                ADD COLUMN notifications_enabled BOOLEAN DEFAULT TRUE
                """,
                
                # 2. <PERSON>rear tabla zone_compliance_logs
                """
                CREATE TABLE zone_compliance_logs (
                    log_id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                    assignment_id BIGINT UNSIGNED NOT NULL,
                    child_id BIGINT UNSIGNED NOT NULL,
                    zone_id BIGINT UNSIGNED NOT NULL,
                    compliance_date DATE NOT NULL,
                    expected_time TIME NOT NULL,
                    actual_arrival_time TIME NULL,
                    status VARCHAR(50) DEFAULT 'pending',
                    minutes_late INT DEFAULT 0,
                    early_warning_sent BOOLEAN DEFAULT FALSE,
                    late_notification_sent BOOLEAN DEFAULT FALSE,
                    missed_notification_sent BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (assignment_id) REFERENCES child_zone_assignments(assignment_id) ON DELETE CASCADE,
                    FOREIGN KEY (child_id) REFERENCES children(child_id) ON DELETE CASCADE,
                    FOREIGN KEY (zone_id) REFERENCES safe_zones(zone_id) ON DELETE CASCADE,
                    
                    INDEX idx_compliance_date (compliance_date),
                    INDEX idx_child_zone_date (child_id, zone_id, compliance_date),
                    INDEX idx_status (status),
                    UNIQUE KEY unique_assignment_date (assignment_id, compliance_date)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """
            ]
            
            for i, query in enumerate(migration_queries, 1):
                try:
                    print(f"📝 Ejecutando migración {i}/{len(migration_queries)}...")
                    db.engine.execute(query)
                    print(f"✅ Migración {i} completada")
                except Exception as e:
                    if "Duplicate column name" in str(e) or "already exists" in str(e):
                        print(f"⚠️  Migración {i} ya aplicada: {e}")
                    else:
                        print(f"❌ Error en migración {i}: {e}")
                        return False
            
            print("🎉 ¡Migración completada exitosamente!")
            print("\n📊 Verificando estructura de tablas...")
            
            # Verificar que las tablas existen
            try:
                result = db.engine.execute("SHOW COLUMNS FROM child_zone_assignments")
                columns = [row[0] for row in result]
                print(f"✅ child_zone_assignments: {len(columns)} columnas")
                if 'early_warning_minutes' in columns:
                    print("   ✅ early_warning_minutes agregada")
                if 'notifications_enabled' in columns:
                    print("   ✅ notifications_enabled agregada")
                
                result = db.engine.execute("SHOW COLUMNS FROM zone_compliance_logs")
                columns = [row[0] for row in result]
                print(f"✅ zone_compliance_logs: {len(columns)} columnas")
                
            except Exception as e:
                print(f"⚠️  Error verificando tablas: {e}")
            
            return True
            
    except Exception as e:
        print(f"💥 Error crítico en migración: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Iniciando script de migración...")
    success = run_migration()
    
    if success:
        print("\n✅ Migración completada. El servidor puede reiniciarse.")
        sys.exit(0)
    else:
        print("\n❌ Migración falló. Revisar errores arriba.")
        sys.exit(1)
