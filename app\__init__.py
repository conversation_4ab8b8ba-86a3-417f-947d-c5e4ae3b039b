# app/__init__.py
import json # Importar json para el filtro dump
from flask import Flask, jsonify, request
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import LoginManager
from flask_socketio import Socket<PERSON>
from config import Config
from flask_wtf.csrf import CSRFProtect

# --- Inicialización de Extensiones ---
db = SQLAlchemy()
migrate = Migrate()
login_manager = LoginManager()
# Especifica la vista de login para Flask-Login (endpoint dentro del blueprint 'auth')
login_manager.login_view = 'auth.login'
# Mensaje flash que se mostrará cuando se requiera login
login_manager.login_message = 'Por favor, inicia sesión para acceder a esta página.'
login_manager.login_message_category = 'info' # Categoría para el mensaje flash

socketio = SocketIO()
csrf = CSRFProtect() # Inicializar CSRFProtect globalmente

# --- Factory de la Aplicación ---
def create_app(config_class=Config):
    """
    Factory para crear y configurar la instancia de la aplicación Flask.
    """
    app = Flask(__name__)
    app.config.from_object(config_class) # Cargar configuración desde el objeto Config

    # --- Inicializar Extensiones con la App ---
    db.init_app(app)
    migrate.init_app(app, db) # Asociar Flask-Migrate
    login_manager.init_app(app) # Asociar Flask-Login
    # Inicializar SocketIO con modo asíncrono threading (compatible sin eventlet)
    socketio.init_app(app, async_mode='threading', cors_allowed_origins="*") # Permitir orígenes para SocketIO si es necesario
    csrf.init_app(app) # Asociar CSRFProtect a la app

    # --- Importar Blueprints ---
    # Es importante importar los blueprints *después* de inicializar las extensiones
    # si los blueprints dependen de ellas (como db o login_manager).
    from app.routes.auth import auth_bp
    from app.routes.users import users_bp
    # ----- MODIFICADO: Importar el nuevo blueprint de zonas -----
    from app.routes.zones_management import zones_bp # Tu cambio para zonas está bien
    # -----------------------------------------------------------
    from app.routes.notifications import notifications_bp
    from app.routes.incidents import incidents_bp
    from app.routes.zone_monitoring import zone_monitoring_bp
    # 'main' viene de app/routes/routes.py (el que redirige a login)
    from app.routes.routes import main as main_redirect_bp
    # Importar el nuevo blueprint para la API
    from app.routes.api import api_bp
    # Importar blueprint de admin si lo estás usando (parece que no está registrado en tu versión)
    # from app.routes.admin import admin_bp # Si no lo usas, está bien comentado

    # --- Registrar Blueprints ---
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(users_bp, url_prefix='/users')
    # ----- MODIFICADO: Registrar el nuevo blueprint de zonas con prefijo /zones -----
    app.register_blueprint(zones_bp, url_prefix='/zones') # Tu cambio para zonas está bien
    # ----------------------------------------------------------------------------
    app.register_blueprint(notifications_bp, url_prefix='/notifications')
    app.register_blueprint(incidents_bp, url_prefix='/incidents')
    app.register_blueprint(zone_monitoring_bp, url_prefix='/zone_monitoring')
    app.register_blueprint(main_redirect_bp) # Blueprint para la ruta raíz '/'
    app.register_blueprint(api_bp) # Registrar el blueprint de API (ya tiene prefijo '/api')
    # app.register_blueprint(admin_bp) # Registrar si usas admin
    # NOTA: perimeters_bp fue removido porque no está implementado

    # --- Eximir el Blueprint de API de la protección CSRF ---
    csrf.exempt(api_bp)

    # --- Importar eventos WebSocket (temporalmente deshabilitado) ---
    # from app.routes import websocket_events

    # --- Registrar Filtros y Procesadores de Contexto ---
    try:
        from app.utils.filters import decrypt_encrypted_data
        app.jinja_env.filters['decrypt_encrypted_data'] = decrypt_encrypted_data
        app.logger.info("Filtro decrypt_encrypted_data registrado correctamente")
    except ImportError as e:
        app.logger.warning(f"No se pudo importar filtro decrypt_encrypted_data: {e}")
        # Crear filtro dummy para evitar errores
        def dummy_decrypt(data):
            return "[Datos encriptados - filtro no disponible]"
        app.jinja_env.filters['decrypt_encrypted_data'] = dummy_decrypt

    @app.template_filter('dump')
    def dump_filter(obj):
        try:
            return json.dumps(obj, indent=2, default=str)
        except TypeError as e:
            app.logger.warning(f"Filtro 'dump' no pudo serializar objeto: {e}")
            return str(obj)

    # --- Configuración Adicional (si es necesaria) ---

    # Inicializar el scheduler de monitoreo de zonas
    try:
        from app.tasks.zone_scheduler import init_zone_scheduler
        init_zone_scheduler(app)
        app.logger.info("Zone Monitoring Scheduler inicializado correctamente")
    except Exception as e:
        app.logger.warning(f"No se pudo inicializar Zone Monitoring Scheduler: {e}")
        # El servidor puede funcionar sin el scheduler automático

    # Middleware para logging detallado de requests
    @app.before_request
    def log_request_info():
        print("=" * 80)
        print("📥 NUEVA REQUEST")
        print("=" * 80)
        print(f"🌐 URL: {request.url}")
        print(f"📍 Endpoint: {request.endpoint}")
        print(f"🔧 Método: {request.method}")
        print(f"🏠 IP: {request.remote_addr}")
        print(f"🖥️ User Agent: {request.user_agent}")
        print(f"📋 Headers: {dict(request.headers)}")

        # Log información de autenticación
        from flask_login import current_user
        if current_user.is_authenticated:
            print(f"👤 Usuario autenticado: {current_user.username} (ID: {current_user.user_id})")
        else:
            print("🚫 Usuario NO autenticado")

        print("=" * 80)

    @app.after_request
    def log_response_info(response):
        print("=" * 80)
        print("📤 RESPUESTA ENVIADA")
        print("=" * 80)
        print(f"📊 Status Code: {response.status_code}")
        print(f"📋 Headers: {dict(response.headers)}")
        print(f"📏 Content Length: {response.content_length}")
        print("=" * 80)
        return response

    return app