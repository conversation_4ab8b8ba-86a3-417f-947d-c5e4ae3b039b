<!-- app/templates/incidents/visualizar_datos_hijo.html -->
{% extends 'base.html' %}

{% block content %}
<h2>Datos del Hijo (Incidencia ID: {{ incident.incident_id }})</h2>
<p><strong>Hijo:</strong> {{ incident.child.first_name }} {{ incident.child.last_name }}</p>
<p><strong>Tipo Incidencia:</strong> {{ incident.incident_type }}</p>
{% if incident.description %}
<p><strong>Descripción Padre:</strong> {{ incident.description }}</p>
{% endif %}

{# --- MODIFICADO: Usar decrypted_zones_data --- #}
{% if decrypted_zones_data %}
    <h3>Z<PERSON>s <PERSON> Asignadas</h3>
    {# Mostrar lista de nombres y horarios de zonas #}
    <ul>
        {% for zone_data in decrypted_zones_data %}
           <li>
               <strong>{{ zone_data.zone_name }}</strong>
               ({{ zone_data.schedule_start }} - {{ zone_data.schedule_end }})
               {# Mostrar coordenadas para referencia rápida #}
               <small>(Centro: Lat {{ zone_data.latitude | round(5) }}, Lon {{ zone_data.longitude | round(5) }})</small>
            </li>
        {% endfor %}
    </ul>
    {# Contenedor del mapa #}
    <div id="map" style="height: 400px; margin-bottom: 20px; border: 1px solid #ccc;"></div>
{% else %}
    <p>No hay zonas seguras asignadas a esta incidencia o hubo un error al cargarlas.</p>
    {# Contenedor del mapa vacío para mostrar logs igualmente #}
    <div id="map" style="height: 400px; margin-bottom: 20px; border: 1px solid #ccc;"></div>
{% endif %}
{# ------------------------------------------ #}

{# --- Última ubicación (sin cambios) --- #}
{% if last_location %}
    <h3>Última Ubicación Conocida</h3>
    <p>Latitud: {{ last_location.lat }}, Longitud: {{ last_location.lng }}</p>
    {# Podríamos añadir el timestamp del log si lo pasamos #}
{% endif %}
{# --------------------------------- #}

{# --- Logs de ubicación (sin cambios estructurales, pero con mapa) --- #}
<h3>Logs de Ubicación</h3>
{% if logs %}
    {# Podríamos mostrar los logs en una tabla para mejor formato #}
    <table class="table table-sm table-striped table-bordered" style="font-size: 0.9em;">
        <thead>
            <tr>
                <th>Fecha/Hora</th>
                <th>Latitud</th>
                <th>Longitud</th>
                {# Podríamos añadir accuracy si estuviera en location_data #}
                {# <th>Precisión (m)</th> #}
            </tr>
        </thead>
        <tbody>
        {% for log in logs %}
            <tr>
                <td>{{ log.recorded_at }}</td>
                <td>{{ log.location_data.lat | round(6) }}</td> {# Redondear decimales #}
                <td>{{ log.location_data.lng | round(6) }}</td>
                {# <td>{{ log.location_data.accuracy | round(1) if log.location_data.accuracy else 'N/D' }}</td> #}
            </tr>
        {% endfor %}
        </tbody>
    </table>
{% else %}
    <p>No hay logs de ubicación registrados para este hijo.</p>
{% endif %}
{# ---------------------------------------------------------- #}


<a href="{{ url_for('incidents.gestionar_incidentes') }}" class="btn btn-secondary">Volver a Incidencias</a> {# Cambiado a secundario #}
{# Añadir botón para resolver si es operador y está abierta #}
{% if current_user.has_role('OPERADOR') and incident.status == 'open' %}
    <a href="{{ url_for('incidents.resolver_incidente', incident_id=incident.incident_id) }}" class="btn btn-success" style="margin-left: 10px;">Resolver Incidente</a>
{% endif %}

{% endblock %}


{% block scripts %}
{# {{ super() }} #}

<script>
    // Inicializar mapa centrado en Viedma por defecto
    var map = L.map('map').setView([-40.8136, -62.9936], 13);

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 19,
        attribution: 'Map data © <a href="https://openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // LayerGroup para guardar las zonas y logs para ajustar el bounds
    var featuresLayerGroup = L.layerGroup().addTo(map);

    // --- MODIFICADO: Dibujar ZONAS (Círculos) ---
    {% if decrypted_zones_data %}
        console.log("Dibujando Zonas Seguras:", {{ decrypted_zones_data | tojson }});
        {% for zone_data in decrypted_zones_data %}
            try {
                const lat = parseFloat({{ zone_data.latitude }});
                const lon = parseFloat({{ zone_data.longitude }});
                const radius = parseInt({{ zone_data.radius }});
                const zoneName = "{{ zone_data.zone_name | escapejs }}"; // Escapar nombre para JS

                if (!isNaN(lat) && !isNaN(lon) && !isNaN(radius)) {
                    // Dibujar el círculo de la zona segura (ej. en rojo)
                    const zoneCircle = L.circle([lat, lon], {
                        radius: radius,
                        color: 'red',       // Color del borde
                        fillColor: '#f03',   // Color de relleno
                        fillOpacity: 0.3     // Opacidad
                    }).bindPopup(`<b>Zona: ${zoneName}</b><br>Centro: ${lat.toFixed(5)}, ${lon.toFixed(5)}<br>Radio: ${radius}m`);

                    // Añadir al LayerGroup para calcular bounds
                    featuresLayerGroup.addLayer(zoneCircle);
                    console.log(`Zona "${zoneName}" dibujada.`);
                } else {
                     console.warn(`Datos inválidos para dibujar zona ID ${zone_data.zone_id}: lat=${lat}, lon=${lon}, radius=${radius}`);
                }
            } catch (e) {
                 console.error(`Error al procesar datos de zona ${zone_data.zone_id}:`, e);
            }
        {% endfor %}
    {% else %}
        console.log("No hay datos de zonas seguras para dibujar.");
    {% endif %}
    // ------------------------------------------

    // --- Dibujar LOGS de Ubicación (Puntos/Marcadores) ---
    {% if logs %}
        console.log("Dibujando Logs de Ubicación:", {{ logs | length }});
        // Convertir logs a formato GeoJSON para facilitar el manejo con Leaflet
        var logFeatures = [
            {% for log in logs %}
                {% if log.location_data and log.location_data.lat and log.location_data.lng %}
                {
                    "type": "Feature",
                    "geometry": {
                        "type": "Point",
                        // GeoJSON usa [longitude, latitude]
                        "coordinates": [{{ log.location_data.lng }}, {{ log.location_data.lat }}]
                    },
                    "properties": {
                        "time": "{{ log.recorded_at }}",
                        "accuracy": {{ log.location_data.accuracy if log.location_data.accuracy else 'null' }}
                        // Puedes añadir más propiedades si las tienes
                    }
                },
                {% endif %}
            {% endfor %}
        ];

        if (logFeatures.length > 0) {
             const logLayer = L.geoJSON({ "type": "FeatureCollection", "features": logFeatures }, {
                pointToLayer: function (feature, latlng) {
                    // Usar circleMarker para mejor rendimiento con muchos puntos
                    return L.circleMarker(latlng, {
                        radius: 5,          // Radio pequeño
                        fillColor: "green", // Color de relleno verde
                        color: "#000",      // Borde negro fino
                        weight: 1,
                        opacity: 1,
                        fillOpacity: 0.8
                    });
                },
                onEachFeature: function (feature, layer) {
                    // Añadir popup con la hora
                    if (feature.properties && feature.properties.time) {
                        let popupContent = `Hora: ${feature.properties.time}`;
                        if (feature.properties.accuracy) {
                             popupContent += `<br>Precisión: ${feature.properties.accuracy.toFixed(1)}m`;
                        }
                        layer.bindPopup(popupContent);
                    }
                }
            });
             // Añadir al LayerGroup
             featuresLayerGroup.addLayer(logLayer);
             console.log(`${logFeatures.length} logs de ubicación dibujados.`);
        } else {
            console.log("No hay logs de ubicación válidos para dibujar.");
        }
    {% else %}
         console.log("No hay logs de ubicación para dibujar.");
    {% endif %}
    // ----------------------------------------------------

    // --- Marcador para la ÚLTIMA ubicación conocida (si existe) ---
    {% if last_location and last_location.lat and last_location.lng %}
        try {
            const lastLat = parseFloat({{ last_location.lat }});
            const lastLon = parseFloat({{ last_location.lng }});
            if (!isNaN(lastLat) && !isNaN(lastLon)) {
                // Usar un marcador normal (ej. azul) para destacarlo
                const lastMarker = L.marker([lastLat, lastLon], {
                     // Podrías usar un icono diferente si quieres
                     // icon: L.icon({ iconUrl: '/path/to/last_location_icon.png', ... })
                }).bindPopup('<b>Última ubicación conocida</b>');

                 // Añadir al LayerGroup
                featuresLayerGroup.addLayer(lastMarker);
                console.log("Marcador de última ubicación añadido.");
             } else {
                  console.warn("Coordenadas inválidas para la última ubicación:", lastLat, lastLon);
             }
        } catch (e) {
             console.error("Error al procesar última ubicación:", e);
        }
    {% endif %}
    // ---------------------------------------------------------

    // --- Ajustar la vista del mapa para que se vean todas las features ---
    // (Solo si se añadió algo al LayerGroup)
    if (featuresLayerGroup.getLayers().length > 0) {
         try {
            map.fitBounds(featuresLayerGroup.getBounds().pad(0.1)); // pad(0.1) añade un pequeño margen
            console.log("Ajustando vista del mapa a todas las features.");
         } catch(e) {
              console.error("Error al ajustar los bounds del mapa:", e);
              // Podría fallar si solo hay un punto y ningún círculo, etc.
              // Como fallback, mantener la vista inicial o centrar en la última ubicación si existe.
               {% if last_location and last_location.lat and last_location.lng %}
                   map.setView([{{ last_location.lat }}, {{ last_location.lng }}], 15); // Zoom razonable
               {% endif %}
         }
    } else {
        // Si no hay nada que mostrar, mantener la vista inicial de Viedma
        console.log("No hay features (zonas/logs) para ajustar la vista del mapa.");
    }
    // ----------------------------------------------------------------

</script>
{% endblock %}