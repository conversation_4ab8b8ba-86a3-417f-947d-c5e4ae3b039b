<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <!-- El atributo 'package' NO debe estar aquí -->

    <!-- ================== Permisos ================== -->
    <!-- Acceso a Internet (para API) -->
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- Estado de la Red (opcional, para verificar conexión) -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- Ubicación Precisa (recomendado para rastreo) -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <!-- Ubicación Aproximada (se pide junto o como fallback si FINE es denegado) -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- Servicio en Primer Plano (General - API 28+) -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <!-- Servicio en Primer Plano de tipo Ubicación (Específico - API 29+) -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />

    <!-- Notificaciones (necesario para Foreground Service - API 33+) -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <!-- Ubicación en Segundo Plano (NECESARIO para seguimiento continuo) -->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

    <!-- Permisos adicionales para estabilidad -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <!-- Bluetooth (para evitar advertencias) -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <!-- ================== Aplicación ================== -->
    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Corredores"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:targetApi="34">
        <!-- android:name=".MyApplication": Registra la clase Application personalizada -->

        <!-- Actividad de inicio (Splash) -->
        <activity
            android:name=".SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.Corredores">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Selección de perfil -->
        <activity
            android:name=".ProfileSelectionActivity"
            android:exported="false" />

        <!-- Login para padres -->
        <activity
            android:name=".LoginActivity"
            android:exported="false" />

        <!-- Vinculación para hijos -->
        <activity
            android:name=".TokenActivity"
            android:exported="false" />

        <!-- Actividad principal dual -->
        <activity
            android:name=".MainActivity"
            android:exported="false" />

        <!-- Servicio de ubicación -->
        <service
            android:name=".services.LocationService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="location" />

        <!-- Servicio de Firebase Cloud Messaging para notificaciones push -->
        <service
            android:name=".services.MyFirebaseMessagingService"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- Receptor para reinicio del sistema -->
        <receiver
            android:name=".receivers.BootReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>

    </application>

</manifest>