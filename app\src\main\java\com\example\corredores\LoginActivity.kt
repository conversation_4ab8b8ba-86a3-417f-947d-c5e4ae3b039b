package com.example.corredores

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.corredores.models.LoginRequest
import com.example.corredores.network.NetworkClient
import com.example.corredores.utils.PreferenceManager
import kotlinx.coroutines.launch

class LoginActivity : AppCompatActivity() {

    private lateinit var preferenceManager: PreferenceManager
    private lateinit var etFolio: EditText
    private lateinit var etPassword: EditText
    private lateinit var btnLogin: Button
    private lateinit var progressBar: ProgressBar

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_login)

        preferenceManager = PreferenceManager(this)

        initViews()
        setupClickListeners()
    }

    private fun initViews() {
        etFolio = findViewById(R.id.et_folio)
        etPassword = findViewById(R.id.et_password)
        btnLogin = findViewById(R.id.btn_login)
        progressBar = findViewById(R.id.progress_bar)
    }

    private fun setupClickListeners() {
        btnLogin.setOnClickListener {
            performLogin()
        }
    }

    private fun performLogin() {
        val folio = etFolio.text.toString().trim()
        val password = etPassword.text.toString().trim()

        if (folio.isEmpty()) {
            etFolio.error = "Ingresa tu folio"
            return
        }

        if (password.isEmpty()) {
            etPassword.error = "Ingresa tu contraseña"
            return
        }

        showLoading(true)

        lifecycleScope.launch {
            try {
                val response = NetworkClient.apiService.loginPadre(LoginRequest(folio, password))

                if (response.isSuccessful) {
                    val loginResponse = response.body()
                    if (loginResponse != null) {
                        // Guardar datos del usuario
                        preferenceManager.setAuthToken(loginResponse.access_token)
                        preferenceManager.setUserData(
                            loginResponse.user.id,
                            loginResponse.user.folio,
                            loginResponse.user.name
                        )
                        preferenceManager.setLoggedIn(true)

                        // Ir a MainActivity (que mostrará el dashboard del padre)
                        val intent = Intent(this@LoginActivity, MainActivity::class.java)
                        startActivity(intent)
                        finish()
                    } else {
                        showError("Error en la respuesta del servidor")
                    }
                } else {
                    val errorMessage = when (response.code()) {
                        401 -> "Folio o contraseña incorrectos"
                        400 -> "Datos inválidos"
                        else -> "Error del servidor: ${response.code()}"
                    }
                    showError(errorMessage)
                }
            } catch (e: Exception) {
                showError("Error de conexión: ${e.message}")
            } finally {
                showLoading(false)
            }
        }
    }

    private fun showLoading(show: Boolean) {
        progressBar.visibility = if (show) View.VISIBLE else View.GONE
        btnLogin.isEnabled = !show
        etFolio.isEnabled = !show
        etPassword.isEnabled = !show
    }

    private fun showError(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }
}
