package com.example.corredores

import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.example.corredores.utils.PermissionHelper
import com.example.corredores.utils.PreferenceManager

class SplashActivity : AppCompatActivity() {
    
    private lateinit var preferenceManager: PreferenceManager
    private var currentPermissionStep = 0 // 0: location, 1: background, 2: GPS
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_splash)
        
        preferenceManager = PreferenceManager(this)
        
        // Mostrar splash por 2 segundos antes de verificar permisos
        Handler(Looper.getMainLooper()).postDelayed({
            checkPermissions()
        }, 2000)
    }
    
    private fun checkPermissions() {
        when {
            !PermissionHelper.hasLocationPermissions(this) -> {
                currentPermissionStep = 0
                showPermissionDialog(
                    getString(R.string.permission_location_title),
                    getString(R.string.permission_location_message)
                ) {
                    PermissionHelper.requestLocationPermissions(this)
                }
            }
            !PermissionHelper.hasBackgroundLocationPermission(this) -> {
                currentPermissionStep = 1
                showPermissionDialog(
                    getString(R.string.permission_background_title),
                    getString(R.string.permission_background_message)
                ) {
                    PermissionHelper.requestBackgroundLocationPermission(this)
                }
            }
            !PermissionHelper.isGpsEnabled(this) -> {
                currentPermissionStep = 2
                showGpsDialog()
            }
            else -> {
                navigateToNextScreen()
            }
        }
    }
    
    private fun showPermissionDialog(title: String, message: String, onAccept: () -> Unit) {
        AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton(getString(R.string.allow)) { _, _ -> onAccept() }
            .setNegativeButton(getString(R.string.exit)) { _, _ -> finish() }
            .setCancelable(false)
            .show()
    }
    
    private fun showGpsDialog() {
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.gps_disabled_title))
            .setMessage(getString(R.string.gps_disabled_message))
            .setPositiveButton(getString(R.string.settings)) { _, _ -> 
                PermissionHelper.openLocationSettings(this)
            }
            .setNegativeButton(getString(R.string.exit)) { _, _ -> finish() }
            .setCancelable(false)
            .show()
    }
    
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        
        when (requestCode) {
            PermissionHelper.LOCATION_PERMISSION_REQUEST -> {
                if (grantResults.isNotEmpty() && grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                    checkPermissions() // Continúa con el siguiente permiso
                } else {
                    showPermissionDeniedDialog()
                }
            }
            PermissionHelper.BACKGROUND_LOCATION_REQUEST -> {
                // El permiso de segundo plano no es crítico, continúa
                if (grantResults.isEmpty() || grantResults[0] != PackageManager.PERMISSION_GRANTED) {
                    Toast.makeText(this, "Permiso de segundo plano no concedido", Toast.LENGTH_SHORT).show()
                }
                checkPermissions()
            }
        }
    }
    
    private fun showPermissionDeniedDialog() {
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.error_permissions))
            .setMessage("Los permisos de ubicación son necesarios para el funcionamiento de la aplicación.")
            .setPositiveButton(getString(R.string.settings)) { _, _ -> 
                PermissionHelper.openAppSettings(this)
            }
            .setNegativeButton(getString(R.string.exit)) { _, _ -> finish() }
            .setCancelable(false)
            .show()
    }
    
    override fun onResume() {
        super.onResume()
        // Verificar GPS cuando regrese de configuración
        if (currentPermissionStep == 2 && PermissionHelper.isGpsEnabled(this)) {
            navigateToNextScreen()
        }
    }
    
    private fun navigateToNextScreen() {
        val intent = when {
            !preferenceManager.hasSelectedProfile() -> {
                Intent(this, ProfileSelectionActivity::class.java)
            }
            !preferenceManager.isLoggedIn() -> {
                when (preferenceManager.getUserType()) {
                    PreferenceManager.USER_TYPE_PARENT -> Intent(this, MainActivity::class.java)
                    PreferenceManager.USER_TYPE_CHILD -> Intent(this, TokenActivity::class.java)
                    else -> Intent(this, ProfileSelectionActivity::class.java)
                }
            }
            else -> {
                Intent(this, MainActivity::class.java)
            }
        }
        
        startActivity(intent)
        finish()
    }
}
