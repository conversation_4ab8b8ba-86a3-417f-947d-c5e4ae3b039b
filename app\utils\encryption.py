# app/utils/encryption.py
from cryptography.fernet import Fernet
import os

class Encryption:
    def __init__(self, key):
        self.fernet = Fernet(key)

    @staticmethod
    def generate_master_key():
        return Fernet.generate_key()

    def encrypt(self, data):
        return self.fernet.encrypt(data.encode()).decode()

    def decrypt(self, token):
        return self.fernet.decrypt(token.encode()).decode()