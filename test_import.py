#!/usr/bin/env python3
import sys
import traceback

print("Testing imports...")

try:
    print("1. Testing basic imports...")
    import os
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Basic imports OK")
    
    print("2. Testing app creation...")
    from app import create_app
    print("✅ App import OK")
    
    print("3. Creating app...")
    app = create_app()
    print("✅ App creation OK")
    
    print("4. Testing models import...")
    from app.models import Role, User, Child
    print("✅ Basic models import OK")
    
    print("5. Testing with app context...")
    with app.app_context():
        print("✅ App context OK")
        
    print("🎉 All tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    print("Traceback:")
    traceback.print_exc()
    sys.exit(1)
