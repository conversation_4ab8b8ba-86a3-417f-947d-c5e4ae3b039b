#!/usr/bin/env python3
"""
Script para listar usuarios en la base de datos
"""

import os
import sys
from dotenv import load_dotenv

# Cargar variables de entorno
load_dotenv()

# Agregar el directorio del proyecto al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def list_users():
    """Lista todos los usuarios en la base de datos"""
    try:
        # Importar después de configurar el path
        from app import create_app, db
        from app.models import User, Role
        
        # Crear la aplicación
        app = create_app()
        
        with app.app_context():
            print("=" * 80)
            print("👥 LISTADO DE USUARIOS EN LA BASE DE DATOS")
            print("=" * 80)
            
            # Obtener todos los usuarios
            users = User.query.all()
            
            if not users:
                print("❌ No se encontraron usuarios en la base de datos")
                return
            
            print(f"📊 Total de usuarios: {len(users)}")
            print()
            
            for i, user in enumerate(users, 1):
                print(f"👤 Usuario {i}:")
                print(f"   🆔 ID: {user.user_id}")
                print(f"   📧 Username: {user.username}")
                print(f"   📧 Email: {user.email}")
                print(f"   👤 Nombre: {user.first_name} {user.last_name}")
                print(f"   ✅ Activo: {user.active}")
                print(f"   📞 Teléfono: {user.phone}")
                
                # Obtener roles
                roles = [role.role_name for role in user.roles]
                print(f"   🎭 Roles: {roles}")
                
                # Verificar si tiene password hash
                print(f"   🔑 Tiene password: {bool(user.password_hash)}")
                
                print("-" * 40)
            
            # Listar roles disponibles
            print("\n🎭 ROLES DISPONIBLES:")
            roles = Role.query.all()
            for role in roles:
                print(f"   - {role.role_name}: {role.description}")
                
    except Exception as e:
        print(f"❌ Error al listar usuarios: {e}")
        import traceback
        print(f"❌ Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    list_users()
