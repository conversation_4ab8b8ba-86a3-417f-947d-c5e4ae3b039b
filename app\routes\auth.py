# app/routes/auth.py
# app/routes/auth.py
from flask import Blueprint, render_template, redirect, url_for, flash, request
from app import db
from app.models import User, Role, UserRoles
from flask_login import login_user, logout_user, login_required, current_user
from app.forms.auth_forms import RegistrationForm, LoginForm
from app.utils.encryption import Encryption
import os

auth_bp = Blueprint('auth', __name__, template_folder='templates/auth')

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    form = RegistrationForm()
    if form.validate_on_submit():
        # Obtener datos del formulario
        username = form.username.data
        email = form.email.data
        password = form.password.data
        first_name = form.first_name.data
        last_name = form.last_name.data
        phone = form.phone.data

        # Verificar si el usuario ya existe
        if User.query.filter((User.username == username) | (User.email == email)).first():
            flash('Usuario o correo ya existe.', 'danger')
            return redirect(url_for('auth.register'))

        # Crear nuevo usuario
        new_user = User(
            username=username,
            email=email,
            first_name=first_name,
            last_name=last_name,
            phone=phone
        )
        new_user.set_password(password)

        # Asignar rol (por defecto, 'PADRE' o según lógica)
        padre_role = Role.query.filter_by(role_name='PADRE').first()
        if not padre_role:
            flash('Rol PADRE no definido en la base de datos.', 'danger')
            return redirect(url_for('auth.register'))

        new_user.roles.append(padre_role)

        # Inicializar encriptación si es PADRE
        if 'PADRE' in [role.role_name for role in new_user.roles]:
            encryption = Encryption(os.getenv('FERNET_KEY'))
            # Generar una clave maestra para el padre
            master_key = Encryption.generate_master_key()
            # Cifrar la clave maestra con la contraseña del padre
            # Nota: En un entorno real, deberías usar una derivación de clave segura basada en la contraseña
            encrypted_master_key = encryption.encrypt(master_key.decode())
            new_user.father_encrypted_key = encrypted_master_key

        # Agregar a la base de datos
        db.session.add(new_user)
        db.session.commit()

        flash('Registro exitoso. Por favor, inicia sesión.', 'success')
        return redirect(url_for('auth.login'))

    return render_template('auth/register.html', form=form)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    print("=" * 60)
    print("🔐 LOGIN ATTEMPT")
    print("=" * 60)

    form = LoginForm()

    if request.method == 'POST':
        print(f"📝 Datos del formulario recibidos:")
        print(f"   📋 Form data: {dict(request.form)}")
        print(f"   ✅ Form válido: {form.validate_on_submit()}")

        if form.errors:
            print(f"   ❌ Errores de validación: {form.errors}")

    if form.validate_on_submit():
        # Obtener datos del formulario
        username = form.username.data
        password = form.password.data

        print(f"👤 Intentando login:")
        print(f"   📧 Username: {username}")
        print(f"   🔑 Password length: {len(password) if password else 0}")

        # Buscar usuario
        user = User.query.filter_by(username=username).first()
        print(f"🔍 Búsqueda de usuario:")
        print(f"   👤 Usuario encontrado: {user is not None}")

        if user:
            print(f"   📧 Email: {user.email}")
            print(f"   🆔 User ID: {user.user_id}")
            print(f"   ✅ Activo: {user.active}")
            print(f"   🎭 Roles: {[role.role_name for role in user.roles]}")

            # Verificar contraseña
            password_valid = user.check_password(password)
            print(f"   🔑 Password válido: {password_valid}")

            if password_valid:
                if not user.active:
                    print("   ❌ Usuario inactivo")
                    flash('Usuario inactivo. Contacta al administrador.', 'danger')
                    return redirect(url_for('auth.login'))

                print("   ✅ Login exitoso - redirigiendo al dashboard")
                login_user(user, remember=form.remember.data)
                flash('Inicio de sesión exitoso.', 'success')
                return redirect(url_for('users.dashboard'))
            else:
                print("   ❌ Contraseña incorrecta")
                flash('Credenciales inválidas.', 'danger')
                return redirect(url_for('auth.login'))
        else:
            print("   ❌ Usuario no encontrado")
            flash('Credenciales inválidas.', 'danger')
            return redirect(url_for('auth.login'))
    else:
        if request.method == 'POST':
            print("   ❌ Formulario no válido")

    print("📄 Renderizando template de login")
    print("=" * 60)
    return render_template('auth/login.html', form=form)

@auth_bp.route('/logout')
@login_required
def logout():
    logout_user()
    flash('Has cerrado sesión.', 'success')
    return redirect(url_for('auth.login'))
