# app/tasks/zone_scheduler.py

import threading
import time
from datetime import datetime, timedelta
from flask import current_app


class ZoneMonitoringScheduler:
    """
    Scheduler para ejecutar el monitoreo de zonas automáticamente
    """

    def __init__(self, app=None):
        self.app = app
        self.monitoring_service = None
        self.running = False
        self.thread = None
        self.check_interval = 60  # Verificar cada 60 segundos

    def init_app(self, app):
        """Inicializar con la aplicación Flask"""
        self.app = app

    def start(self):
        """Iniciar el scheduler en un hilo separado"""
        if self.running:
            return

        self.running = True
        self.thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.thread.start()

        if self.app:
            with self.app.app_context():
                current_app.logger.info("Zone Monitoring Scheduler iniciado")

    def stop(self):
        """Detener el scheduler"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)

        if self.app:
            with self.app.app_context():
                current_app.logger.info("Zone Monitoring Scheduler detenido")

    def _run_scheduler(self):
        """Bucle principal del scheduler"""
        while self.running:
            try:
                if self.app:
                    with self.app.app_context():
                        self._execute_monitoring_cycle()

            except Exception as e:
                if self.app:
                    with self.app.app_context():
                        current_app.logger.error(f"Error en scheduler de monitoreo: {e}")

            # Esperar antes del siguiente ciclo
            time.sleep(self.check_interval)

    def _execute_monitoring_cycle(self):
        """Ejecutar un ciclo de monitoreo"""
        try:
            if not self.monitoring_service:
                # Import aquí para evitar import circular
                from app.services.zone_monitoring import ZoneMonitoringService
                self.monitoring_service = ZoneMonitoringService()

            # Solo ejecutar durante horas activas (6 AM - 11 PM)
            current_hour = datetime.now().hour
            if current_hour < 6 or current_hour > 23:
                return

            stats = self.monitoring_service.run_monitoring_cycle()

            # Log solo si hay actividad
            if any(stats.values()):
                current_app.logger.info(f"Monitoreo automático ejecutado: {stats}")

        except Exception as e:
            current_app.logger.error(f"Error ejecutando ciclo de monitoreo automático: {e}")


# Instancia global del scheduler
zone_scheduler = ZoneMonitoringScheduler()


def init_zone_scheduler(app):
    """
    Inicializar y arrancar el scheduler de monitoreo de zonas
    """
    zone_scheduler.init_app(app)
    zone_scheduler.start()

    # Registrar función de limpieza al cerrar la app
    import atexit
    atexit.register(zone_scheduler.stop)


# Función para ejecutar manualmente (útil para testing)
def run_manual_monitoring():
    """
    Ejecutar monitoreo manualmente (para testing o cron jobs)
    """
    from app import create_app
    from app.services.zone_monitoring import ZoneMonitoringService

    app = create_app()
    with app.app_context():
        try:
            monitoring_service = ZoneMonitoringService()
            stats = monitoring_service.run_monitoring_cycle()
            print(f"Monitoreo manual ejecutado: {stats}")
            return stats
        except Exception as e:
            print(f"Error en monitoreo manual: {e}")
            return None


if __name__ == "__main__":
    # Permitir ejecutar el script directamente para testing
    print("Ejecutando monitoreo manual...")
    stats = run_manual_monitoring()
    if stats:
        print("Monitoreo completado exitosamente")
    else:
        print("Error en el monitoreo")
