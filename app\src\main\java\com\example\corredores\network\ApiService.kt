package com.example.corredores.network

import com.example.corredores.models.*
import retrofit2.Response
import retrofit2.http.*

interface ApiService {

    // Autenticación para padres
    @POST("auth/login")
    suspend fun loginPadre(@Body request: LoginRequest): Response<LoginResponse>

    // Autenticación para hijos con token
    @POST("api/link_device")
    suspend fun vincularDispositivo(@Body request: TokenRequest): Response<TokenResponse>

    // Enviar ubicación del hijo
    @POST("api/log_location")
    suspend fun updateLocation(
        @Header("X-Child-ID") childId: String,
        @Body locationData: LocationUpdateRequest
    ): Response<LocationUpdateResponse>

    // Verificar estado de geofencing
    @GET("api/geofence/status")
    suspend fun checkGeofenceStatus(
        @Header("Authorization") token: String,
        @Query("child_id") childId: Int
    ): Response<GeofenceStatusResponse>

    // Enviar notificación al padre
    @POST("api/send_notification")
    suspend fun sendNotification(
        @Header("X-Child-ID") childId: String,
        @Body notificationData: NotificationRequest
    ): Response<NotificationResponse>

    // Registrar conexión activa del hijo
    @POST("api/register_connection")
    suspend fun registerConnection(
        @Header("X-Child-ID") childId: String
    ): Response<LocationRequestResponse>

    // Verificar solicitudes de ubicación pendientes (hijo hace polling)
    @GET("api/request_immediate_location")
    suspend fun checkPendingLocationRequests(
        @Header("X-Child-ID") childId: String
    ): Response<LocationRequestResponse>

    // Crear sesión web para dashboard
    @POST("users/api/create_web_session")
    suspend fun createWebSession(@Body request: WebSessionRequest): Response<WebSessionResponse>
}
