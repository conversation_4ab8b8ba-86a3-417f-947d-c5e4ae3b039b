<!-- app/templates/zones/editar_zona.html -->
{% extends 'base.html' %}

{% block content %}
  <h2>Editar Zona Segura</h2>
  <p><PERSON><PERSON><PERSON> hacer clic en el mapa para cambiar el centro de la zona segura.</p>

  {# El action apunta a la ruta de edición de zona, pasando el zone_id #}
  <form method="POST" action="{{ url_for('zones.editar_zona', zone_id=zone_id) }}">
    {{ form.hidden_tag() }}
    <div>
      {{ form.zone_name.label }}<br> {# Usar zone_name #}
      {{ form.zone_name(size=64) }}<br>
      {% for error in form.zone_name.errors %}
          <span style="color: red;">[{{ error }}]</span>
      {% endfor %}
    </div>
    <div>
      {{ form.schedule_start.label }}<br>
      {{ form.schedule_start() }}<br>
      {% for error in form.schedule_start.errors %}
          <span style="color: red;">[{{ error }}]</span>
      {% endfor %}
    </div>
    <div>
      {{ form.schedule_end.label }}<br>
      {{ form.schedule_end() }}<br>
      {% for error in form.schedule_end.errors %}
          <span style="color: red;">[{{ error }}]</span>
      {% endfor %}
    </div>

    <!-- Campos ocultos para Latitud y Longitud (se poblarán desde la vista/JS) -->
    {{ form.latitude(id="latitude") }}
    {{ form.longitude(id="longitude") }}
     {# Mostrar errores si los validadores DataRequired fallan #}
    {% for error in form.latitude.errors %}
        <span style="color: red;">[{{ error }}]</span><br>
    {% endfor %}
     {% for error in form.longitude.errors %}
        <span style="color: red;">[{{ error }}]</span><br>
    {% endfor %}

    <!-- Campo para seleccionar el hijo -->
    <div>
      {{ form.child_id.label }}<br>
      {{ form.child_id() }}<br>
      {% for error in form.child_id.errors %}
          <span style="color: red;">[{{ error }}]</span>
      {% endfor %}
    </div>

    <!-- Contenedor del mapa -->
    <div id="map" style="height: 400px; margin-top: 15px; border: 1px solid #ccc;"></div>

    <div style="margin-top: 15px;"> {# Espacio antes de los botones #}
      {{ form.submit() }} {# Botón de actualizar #}
      {# Enlace para volver a la lista de zonas #}
      <a href="{{ url_for('zones.ver_zonas') }}" class="btn btn-secondary" style="margin-left: 10px;"> Volver a Mis Zonas</a>
    </div>
  </form>

{% endblock %}


{% block scripts %}
  {# Incluir scripts base si existen #}
  {# {{ super() }} #}

  <!-- Script para inicializar el mapa, cargar la zona existente y permitir edición -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Inicializar el mapa centrado en Viedma por defecto
        var map = L.map('map').setView([-40.8136, -62.9936], 13);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          maxZoom: 19,
          attribution: 'Map data © <a href="https://openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Referencias a los inputs ocultos de coordenadas
        const latitudeInput = document.getElementById('latitude');
        const longitudeInput = document.getElementById('longitude');

        // Variables para guardar el marcador y el círculo actuales
        let currentMarker = null;
        let currentCircle = null;
        const zoneRadius = 50; // Radio fijo en metros

        // --- Función para dibujar el marcador y círculo ---
        function drawZone(lat, lon) {
             // Limpiar marcador y círculo anteriores
            if (currentMarker) { map.removeLayer(currentMarker); }
            if (currentCircle) { map.removeLayer(currentCircle); }

            // Dibujar nuevo marcador
             currentMarker = L.marker([lat, lon]).addTo(map)
                .bindPopup(`Centro de la Zona<br>Lat: ${lat.toFixed(6)}<br>Lon: ${lon.toFixed(6)}`)
                .openPopup();

             // Dibujar nuevo círculo
            currentCircle = L.circle([lat, lon], {
                radius: zoneRadius,
                color: 'blue',
                fillColor: '#30f',
                fillOpacity: 0.3
            }).addTo(map);

             console.log(`Zone drawn at: Lat ${lat}, Lng ${lon}`);
        }

        // --- Cargar la ubicación inicial de la zona al cargar la página ---
        const initialLatStr = latitudeInput.value;
        const initialLonStr = longitudeInput.value;

        if (initialLatStr && initialLonStr) {
            try {
                const initialLat = parseFloat(initialLatStr);
                const initialLon = parseFloat(initialLonStr);

                if (!isNaN(initialLat) && !isNaN(initialLon)) {
                    console.log(`Initial coordinates loaded: Lat ${initialLat}, Lng ${initialLon}`);
                    drawZone(initialLat, initialLon); // Dibujar la zona inicial
                    map.fitBounds(currentCircle.getBounds()); // Ajustar vista a la zona
                } else {
                    console.warn("Initial coordinates are not valid numbers:", initialLatStr, initialLonStr);
                    alert("No se pudo cargar la ubicación guardada. Por favor, haz clic en el mapa para establecerla.");
                }
            } catch (e) {
                console.error("Error parsing initial coordinates:", e);
                alert("Error al cargar la ubicación guardada. Por favor, haz clic en el mapa.");
            }
        } else {
            console.log("No initial coordinates found in hidden fields. Waiting for map click.");
             // Podrías mostrar un mensaje pidiendo al usuario que haga clic si lo deseas
        }


        // --- Manejar clics en el mapa para ACTUALIZAR la ubicación ---
        map.on('click', function(e) {
          const clickedLat = e.latlng.lat;
          const clickedLng = e.latlng.lng;

          console.log(`Map clicked (update): Lat ${clickedLat}, Lng ${clickedLng}`);

          // 1. Actualizar los campos ocultos del formulario
          if (latitudeInput && longitudeInput) {
            latitudeInput.value = clickedLat;
            longitudeInput.value = clickedLng;
            console.log(`Hidden fields updated: Lat ${latitudeInput.value}, Lng ${longitudeInput.value}`);
          } else {
              console.error("Error: Latitude or Longitude hidden input not found!");
              alert("Error interno: No se pueden guardar las coordenadas. Contacte al administrador.");
              return;
          }

          // 2. Dibujar el nuevo marcador y círculo (la función drawZone limpia los anteriores)
          drawZone(clickedLat, clickedLng);

          // Opcional: Ajustar el zoom para ver bien el nuevo círculo
          // map.fitBounds(currentCircle.getBounds()); // Puede ser molesto si el usuario está haciendo zoom
        }); // Fin de map.on('click')

    }); // Fin de DOMContentLoaded
  </script>
{% endblock %}