# app/forms/auth_forms.py

# app/forms/auth_forms.py
from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON>ield, PasswordField, SubmitField, <PERSON><PERSON>anField
from wtforms.validators import DataRequired, Length, Email, EqualTo

class RegistrationForm(FlaskForm):
    username = String<PERSON><PERSON>('Usuario', validators=[DataRequired(), Length(min=4, max=25)])
    email = StringField('Correo Electrónico', validators=[DataRequired(), Email()])
    password = PasswordField('Contraseña', validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField('Confirmar Contraseña', validators=[DataRequired(), EqualTo('password')])
    first_name = StringField('Nombre', validators=[Length(max=100)])
    last_name = StringField('Apellido', validators=[Length(max=100)])
    phone = StringField('Teléfono', validators=[Length(max=20)])
    submit = SubmitField('Registrarse')

class LoginForm(FlaskForm):
    username = <PERSON><PERSON>ield('Usuario', validators=[DataRequired()])
    password = PasswordField('Contraseña', validators=[DataRequired()])
    remember = BooleanField('Recordarme')
    submit = SubmitField('Entrar')
