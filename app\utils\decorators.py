# app/utils/decorators.py
from functools import wraps
from flask import request, jsonify, g, flash, redirect, url_for
from flask_login import current_user # Necesario para role_required
from app.models import Child, User # Importar modelos necesarios

def api_child_required(f):
    """
    Decorador para endpoints de API que requieren autenticación de un hijo.

    Busca el identificador del hijo (child_id) en la cabecera HTTP 'X-Child-ID'.
    Como alternativa, si no está en la cabecera, lo busca dentro del cuerpo JSON
    de la solicitud bajo la clave 'child_id'.

    Si se encuentra un ID válido, verifica que corresponda a un Child existente
    en la base de datos y lo almacena en el contexto global `g.child` para
    que esté disponible dentro de la función del endpoint.

    Si el ID falta, tiene un formato incorrecto, o no corresponde a un Child
    válido, devuelve una respuesta JSON con el código de error HTTP apropiado
    (400 Bad Request, 401 Unauthorized, 500 Internal Server Error).
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        child_id = request.headers.get('X-Child-ID') # Intenta obtenerlo de la cabecera

        # Si no está en la cabecera Y la solicitud es JSON, intentar desde el cuerpo
        if not child_id and request.is_json:
            data = request.get_json()
            if data: # Asegurarse de que el JSON no esté vacío
                child_id = data.get('child_id')

        # Verificar si se obtuvo un ID
        if not child_id:
            return jsonify({
                'status': 'error',
                'message': 'Identificador de hijo requerido (en cabecera X-Child-ID o como \'child_id\' en el cuerpo JSON)'
            }), 400 # Bad Request

        # Intentar convertir a entero y buscar en la base de datos
        try:
            child_id_int = int(child_id)
            # Buscar el hijo por su clave primaria
            child = Child.query.get(child_id_int)
        except ValueError:
            # El ID proporcionado no era un número válido
            return jsonify({'status': 'error', 'message': 'Identificador de hijo inválido (formato incorrecto)'}), 400 # Bad Request
        except Exception as e:
             # Captura cualquier otro error durante la consulta a la base de datos
             # Loggear el error es importante para depuración
             print(f"Error buscando child en BD para API: {e}") # Log simple
             # No exponer detalles internos al cliente
             return jsonify({'status': 'error', 'message': 'Error interno del servidor al verificar el hijo'}), 500

        # Verificar si se encontró un registro de Child con ese ID
        if not child:
            # No se encontró el hijo, podría ser un ID inválido o inexistente
            return jsonify({'status': 'error', 'message': 'Identificador de hijo no encontrado o inválido'}), 401 # Unauthorized (o 404 Not Found, según preferencia)

        # ¡Validación exitosa! El hijo existe.
        # Almacenar el objeto Child encontrado en el contexto global 'g' de Flask.
        # Esto permite que la función del endpoint acceda a la información del hijo
        # simplemente usando g.child (ej: g.child.child_id, g.child.first_name).
        g.child = child

        # Ejecutar la función original del endpoint (la que fue decorada)
        return f(*args, **kwargs)
    return decorated_function


def role_required(*roles):
    """
    Decorador para rutas web (no API) que verifica si el usuario logueado
    tiene al menos uno de los roles especificados.

    Si el usuario no está logueado, redirige al login.
    Si el usuario está logueado pero no tiene ninguno de los roles requeridos,
    muestra un mensaje flash de error y redirige al dashboard principal.

    Uso:
    @app.route('/admin_only')
    @login_required
    @role_required('ADMIN')
    def admin_view():
        # ...

    @app.route('/supervisor_or_admin')
    @login_required
    @role_required('ADMIN', 'SUPERVISOR')
    def supervisor_admin_view():
        # ...
    """
    def wrapper(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Primero, asegurarse de que el usuario esté autenticado
            # Flask-Login generalmente maneja esto con @login_required, pero
            # una doble verificación no hace daño y permite redirigir al login.
            if not current_user.is_authenticated:
                flash('Debes iniciar sesión para acceder a esta página.', 'warning')
                # Asume que tu blueprint de autenticación tiene una ruta llamada 'login'
                return redirect(url_for('auth.login'))

            # Obtener los nombres de los roles del usuario actual
            # Usamos un set para una búsqueda eficiente
            user_roles_names = {role.role_name for role in current_user.roles}
            # Convertir los roles requeridos (pasados como argumentos) a un set
            required_roles_set = set(roles)

            # Verificar si hay alguna intersección entre los roles del usuario y los requeridos
            if not required_roles_set.intersection(user_roles_names):
                # El usuario no tiene ninguno de los roles necesarios
                flash(f'Acceso denegado. Necesitas uno de los siguientes roles: {", ".join(roles)}.', 'danger')
                # Redirigir a una página segura por defecto, como el dashboard
                # Asume que tu blueprint de usuarios tiene una ruta llamada 'dashboard'
                return redirect(url_for('users.dashboard'))

            # Si el usuario tiene al menos uno de los roles requeridos, permitir el acceso
            # Ejecutar la función original de la vista
            return f(*args, **kwargs)
        return decorated_function
    return wrapper