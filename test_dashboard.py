#!/usr/bin/env python3
"""
Script para probar el dashboard y ver errores específicos
"""

import requests
import sys

def test_dashboard():
    """Prueba el endpoint del dashboard"""
    try:
        # URL del dashboard
        url = "http://localhost:5004/users/dashboard"
        
        print(f"Probando: {url}")
        
        # Hacer solicitud
        response = requests.get(url, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 500:
            print("ERROR 500 - Internal Server Error")
            print("Contenido de la respuesta:")
            print(response.text[:1000])  # Primeros 1000 caracteres
        elif response.status_code == 200:
            print("✅ Dashboard carga correctamente")
        else:
            print(f"Código de estado inesperado: {response.status_code}")
            print(response.text[:500])
            
    except requests.exceptions.ConnectionError:
        print("❌ No se puede conectar al servidor. ¿Está ejecutándose?")
    except requests.exceptions.Timeout:
        print("❌ Timeout al conectar con el servidor")
    except Exception as e:
        print(f"❌ Error inesperado: {e}")

if __name__ == "__main__":
    test_dashboard()
