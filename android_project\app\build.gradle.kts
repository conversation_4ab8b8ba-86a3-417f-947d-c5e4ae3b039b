// Indica que este script usa la sintaxis de Kotlin para Gradle
plugins {
    // Plugin esencial para construir una aplicación Android
    id("com.android.application")
    // Plugin para habilitar el soporte de Kotlin en Android
    id("org.jetbrains.kotlin.android")
    // (Opcional pero común) Habilita el uso de Kotlin Parcelize para simplificar el paso de objetos entre componentes
    // id("kotlin-parcelize")
}

android {
    // Identificador único del paquete de la aplicación
    namespace = "com.example.corredoresseguroshijo"
    // Versión del SDK de Android contra la que se compila la aplicación
    compileSdk = 34 // Recomendación: Usar la última versión estable (ej. 34 al momento de escribir esto) en lugar de betas (35) a menos que necesites características específicas

    defaultConfig {
        // Identificador único de la aplicación en el dispositivo y la Play Store
        applicationId = "com.example.corredoresseguroshijo"
        // Versión mínima de Android requerida para ejecutar la aplicación
        minSdk = 24 // Android 7.0 (Nougat) - Buena base
        // Versión del SDK contra la que se ha probado la aplicación (idealmente igual a compileSdk)
        targetSdk = 34 // Debe coincidir o ser cercano a compileSdk

        // Código de versión interno (incremental para cada release en Play Store)
        versionCode = 1
        // Nombre de versión visible para el usuario
        versionName = "1.0"

        // Runner para ejecutar pruebas instrumentadas de Android
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        // Configuración para builds de lanzamiento (producción)
        release {
            // Habilitar la ofuscación y reducción de código (¡recomendado para producción!)
            isMinifyEnabled = false // Cambiar a true para release builds reales
            // Archivos de reglas para ProGuard/R8 (ofuscación y eliminación de código no usado)
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro" // Tu archivo de reglas personalizadas
            )
            // Aquí podrías definir claves de API de producción, URLs diferentes, etc.
            // buildConfigField "String", "API_BASE_URL", "\"https://patagoniaservers.com.ar:5004/api/\""
        }
        // Configuración para builds de depuración (desarrollo)
        debug {
            // Puedes habilitar opciones de depuración específicas aquí si es necesario
            applicationIdSuffix = ".debug" // Sufijo opcional para diferenciar builds debug
            isDebuggable = true
             // Podrías definir una URL base diferente para depuración si usas un servidor local
            // buildConfigField "String", "API_BASE_URL", "\"http://10.0.2.2:5004/api/\""
        }
    }

    // Opciones de compilación para código Java (si lo hubiera) y compatibilidad
    compileOptions {
        // Nivel de lenguaje Java compatible
        sourceCompatibility = JavaVersion.VERSION_11 // O VERSION_1_8 si prefieres
        targetCompatibility = JavaVersion.VERSION_11 // O VERSION_1_8
    }

    // Opciones específicas para el compilador de Kotlin
    kotlinOptions {
        // Versión de JVM objetivo para el bytecode generado
        jvmTarget = "11" // O "1.8" si usaste 1.8 arriba
    }

    // Habilitar ViewBinding (forma recomendada y segura de acceder a las vistas, alternativa a findViewById)
    // buildFeatures {
    //     viewBinding = true
    // }
}

// Bloque donde se declaran las dependencias de la aplicación
dependencies {

    // --- Dependencias Core de AndroidX y UI ---
    implementation("androidx.core:core-ktx:1.13.1") // Extensiones Kotlin para AndroidX Core
    implementation("androidx.appcompat:appcompat:1.7.0") // Soporte para compatibilidad hacia atrás de UI
    implementation("com.google.android.material:material:1.12.0") // Componentes de Material Design
    implementation("androidx.constraintlayout:constraintlayout:2.1.4") // Layout para interfaces complejas
    implementation("androidx.activity:activity-ktx:1.9.0") // Extensiones KTX para Activity


    // --- Dependencias que añadimos ---

    // Networking - Retrofit para llamadas HTTP y Gson para parsear JSON
    implementation("com.squareup.retrofit2:retrofit:2.9.0")         // Versión estable popular
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")   // Convertidor para JSON (Gson)
    implementation("com.google.code.gson:gson:2.10.1")              // Librería Gson

    // (Opcional pero muy recomendado) Logging Interceptor para Retrofit (ver tráfico de red en Logcat)
    implementation("com.squareup.okhttp3:logging-interceptor:4.11.0") // Asegúrate que la versión sea compatible con Retrofit/OkHttp

    // Google Play Services - Location para obtener la ubicación
    implementation("com.google.android.gms:play-services-location:21.2.0") // Usar la última versión estable

    // Ciclo de Vida (Lifecycle) - ViewModel y LiveData (para manejar estado de UI y datos observables)
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.1") // ViewModel con extensiones Kotlin
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.8.1")   // LiveData con extensiones Kotlin
    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.8.1")    // Para usar 'viewModelScope' en ViewModels (Coroutines)

    // Corrutinas de Kotlin para operaciones asíncronas
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3")

    // WebSockets (Socket.IO) para notificaciones inmediatas
    implementation("io.socket:socket.io-client:2.0.1")


    // --- Dependencias de Pruebas ---
    testImplementation("junit:junit:4.13.2") // Pruebas unitarias locales (en JVM)
    androidTestImplementation("androidx.test.ext:junit:1.1.5") // Pruebas instrumentadas (en dispositivo/emulador)
    androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1") // Pruebas de UI Espresso

    // --- Eliminando referencias a libs (si no usas catálogo de versiones) ---
    // Si NO estás usando el catálogo de versiones (libs en settings.gradle),
    // elimina las referencias como libs.androidx.core.ktx y usa las cadenas
    // directamente como se muestra arriba ("androidx.core:core-ktx:1.13.1").
    // Si SÍ usas catálogo de versiones, mantén el formato alias(libs...)
    // y asegúrate de que las librerías estén definidas en tu archivo libs.versions.toml.

    // Ejemplo de cómo se vería si NO usaras catálogo de versiones para las dependencias base:
    // implementation("androidx.core:core-ktx:1.13.1")
    // implementation("androidx.appcompat:appcompat:1.7.0")
    // implementation("com.google.android.material:material:1.12.0")
    // implementation("androidx.activity:activity-ktx:1.9.0") // Reemplaza libs.androidx.activity
    // implementation("androidx.constraintlayout:constraintlayout:2.1.4")
    // testImplementation("junit:junit:4.13.2")
    // androidTestImplementation("androidx.test.ext:junit:1.1.5") // Reemplaza libs.androidx.junit
    // androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1") // Reemplaza libs.androidx.espresso.core


}
