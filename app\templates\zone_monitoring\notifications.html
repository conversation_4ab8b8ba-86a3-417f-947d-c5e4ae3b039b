{% extends 'base.html' %}

{% block title %}Notificaciones de Zonas - Corredores Seguros{% endblock %}

{% block head %}
    {{ super() }}
    <style>
        /* Estilos consistentes con el dashboard original */
        .notifications-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }

        .notifications-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #ddd;
            gap: 20px;
            flex-wrap: wrap;
        }

        .notifications-title {
            color: #333;
            font-size: 28px;
            margin: 0;
        }

        .back-button {
            background: #6c757d;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s;
            white-space: nowrap;
            flex-shrink: 0;
            display: inline-flex;
            align-items: center;
            font-size: 14px;
            font-weight: 500;
            min-width: fit-content;
        }

        .back-button:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .stat-card.danger { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); }
        .stat-card.warning { background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%); color: #333; }
        .stat-card.dark { background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); }

        .stat-number {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }

        .filters-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border: 1px solid #ddd;
        }

        .filters-title {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .filters-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-control {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s;
            font-size: 14px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
        }

        .notifications-list {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .list-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .list-title {
            margin: 0;
            color: #333;
            font-size: 18px;
        }

        .notification-item {
            padding: 20px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s;
        }

        .notification-item:hover {
            background: #f8f9fa;
        }

        .notification-item.unread {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .notification-main {
            flex: 1;
        }

        .notification-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            flex-wrap: wrap;
            gap: 8px;
        }

        .notification-icon {
            font-size: 16px;
            margin-right: 8px;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .badge-warning { background: #ffc107; color: #333; }
        .badge-danger { background: #dc3545; color: white; }
        .badge-dark { background: #343a40; color: white; }
        .badge-primary { background: #007bff; color: white; }

        .child-name {
            font-weight: bold;
            color: #333;
            margin-left: 8px;
        }

        .notification-message {
            margin: 10px 0;
            color: #555;
            line-height: 1.4;
        }

        .notification-time {
            font-size: 12px;
            color: #666;
        }

        .notification-actions {
            margin-left: 15px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 30px;
            gap: 5px;
        }

        .page-link {
            padding: 8px 12px;
            border: 1px solid #ddd;
            color: #007bff;
            text-decoration: none;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .page-link:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .page-link.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .page-link.disabled {
            color: #6c757d;
            pointer-events: none;
            background: #fff;
            border-color: #dee2e6;
        }

        @media (max-width: 768px) {
            .notifications-container {
                padding: 15px;
            }

            .notifications-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
                align-items: center;
            }

            .notifications-title {
                font-size: 24px;
            }

            .back-button {
                padding: 10px 20px;
                font-size: 13px;
            }

            .filters-form {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .notification-content {
                flex-direction: column;
                gap: 15px;
            }

            .notification-actions {
                margin-left: 0;
                align-self: flex-start;
            }

            .notification-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="notifications-container">
    <!-- Header -->
    <div class="notifications-header">
        <div style="flex: 1; min-width: 0;">
            <h1 class="notifications-title"><i class="fas fa-bell" style="margin-right: 10px;"></i>Notificaciones de Zonas</h1>
            <p style="color: #666; margin: 5px 0 0 0;">Historial completo de alertas de zonas seguras</p>
        </div>
        <div style="flex-shrink: 0;">
            <a href="{{ url_for('users.dashboard') }}" class="back-button">
                <i class="fas fa-arrow-left" style="margin-right: 8px;"></i>Volver al Dashboard
            </a>
        </div>
    </div>

    <!-- Estadísticas -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-label">
                <i class="fas fa-bell" style="margin-right: 5px;"></i>Total
            </div>
        </div>
        <div class="stat-card danger">
            <div class="stat-number">{{ stats.unread }}</div>
            <div class="stat-label">
                <i class="fas fa-exclamation-circle" style="margin-right: 5px;"></i>No Leídas
            </div>
        </div>
        <div class="stat-card warning">
            <div class="stat-number">{{ stats.early_warning }}</div>
            <div class="stat-label">
                <i class="fas fa-clock" style="margin-right: 5px;"></i>Alertas Tempranas
            </div>
        </div>
        <div class="stat-card dark">
            <div class="stat-number">{{ stats.late_arrival + stats.missed }}</div>
            <div class="stat-label">
                <i class="fas fa-times-circle" style="margin-right: 5px;"></i>Críticas
            </div>
        </div>
    </div>

    <!-- Filtros -->
    <div class="filters-section">
        <h3 class="filters-title"><i class="fas fa-filter" style="margin-right: 8px;"></i>Filtros</h3>
        <form method="GET" class="filters-form">
            <div class="form-group">
                <label for="type" class="form-label">Tipo de Notificación</label>
                <select class="form-control" id="type" name="type">
                    <option value="">Todos los tipos</option>
                    <option value="zone_early_warning" {% if current_type == 'zone_early_warning' %}selected{% endif %}>
                        ⚠️ Alertas Tempranas
                    </option>
                    <option value="zone_late_arrival" {% if current_type == 'zone_late_arrival' %}selected{% endif %}>
                        🚨 Llegadas Tardías
                    </option>
                    <option value="zone_missed" {% if current_type == 'zone_missed' %}selected{% endif %}>
                        ❌ Zonas Perdidas
                    </option>
                </select>
            </div>
            <div class="form-group">
                <label for="date_from" class="form-label">Desde</label>
                <input type="date" class="form-control" id="date_from" name="date_from" value="{{ current_date_from }}">
            </div>
            <div class="form-group">
                <label for="date_to" class="form-label">Hasta</label>
                <input type="date" class="form-control" id="date_to" name="date_to" value="{{ current_date_to }}">
            </div>
            <div class="form-group">
                <div style="display: flex; gap: 10px;">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search" style="margin-right: 5px;"></i>Filtrar
                    </button>
                    <a href="{{ url_for('zone_monitoring.notifications_page') }}" class="btn btn-secondary">
                        <i class="fas fa-times" style="margin-right: 5px;"></i>Limpiar
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Lista de Notificaciones -->
    <div class="notifications-list">
        <div class="list-header">
            <h3 class="list-title">
                <i class="fas fa-list" style="margin-right: 8px;"></i>Notificaciones
                {% if notifications.total > 0 %}
                    ({{ notifications.total }} total{{ 's' if notifications.total != 1 else '' }})
                {% endif %}
            </h3>
            {% if stats.unread > 0 %}
                <button class="btn btn-success btn-sm" onclick="markAllAsRead()">
                    <i class="fas fa-check-double" style="margin-right: 5px;"></i>Marcar todas como leídas
                </button>
            {% endif %}
        </div>
        <div>
            {% if notifications.items %}
                {% for notification in notifications.items %}
                    <div class="notification-item {% if not notification.viewed_at %}unread{% endif %}"
                         data-notification-id="{{ notification.notification_id }}">
                        <div class="notification-content">
                            <div class="notification-main">
                                <div class="notification-header">
                                    {% if notification.notification_type == 'zone_early_warning' %}
                                        <i class="fas fa-clock notification-icon" style="color: #ffc107;"></i>
                                        <span class="badge badge-warning">ALERTA TEMPRANA</span>
                                    {% elif notification.notification_type == 'zone_late_arrival' %}
                                        <i class="fas fa-exclamation-triangle notification-icon" style="color: #dc3545;"></i>
                                        <span class="badge badge-danger">LLEGADA TARDÍA</span>
                                    {% elif notification.notification_type == 'zone_missed' %}
                                        <i class="fas fa-times-circle notification-icon" style="color: #343a40;"></i>
                                        <span class="badge badge-dark">ZONA PERDIDA</span>
                                    {% endif %}

                                    <span class="child-name">{{ notification.child.first_name }} {{ notification.child.last_name }}</span>

                                    {% if not notification.viewed_at %}
                                        <span class="badge badge-primary">NUEVO</span>
                                    {% endif %}
                                </div>

                                <div class="notification-message">{{ notification.message_text }}</div>

                                <div class="notification-time">
                                    <i class="fas fa-calendar" style="margin-right: 5px;"></i>
                                    {{ notification.created_at.strftime('%d/%m/%Y %H:%M:%S') }}
                                    {% if notification.viewed_at %}
                                        | <i class="fas fa-eye" style="margin-left: 10px; margin-right: 5px;"></i>Leída el {{ notification.viewed_at.strftime('%d/%m/%Y %H:%M') }}
                                    {% endif %}
                                </div>
                            </div>

                            {% if not notification.viewed_at %}
                                <div class="notification-actions">
                                    <button class="btn btn-primary btn-sm"
                                            onclick="markAsRead({{ notification.notification_id }})">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-inbox"></i>
                    </div>
                    <h4>No hay notificaciones</h4>
                    <p>No se encontraron notificaciones con los filtros aplicados.</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Paginación -->
    {% if notifications.pages > 1 %}
        <div class="pagination">
            {% if notifications.has_prev %}
                <a class="page-link" href="{{ url_for('zone_monitoring.notifications_page', page=notifications.prev_num, type=current_type, date_from=current_date_from, date_to=current_date_to) }}">
                    <i class="fas fa-chevron-left"></i>
                </a>
            {% endif %}

            {% for page_num in notifications.iter_pages() %}
                {% if page_num %}
                    {% if page_num != notifications.page %}
                        <a class="page-link" href="{{ url_for('zone_monitoring.notifications_page', page=page_num, type=current_type, date_from=current_date_from, date_to=current_date_to) }}">
                            {{ page_num }}
                        </a>
                    {% else %}
                        <span class="page-link active">{{ page_num }}</span>
                    {% endif %}
                {% else %}
                    <span class="page-link disabled">…</span>
                {% endif %}
            {% endfor %}

            {% if notifications.has_next %}
                <a class="page-link" href="{{ url_for('zone_monitoring.notifications_page', page=notifications.next_num, type=current_type, date_from=current_date_from, date_to=current_date_to) }}">
                    <i class="fas fa-chevron-right"></i>
                </a>
            {% endif %}
        </div>
    {% endif %}
</div>

<script>
    function markAsRead(notificationId) {
        fetch(`/zone_monitoring/mark_notification_viewed/${notificationId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content')
            },
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Actualizar la UI
                const element = document.querySelector(`[data-notification-id="${notificationId}"]`);
                if (element) {
                    element.classList.remove('unread');
                    const badge = element.querySelector('.badge-primary');
                    const button = element.querySelector('button');
                    if (badge) badge.remove();
                    if (button) button.parentElement.remove();
                }

                // Recargar página después de 1 segundo para actualizar estadísticas
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error al marcar como leída');
        });
    }
    
    function markAllAsRead() {
        const unreadButtons = document.querySelectorAll('[data-notification-id] button');
        let promises = [];

        unreadButtons.forEach(button => {
            const notificationId = button.closest('[data-notification-id]').getAttribute('data-notification-id');
            promises.push(
                fetch(`/zone_monitoring/mark_notification_viewed/${notificationId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content')
                    },
                    body: JSON.stringify({})
                })
            );
        });

        Promise.all(promises).then(() => {
            window.location.reload();
        }).catch(error => {
            console.error('Error:', error);
            alert('Error al marcar todas como leídas');
        });
    }

    // Función para marcar todas las notificaciones como leídas (alias)
    function markAllNotificationsRead() {
        markAllAsRead();
    }

    // Función para solicitar permisos de notificación
    function requestNotificationPermission() {
        console.log('🔔 Usuario solicitó permisos de notificación...');

        if (!("Notification" in window)) {
            alert('Tu navegador no soporta notificaciones push');
            return;
        }

        if (Notification.permission === "granted") {
            console.log('✅ Permisos ya concedidos, mostrando notificación de prueba...');
            showTestNotification();
            return;
        }

        Notification.requestPermission().then(function(permission) {
            console.log('🔔 Respuesta del usuario:', permission);

            if (permission === "granted") {
                showWelcomeNotification();
                showTestNotification();
            } else {
                alert('Para recibir notificaciones de campanita, debes permitir las notificaciones en tu navegador.');
            }
        });
    }

    // Función para mostrar notificación de bienvenida
    function showWelcomeNotification() {
        if (Notification.permission === "granted") {
            new Notification("🔔 Notificaciones Activadas", {
                body: "Recibirás alertas cuando tus hijos no lleguen a las zonas asignadas",
                icon: "/static/img/notification-icon.png",
                tag: "welcome"
            });
        }
    }

    // Función para mostrar notificación de prueba
    function showTestNotification() {
        if (Notification.permission === "granted") {
            const testNotification = new Notification("🧪 Notificación de Prueba", {
                body: "¡Las notificaciones están funcionando correctamente!",
                icon: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzIiIGZpbGw9IiNGRkM5MDAiLz4KPHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxNiIgeT0iMTYiPgo8cGF0aCBkPSJNMTggOEMxOCA2LjQwODcgMTcuMzY4NCA0Ljg4MjU4IDE2LjI0MjYgMy43NTczNkMxNS4xMTc0IDIuNjMyMTQgMTMuNTkxMyAyIDEyIDJDMTAuNDA4NyAyIDguODgyNTggMi42MzIxNCA3Ljc1NzM2IDMuNzU3MzZDNi42MzIxNCA0Ljg4MjU4IDYgNi40MDg3IDYgOEM2IDEwLjEyMTcgNS4xNTY5NiAxMi4xNTY2IDMuNzU3MzYgMTMuNTU1NkMyLjM1Nzc2IDE0Ljk1NSAyIDE2Ljk3ODMgMiAxOUgyMkMyMiAxNi45NzgzIDIxLjY0MjIgMTQuOTU1IDIwLjI0MjYgMTMuNTU1NkMxOC44NDMgMTIuMTU2NiAxOCAxMC4xMjE3IDE4IDhaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTMuNzMgMjFDMTMuNTU0MyAyMS4zMDMxIDEzLjMwMzEgMjEuNTU0MyAxMyAyMS43M0MxMi42OTY5IDIxLjkwNTcgMTIuMzUzNCAyMiAxMiAyMkMxMS42NDY2IDIyIDExLjMwMzEgMjEuOTA1NyAxMSAyMS43M0MxMC42OTY5IDIxLjU1NDMgMTAuNDQ1NyAyMS4zMDMxIDEwLjI3IDIxSDE0LjczSDEzLjczWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cg==",
                tag: "test",
                requireInteraction: false
            });

            // Auto-cerrar después de 3 segundos
            setTimeout(() => {
                testNotification.close();
            }, 3000);
        }
    }
</script>
{% endblock %}
