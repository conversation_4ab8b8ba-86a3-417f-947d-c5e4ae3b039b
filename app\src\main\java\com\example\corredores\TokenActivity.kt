package com.example.corredores

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.corredores.models.TokenRequest
import com.example.corredores.network.NetworkClient
import com.example.corredores.services.LocationService
import com.example.corredores.utils.PreferenceManager
import kotlinx.coroutines.launch

class TokenActivity : AppCompatActivity() {

    private lateinit var preferenceManager: PreferenceManager
    private lateinit var etToken: EditText
    private lateinit var btnConnect: Button
    private lateinit var progressBar: ProgressBar

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_token)

        preferenceManager = PreferenceManager(this)

        initViews()
        setupClickListeners()
    }

    private fun initViews() {
        etToken = findViewById(R.id.et_token)
        btnConnect = findViewById(R.id.btn_connect)
        progressBar = findViewById(R.id.progress_bar)
    }

    private fun setupClickListeners() {
        btnConnect.setOnClickListener {
            performTokenLogin()
        }
    }

    private fun performTokenLogin() {
        val token = etToken.text.toString().trim()

        if (token.isEmpty()) {
            etToken.error = "Ingresa el token"
            return
        }

        if (token.length != 16) {
            etToken.error = "El token debe tener 16 caracteres"
            return
        }

        showLoading(true)

        lifecycleScope.launch {
            try {
                val response = NetworkClient.apiService.vincularDispositivo(TokenRequest(token))

                if (response.isSuccessful) {
                    val tokenResponse = response.body()
                    if (tokenResponse != null && tokenResponse.status == "success") {
                        // Guardar datos del hijo
                        tokenResponse.child_id?.let { childId ->
                            preferenceManager.setChildId(childId)
                            preferenceManager.setLoggedIn(true)
                            // Guardar el token para reautenticación web
                            preferenceManager.setLastToken(token)

                            // Iniciar servicio de ubicación en segundo plano
                            LocationService.startService(this@TokenActivity)

                            // Redirigir automáticamente al dashboard del hijo en el servidor
                            val intent = Intent(this@TokenActivity, MainActivity::class.java)
                            intent.putExtra("redirect_to_dashboard", true)
                            startActivity(intent)
                            finish()
                        } ?: run {
                            showError("Error: No se recibió el ID del hijo")
                        }
                    } else {
                        showError(tokenResponse?.message ?: "Token inválido")
                    }
                } else {
                    val errorMessage = when (response.code()) {
                        400 -> "Token inválido o expirado"
                        404 -> "Token no encontrado"
                        else -> "Error del servidor: ${response.code()}"
                    }
                    showError(errorMessage)
                }
            } catch (e: Exception) {
                showError("Error de conexión: ${e.message}")
            } finally {
                showLoading(false)
            }
        }
    }

    private fun showLoading(show: Boolean) {
        progressBar.visibility = if (show) View.VISIBLE else View.GONE
        btnConnect.isEnabled = !show
        etToken.isEnabled = !show
    }

    private fun showError(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }
}
