#config.py

import os
from dotenv import load_dotenv
from cryptography.fernet import Fernet

basedir = os.path.abspath(os.path.dirname(__file__))
load_dotenv(os.path.join(basedir, '.env'))

class Config:
    SECRET_KEY = os.getenv('SECRET_KEY', 'tu_clave_secreta_por_defecto')
    
    # Configuración para MySQL
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL', 'mysql+pymysql://joacoabe:isaias52@localhost:3306/corredores_seguros')
    
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0')
    CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')
    FERNET_KEY = os.getenv('FERNET_KEY', Fernet.generate_key().decode())
