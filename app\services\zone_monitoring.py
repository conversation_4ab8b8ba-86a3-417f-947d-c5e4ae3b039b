# app/services/zone_monitoring.py

import json
import math
from datetime import datetime, date, time, timedelta
from typing import List, Dict, Optional, Tuple
from flask import current_app
from app import db
from app.models import (
    SafeZone, ChildZoneAssignment, ZoneComplianceLog,
    UserLocationLog, ChildNotification, Child
)
from app.utils.encryption import Encryption


class ZoneMonitoringService:
    """
    Servicio para monitorear el cumplimiento de zonas seguras
    y enviar notificaciones automáticas a los padres.
    """

    def __init__(self):
        self.encryption = None
        self._init_encryption()

    def _init_encryption(self):
        """Inicializar el servicio de encriptación"""
        try:
            fernet_key = current_app.config.get('FERNET_KEY')
            if fernet_key:
                self.encryption = Encryption(fernet_key)
        except Exception as e:
            current_app.logger.error(f"Error inicializando encriptación: {e}")

    def calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """
        Calcular distancia entre dos puntos usando fórmula de Haversine
        Retorna distancia en metros
        """
        R = 6371000  # Radio de la Tierra en metros

        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lon = math.radians(lon2 - lon1)

        a = (math.sin(delta_lat / 2) * math.sin(delta_lat / 2) +
             math.cos(lat1_rad) * math.cos(lat2_rad) *
             math.sin(delta_lon / 2) * math.sin(delta_lon / 2))
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

        return R * c

    def is_child_in_zone(self, child_id: int, zone_id: int) -> Tuple[bool, Optional[float]]:
        """
        Verificar si un hijo está actualmente dentro de una zona específica
        Retorna (está_en_zona, distancia_al_centro)
        """
        try:
            # Obtener la zona
            zone = SafeZone.query.get(zone_id)
            if not zone:
                return False, None

            # Desencriptar datos de la zona
            if not self.encryption:
                current_app.logger.error("Encriptación no disponible")
                return False, None

            zone_data = json.loads(self.encryption.decrypt(zone.encrypted_zone_data))
            zone_lat = zone_data['lat']
            zone_lon = zone_data['lon']
            zone_radius = zone_data['radius']

            # Obtener la ubicación más reciente del hijo
            latest_location = UserLocationLog.query.filter_by(
                child_id=child_id
            ).order_by(UserLocationLog.recorded_at.desc()).first()

            if not latest_location:
                return False, None

            # Desencriptar ubicación del hijo
            location_data = json.loads(self.encryption.decrypt(latest_location.encrypted_location_data))
            child_lat = location_data['lat']
            child_lon = location_data['lng']

            # Calcular distancia
            distance = self.calculate_distance(child_lat, child_lon, zone_lat, zone_lon)

            # Verificar si está dentro del radio
            is_inside = distance <= zone_radius

            return is_inside, distance

        except Exception as e:
            current_app.logger.error(f"Error verificando si hijo {child_id} está en zona {zone_id}: {e}")
            return False, None

    def create_daily_compliance_logs(self, target_date: date = None) -> int:
        """
        Crear logs de cumplimiento para todas las asignaciones activas
        para un día específico (por defecto hoy)
        Retorna número de logs creados
        """
        if target_date is None:
            target_date = date.today()

        try:
            # Obtener todas las asignaciones activas
            assignments = ChildZoneAssignment.query.filter_by(
                status='assigned',
                notifications_enabled=True
            ).all()

            logs_created = 0

            for assignment in assignments:
                # Verificar si ya existe un log para esta fecha
                existing_log = ZoneComplianceLog.query.filter_by(
                    assignment_id=assignment.assignment_id,
                    compliance_date=target_date
                ).first()

                if existing_log:
                    continue  # Ya existe, saltar

                # Crear nuevo log de cumplimiento
                compliance_log = ZoneComplianceLog(
                    assignment_id=assignment.assignment_id,
                    child_id=assignment.child_id,
                    zone_id=assignment.zone_id,
                    compliance_date=target_date,
                    expected_time=assignment.zone.schedule_start,
                    status='pending'
                )

                db.session.add(compliance_log)
                logs_created += 1

            db.session.commit()
            current_app.logger.info(f"Creados {logs_created} logs de cumplimiento para {target_date}")
            return logs_created

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creando logs de cumplimiento: {e}")
            return 0

    def check_zone_arrivals(self) -> int:
        """
        Verificar llegadas a zonas y actualizar logs de cumplimiento
        Retorna número de llegadas detectadas
        """
        try:
            current_time = datetime.now().time()
            today = date.today()

            # Obtener logs pendientes de hoy
            pending_logs = ZoneComplianceLog.query.filter_by(
                compliance_date=today,
                status='pending'
            ).all()

            arrivals_detected = 0

            for log in pending_logs:
                # Verificar si el hijo está en la zona
                is_in_zone, distance = self.is_child_in_zone(log.child_id, log.zone_id)

                if is_in_zone:
                    # El hijo ha llegado a la zona
                    log.actual_arrival_time = current_time
                    log.status = 'arrived'

                    # Calcular si llegó tarde
                    expected_datetime = datetime.combine(today, log.expected_time)
                    actual_datetime = datetime.combine(today, current_time)

                    if actual_datetime > expected_datetime:
                        time_diff = actual_datetime - expected_datetime
                        log.minutes_late = int(time_diff.total_seconds() / 60)
                        log.status = 'late'

                    arrivals_detected += 1
                    current_app.logger.info(f"Hijo {log.child_id} llegó a zona {log.zone_id} a las {current_time}")

            db.session.commit()
            return arrivals_detected

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error verificando llegadas a zonas: {e}")
            return 0

    def send_notification(self, child_id: int, father_id: int, notification_type: str, message: str) -> bool:
        """
        Enviar notificación al padre (base de datos + push notification)
        """
        try:
            # 1. Guardar notificación en base de datos
            notification = ChildNotification(
                child_id=child_id,
                father_id=father_id,
                notification_type=notification_type,
                message_text=message
            )
            db.session.add(notification)
            db.session.commit()

            current_app.logger.info(f"Notificación BD enviada: {notification_type} - Hijo {child_id} -> Padre {father_id}")

            # 2. Enviar notificación push FCM
            try:
                from app.services.fcm_service import send_zone_notification_to_father
                from app.models import Child

                # Obtener información del hijo
                child = Child.query.get(child_id)
                child_name = f"{child.first_name} {child.last_name}" if child else f"Hijo {child_id}"

                # Extraer nombre de zona del mensaje si es posible
                zone_name = "zona asignada"  # Valor por defecto
                if "zona" in message.lower():
                    # Intentar extraer el nombre de la zona del mensaje
                    parts = message.split("zona")
                    if len(parts) > 1:
                        zone_name = parts[1].strip().split()[0] if parts[1].strip() else "zona asignada"

                # Enviar notificación push
                push_sent = send_zone_notification_to_father(
                    father_id=father_id,
                    child_name=child_name,
                    zone_name=zone_name,
                    notification_type=notification_type
                )

                if push_sent:
                    current_app.logger.info(f"Notificación PUSH enviada: {notification_type} - Hijo {child_id} -> Padre {father_id}")
                else:
                    current_app.logger.warning(f"Notificación PUSH falló: {notification_type} - Hijo {child_id} -> Padre {father_id}")

            except Exception as push_error:
                current_app.logger.error(f"Error enviando notificación push: {push_error}")
                # No fallar si solo falla el push, la notificación BD ya se guardó

            return True

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error enviando notificación: {e}")
            return False

    def check_early_warnings(self) -> int:
        """
        Verificar y enviar alertas tempranas (5 minutos antes de la hora esperada)
        """
        try:
            current_time = datetime.now()
            today = date.today()

            # Obtener logs pendientes de hoy
            pending_logs = ZoneComplianceLog.query.filter_by(
                compliance_date=today,
                status='pending',
                early_warning_sent=False
            ).all()

            warnings_sent = 0

            for log in pending_logs:
                assignment = log.assignment
                child = log.child
                zone = log.zone

                # Calcular tiempo de alerta temprana
                expected_datetime = datetime.combine(today, log.expected_time)
                warning_time = expected_datetime - timedelta(minutes=assignment.early_warning_minutes)

                # Si es hora de enviar la alerta temprana
                if current_time >= warning_time and current_time < expected_datetime:
                    # Verificar si el hijo NO está en la zona
                    is_in_zone, distance = self.is_child_in_zone(log.child_id, log.zone_id)

                    if not is_in_zone:
                        message = (f"⚠️ ALERTA TEMPRANA: {child.first_name} {child.last_name} "
                                 f"debe llegar a '{zone.zone_name}' en {assignment.early_warning_minutes} minutos "
                                 f"(a las {log.expected_time.strftime('%H:%M')})")

                        if self.send_notification(
                            child_id=log.child_id,
                            father_id=zone.father_id,
                            notification_type='zone_early_warning',
                            message=message
                        ):
                            log.early_warning_sent = True
                            warnings_sent += 1

            db.session.commit()
            return warnings_sent

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error verificando alertas tempranas: {e}")
            return 0

    def check_late_arrivals(self) -> int:
        """
        Verificar y notificar llegadas tardías
        """
        try:
            current_time = datetime.now()
            today = date.today()

            # Obtener logs pendientes donde ya pasó la hora esperada
            pending_logs = ZoneComplianceLog.query.filter_by(
                compliance_date=today,
                status='pending',
                late_notification_sent=False
            ).all()

            late_notifications_sent = 0

            for log in pending_logs:
                expected_datetime = datetime.combine(today, log.expected_time)

                # Si ya pasó la hora esperada
                if current_time > expected_datetime:
                    # Verificar si el hijo NO está en la zona
                    is_in_zone, distance = self.is_child_in_zone(log.child_id, log.zone_id)

                    if not is_in_zone:
                        child = log.child
                        zone = log.zone
                        minutes_late = int((current_time - expected_datetime).total_seconds() / 60)

                        message = (f"🚨 LLEGADA TARDÍA: {child.first_name} {child.last_name} "
                                 f"no ha llegado a '{zone.zone_name}'. "
                                 f"Debía llegar a las {log.expected_time.strftime('%H:%M')} "
                                 f"({minutes_late} minutos de retraso)")

                        if self.send_notification(
                            child_id=log.child_id,
                            father_id=zone.father_id,
                            notification_type='zone_late_arrival',
                            message=message
                        ):
                            log.late_notification_sent = True
                            log.minutes_late = minutes_late
                            late_notifications_sent += 1

            db.session.commit()
            return late_notifications_sent

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error verificando llegadas tardías: {e}")
            return 0

    def check_missed_zones(self, grace_period_minutes: int = 30) -> int:
        """
        Verificar zonas completamente perdidas (después del período de gracia)
        """
        try:
            current_time = datetime.now()
            today = date.today()

            # Obtener logs pendientes donde ya pasó el período de gracia
            pending_logs = ZoneComplianceLog.query.filter_by(
                compliance_date=today,
                status='pending',
                missed_notification_sent=False
            ).all()

            missed_notifications_sent = 0

            for log in pending_logs:
                expected_datetime = datetime.combine(today, log.expected_time)
                grace_deadline = expected_datetime + timedelta(minutes=grace_period_minutes)

                # Si ya pasó el período de gracia
                if current_time > grace_deadline:
                    # Verificar si el hijo NO está en la zona
                    is_in_zone, distance = self.is_child_in_zone(log.child_id, log.zone_id)

                    if not is_in_zone:
                        child = log.child
                        zone = log.zone

                        message = (f"❌ ZONA PERDIDA: {child.first_name} {child.last_name} "
                                 f"no llegó a '{zone.zone_name}'. "
                                 f"Hora esperada: {log.expected_time.strftime('%H:%M')}. "
                                 f"Han pasado más de {grace_period_minutes} minutos.")

                        if self.send_notification(
                            child_id=log.child_id,
                            father_id=zone.father_id,
                            notification_type='zone_missed',
                            message=message
                        ):
                            log.missed_notification_sent = True
                            log.status = 'missed'
                            missed_notifications_sent += 1

            db.session.commit()
            return missed_notifications_sent

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error verificando zonas perdidas: {e}")
            return 0

    def run_monitoring_cycle(self) -> Dict[str, int]:
        """
        Ejecutar un ciclo completo de monitoreo
        Retorna estadísticas del ciclo
        """
        stats = {
            'logs_created': 0,
            'arrivals_detected': 0,
            'early_warnings': 0,
            'late_notifications': 0,
            'missed_notifications': 0
        }

        try:
            # 1. Crear logs de cumplimiento para hoy (si no existen)
            stats['logs_created'] = self.create_daily_compliance_logs()

            # 2. Verificar llegadas a zonas
            stats['arrivals_detected'] = self.check_zone_arrivals()

            # 3. Verificar alertas tempranas
            stats['early_warnings'] = self.check_early_warnings()

            # 4. Verificar llegadas tardías
            stats['late_notifications'] = self.check_late_arrivals()

            # 5. Verificar zonas perdidas
            stats['missed_notifications'] = self.check_missed_zones()

            current_app.logger.info(f"Ciclo de monitoreo completado: {stats}")
            return stats

        except Exception as e:
            current_app.logger.error(f"Error en ciclo de monitoreo: {e}")
            return stats
