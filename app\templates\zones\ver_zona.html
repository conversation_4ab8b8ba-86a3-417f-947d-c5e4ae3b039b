<!-- app/templates/zones/ver_zonas.html -->
{% extends 'base.html' %}

{% block content %}
<h2>Mis Zonas Seguras</h2>

{% if zones %}
<table border="1" class="table table-striped"> {# Añadidas clases de Bootstrap para estilo básico #}
    <thead>
        <tr>
            <th>Nombre de la Zona</th>
            <th>Horario</th>
            <th>Ubicación (Centro)</th> {# Nueva columna #}
            <th>Asignada a</th>
            <th>Acciones</th>
        </tr>
    </thead>
    <tbody>
        {% for zone in zones %} {# Iterar sobre zones #}
        <tr>
            <td>{{ zone.zone_name }}</td> {# Mostrar zone.zone_name #}
            <td>{{ zone.schedule_start.strftime('%H:%M') if zone.schedule_start else 'N/A' }} - {{ zone.schedule_end.strftime('%H:%M') if zone.schedule_end else 'N/A' }}</td> {# Formatear hora #}
            <td>
                {# Desencriptar y mostrar coordenadas (JSON como string) #}
                {% if zone.encrypted_zone_data %}
                    {# Usar el filtro personalizado. Muestra el JSON desencriptado. #}
                    {# Para ver Lat/Lon específicos, necesitarías parsear este JSON #}
                    {# (ya sea con un filtro más complejo o pasando datos parseados desde la vista) #}
                    <pre style="font-size: 0.8em; margin: 0; background: #eee; padding: 2px;">{{ zone.encrypted_zone_data | decrypt_encrypted_data }}</pre>
                {% else %}
                    N/D
                {% endif %}
            </td>
            <td>
                {# Mostrar hijos asignados usando zone.assignments #}
                {% if zone.assignments %}
                    {% for assignment in zone.assignments %}
                        {{ assignment.child.first_name }} {{ assignment.child.last_name }}{% if not loop.last %}, {% endif %}
                    {% endfor %}
                {% else %}
                    <span class="text-muted">No asignada</span> {# Texto gris si no está asignada #}
                {% endif %}
            </td>
            <td>
                <!-- Formulario para asignar la zona a un hijo -->
                {# Usar URL y parámetro actualizados #}
                <form method="POST" action="{{ url_for('zones.asignar_zona', zone_id=zone.zone_id) }}" style="display:inline;">
                    {# Usar el form_asignar pasado desde la vista #}
                    {{ form_asignar.hidden_tag() if form_asignar else csrf_token() }} {# Incluir CSRF #}
                    <select name="child_id" class="form-control form-control-sm d-inline-block w-auto"> {# Estilos básicos #}
                        {# Iterar sobre hijos pasados desde la vista #}
                        {% for child in hijos %}
                            <option value="{{ child.child_id }}">{{ child.first_name }} {{ child.last_name }}</option>
                        {% else %}
                            <option disabled>No hay hijos</option>
                        {% endfor %}
                    </select>
                    <button type="submit" class="btn btn-success btn-sm">Asignar</button> {# Botón más descriptivo #}
                </form>

                <!-- Contenedor para los botones Editar y Eliminar -->
                <div style="display:inline-block; margin-left: 5px;">
                    {# Usar URL y parámetro actualizados #}
                    <a href="{{ url_for('zones.editar_zona', zone_id=zone.zone_id) }}" class="btn btn-primary btn-sm">Editar</a>

                    {# Usar URL y parámetro actualizados. Mensaje de confirmación actualizado. #}
                    <form method="POST" action="{{ url_for('zones.eliminar_zona', zone_id=zone.zone_id) }}" style="display:inline;" onsubmit="return confirm('¿Está seguro de eliminar esta zona segura y todas sus asignaciones?');">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <button type="submit" class="btn btn-danger btn-sm" style="margin-left: 5px;">Eliminar</button>
                    </form>
                </div>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% else %}
 <p>Aún no has creado ninguna zona segura.</p>
{% endif %}

{# Botón para crear nueva zona, URL actualizada #}
<a href="{{ url_for('zones.crear_zona') }}" class="btn btn-primary mt-3">Crear Nueva Zona Segura</a> {# Añadido margen superior #}

{% endblock %}