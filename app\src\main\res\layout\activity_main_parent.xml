<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- <PERSON><PERSON> superior -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp"
        android:background="@color/design_default_color_primary">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Dashboard Padre"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn_logout"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="Salir"
            android:textSize="12sp"
            android:backgroundTint="@color/white"
            android:textColor="@color/design_default_color_primary" />

    </LinearLayout>

    <!-- WebView para el dashboard -->
    <WebView
        android:id="@+id/webview_dashboard"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

</LinearLayout>
