# app/utils/scheduler.py
from celery import Celery
from app import create_app, db
from app.models import UserLocationLog, Child  # ADDED Child
from datetime import datetime
from config import Config
from celery.schedules import crontab

app = create_app()
celery = Celery(app.import_name, broker=Config.CELERY_BROKER_URL)
celery.conf.update(app.config)

@celery.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    # Ejecutar la tarea diariamente a las 04:15 am UTC
    sender.add_periodic_task(
        crontab(hour=4, minute=15),
        delete_old_location_logs.s(),
    )

@celery.task
def delete_old_location_logs():
    with app.app_context():
        today = datetime.utcnow().date()
        # logs_deleted = UserLocationLog.query.filter(UserLocationLog.recorded_at < today).delete() REMOVED
        logs_deleted = UserLocationLog.query.filter(UserLocationLog.recorded_at < today).delete() # MODIFIED - No filter by user/child, delete ALL old logs
        db.session.commit()
        print(f'Eliminados {logs_deleted} registros de logs de ubicación.')