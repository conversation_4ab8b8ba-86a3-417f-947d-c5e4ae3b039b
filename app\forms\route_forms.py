# app/forms/route_forms.py

from flask_wtf import FlaskForm
from wtforms import StringField, TimeField, TextAreaField, SelectField, SubmitField
from wtforms.validators import DataRequired, Length

class CreateRouteForm(FlaskForm):
    route_name = StringField('Nombre de la Ruta', validators=[DataRequired(), Length(max=100)])
    schedule_start = TimeField('Hora de Inicio', validators=[DataRequired()])
    schedule_end = TimeField('Hora de Fin', validators=[DataRequired()])
    route_data = TextAreaField('Datos de la Ruta (GeoJSON)', validators=[DataRequired()])
    # Campo para seleccionar el hijo; se llenará dinámicamente en la vista
    child_id = SelectField('Selecciona el Hijo', coerce=int, validators=[DataRequired()])
    submit = SubmitField('Crear Ruta')

class EditRouteForm(FlaskForm):
    # Formulario para editar una ruta existente
    route_name = StringField('Nombre de la Ruta', validators=[DataRequired(), Length(max=100)])
    schedule_start = TimeField('Hora de Inicio', validators=[DataRequired()])
    schedule_end = TimeField('Hora de Fin', validators=[DataRequired()])
    route_data = TextAreaField('Datos de la Ruta (GeoJSON)', validators=[DataRequired()])
    child_id = SelectField('Selecciona el Hijo', coerce=int, validators=[DataRequired()])
    submit = SubmitField('Actualizar Ruta')

# NEW FORM - AsignarRutaForm
class AsignarRutaForm(FlaskForm):
    submit = SubmitField('Asignar a Hijo') # You might not even need this submit field in the form definition