<!-- app/templates/users/listar_usuarios.html -->
{% extends 'base.html' %}

{% block content %}
  <h1>Lista de Usuarios</h1>
  <table class="table">
    <thead>
      <tr>
        <th>ID</th>
        <th>Usuario</th>
        <th>Nombre</th>
        <th>Apellido</th>
        <th>Correo</th>
        <th>Roles</th>
        <th>Acciones</th>
      </tr>
    </thead>
    <tbody>
      {% for user in users %}
        <tr>
          <td>{{ user.user_id }}</td>
          <td>{{ user.username }}</td>
          <td>{{ user.first_name }}</td>
          <td>{{ user.last_name }}</td>
          <td>{{ user.email }}</td>
          <td>
            {% for role in user.roles %}
              {{ role.role_name }}{% if not loop.last %}, {% endif %}
            {% endfor %}
          </td>
          <td>
            <!-- Botón para editar -->
            <a href="{{ url_for('users.editar_usuario', user_id=user.user_id) }}" class="btn btn-primary btn-sm">Editar</a>
            <!-- Formulario para eliminar usuario -->
            <form action="{{ url_for('users.eliminar_usuario', user_id=user.user_id) }}" method="POST" style="display:inline;">
              <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
              <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('¿Estás seguro de eliminar este usuario?');">Eliminar</button>
            </form>
          </td>
        </tr>
      {% endfor %}
    </tbody>
  </table>
{% endblock %}

