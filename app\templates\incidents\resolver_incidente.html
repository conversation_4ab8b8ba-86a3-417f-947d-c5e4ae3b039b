#!/usr/bin/env python3
<!-- app/templates/incidents/resolver_incidente.html -->

{% extends 'base.html' %}

{% block content %}
<h2>Resolver Incidente {{ incident.incident_id }}</h2>
<form method="POST">
    {{ form.hidden_tag() }}
    <div class="form-group">
        {{ form.resolution.label }}
        {{ form.resolution(class="form-control", rows="6") }}
        {% for error in form.resolution.errors %}
            <span class="text-danger">{{ error }}</span>
        {% endfor %}
    </div>
    <div class="form-group">
        {{ form.submit(class="btn btn-primary") }}
    </div>
</form>
{% endblock %}
