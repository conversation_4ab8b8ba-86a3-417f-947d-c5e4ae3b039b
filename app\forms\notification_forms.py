# app/forms/notification_forms.py
from flask_wtf import FlaskForm
from wtforms import SelectField, TextAreaField, SubmitField
from wtforms.validators import DataRequired, Length

class SendNotificationForm(FlaskForm):
    notification_type = SelectField('Tipo de Notificación', choices=[
        ('delay', 'De<PERSON>a'),
        ('help', '<PERSON>yu<PERSON>'),
        ('ok', 'Estoy Bien'),
        ('other', '<PERSON><PERSON>')
    ], validators=[DataRequired()])
    message_text = TextAreaField('Mensaje Adicional', validators=[Length(max=500)])
    submit = SubmitField('Enviar Notificación')
