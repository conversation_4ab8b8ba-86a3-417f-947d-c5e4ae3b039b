# app/forms/incident_forms.py
from flask_wtf import FlaskForm
from wtforms import SelectField, TextAreaField, SubmitField, SelectMultipleField
from wtforms.validators import DataRequired, Length

class ReportIncidentForm(FlaskForm):
    incident_type = SelectField('Tipo de Incidencia', choices=[
        # Puedes ajustar estos tipos si lo necesitas
        ('desviacion_zona', 'Fuera de Zona Segura'), # Tipo actualizado
        ('retraso', 'Retraso'),
        ('sin_reporte', 'Sin Reporte GPS'), # Ejemplo de otro tipo
        ('otro', 'Otro')
    ], validators=[DataRequired(message="Debe seleccionar un tipo de incidencia.")])

    description = TextAreaField('Descripción Adicional', validators=[Length(max=1000)]) # Etiqueta ligeramente cambiada

    child_id = SelectField('Seleccionar Hijo Afectado', coerce=int, validators=[DataRequired(message="Debe seleccionar un hijo.")]) # Etiqueta ligeramente cambiada

    # --- MODIFICADO: Renombrado de route_ids a zone_ids ---
    zone_ids = SelectMultipleField(
        'Zonas Seguras a Compartir', # Etiqueta actualizada
        coerce=int,
        validators=[DataRequired(message="Debe seleccionar al menos una zona segura para compartir con el operador.")]
    )
    # ----------------------------------------------------

    submit = SubmitField('Reportar Incidencia')