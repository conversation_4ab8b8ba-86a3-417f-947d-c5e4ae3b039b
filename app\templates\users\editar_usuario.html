<!-- app/templates/users/editar_usuario.html -->
{% extends 'base.html' %}

{% block content %}
  <h1>Editar Usuario</h1>
  <form method="POST" action="{{ url_for('users.editar_usuario', user_id=user.user_id) }}">
    {{ form.hidden_tag() }}
    
    <div class="form-group">
      <label for="username">Usuario</label>
      {{ form.username(class="form-control", placeholder="Ingrese nombre de usuario") }}
      {% for error in form.username.errors %}
        <span class="text-danger">{{ error }}</span>
      {% endfor %}
    </div>
    
    <div class="form-group">
      <label for="first_name">Nombre</label>
      {{ form.first_name(class="form-control", placeholder="Ingrese nombre") }}
      {% for error in form.first_name.errors %}
        <span class="text-danger">{{ error }}</span>
      {% endfor %}
    </div>
    
    <div class="form-group">
      <label for="last_name">Apellido</label>
      {{ form.last_name(class="form-control", placeholder="Ingrese apellido") }}
      {% for error in form.last_name.errors %}
        <span class="text-danger">{{ error }}</span>
      {% endfor %}
    </div>
    
    <div class="form-group">
      <label for="email">Correo Electrónico</label>
      {{ form.email(class="form-control", placeholder="Ingrese correo electrónico") }}
      {% for error in form.email.errors %}
        <span class="text-danger">{{ error }}</span>
      {% endfor %}
    </div>
    
    <div class="form-group">
      <label for="password">Nueva Contraseña (dejar en blanco si no se cambia)</label>
      {{ form.password(class="form-control", placeholder="Ingrese nueva contraseña") }}
      {% for error in form.password.errors %}
        <span class="text-danger">{{ error }}</span>
      {% endfor %}
    </div>
    
    <div class="form-group">
      <label for="confirm_password">Confirmar Contraseña</label>
      {{ form.confirm_password(class="form-control", placeholder="Repita la nueva contraseña") }}
      {% for error in form.confirm_password.errors %}
        <span class="text-danger">{{ error }}</span>
      {% endfor %}
    </div>
    
    <button type="submit" class="btn btn-primary">{{ form.submit.label.text }}</button>
  </form>
{% endblock %}