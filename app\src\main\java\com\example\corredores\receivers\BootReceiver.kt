package com.example.corredores.receivers

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.example.corredores.services.LocationService
import com.example.corredores.utils.PreferenceManager

class BootReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "BootReceiver"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "Boot receiver triggered: ${intent.action}")
        
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                
                val preferenceManager = PreferenceManager(context)
                
                // Solo reiniciar el servicio si es un hijo y está logueado
                if (preferenceManager.getUserType() == PreferenceManager.USER_TYPE_CHILD &&
                    preferenceManager.isLoggedIn()) {
                    
                    Log.d(TAG, "Restarting location service for child user")
                    LocationService.startService(context)
                } else {
                    Log.d(TAG, "Not starting service - user type: ${preferenceManager.getUserType()}, logged in: ${preferenceManager.isLoggedIn()}")
                }
            }
        }
    }
}
