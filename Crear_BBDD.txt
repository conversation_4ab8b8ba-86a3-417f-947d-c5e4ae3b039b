**Asumimos que en el nuevo servidor ya tienes:**

*   Instalado MySQL (o MariaDB).
*   Instalado Python y las herramientas de desarrollo necesarias.
*   Tu código de la aplicación Flask descargado/clonado.
*   Un entorno virtual creado y activado (ej. `corre`).
*   Las dependencias de Python instaladas (`pip install -r requirements.txt`).
*   La contraseña de `root` de MySQL (o de un usuario con privilegios para crear bases de datos y usuarios).

**Pasos para Montar la Base de Datos:**

1.  **Acceder a MySQL como Usuario Privilegiado:**
    *   Abre una terminal en el nuevo servidor.
    *   Conéctate a MySQL usando `root` o un usuario equivalente:
        ```bash
        mysql -u root -p
        ```
    *   Ingresa la contraseña de `root` de MySQL cuando se te solicite.

2.  **Crear la Base de Datos Vacía:**
    *   Dentro del prompt `mysql>` o `MariaDB [(none)]>`, ejecuta:
        ```sql
        CREATE DATABASE corredores_seguros CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        ```
    *   Esto crea la base de datos que tu aplicación espera.

3.  **Crear el Usuario de la Aplicación:**
    *   Vamos a crear el usuario `joacoabe` que tu aplicación usará para conectarse. **¡IMPORTANTE: Usa la misma contraseña (`isaias52` o la que definas en tu configuración final) que tendrás en tu `config.py` o `.env`!**
    *   Ejecuta:
        ```sql
        CREATE USER 'joacoabe'@'localhost' IDENTIFIED BY 'isaias52';
        ```
        *(Si este comando da error porque el usuario ya existe, no te preocupes, pasa al siguiente paso).*

4.  **Otorgar Permisos al Usuario de la Aplicación:**
    *   Dale al usuario `joacoabe` los permisos necesarios para trabajar *únicamente* sobre la base de datos `corredores_seguros`.
    *   Ejecuta:
        ```sql
        GRANT ALL PRIVILEGES ON corredores_seguros.* TO 'joacoabe'@'localhost';
        ```
        *   **Nota:** `localhost` significa que este usuario solo puede conectarse si la aplicación Flask se está ejecutando en la *misma* máquina que el servidor MySQL. Si estuvieran en máquinas separadas, necesitarías usar la IP o el hostname del servidor Flask en lugar de `localhost`.
        *   **Seguridad:** `ALL PRIVILEGES` es lo más simple para empezar. En un entorno de producción muy estricto, podrías otorgar solo los permisos específicos necesarios (`SELECT`, `INSERT`, `UPDATE`, `DELETE`, `CREATE`, `ALTER`, etc.).

5.  **Aplicar los Cambios de Permisos:**
    *   Ejecuta:
        ```sql
        FLUSH PRIVILEGES;
        ```

6.  **Salir de MySQL:**
    *   Ejecuta:
        ```sql
        EXIT;
        ```

7.  **Crear las Tablas (Usando Flask-Migrate):**
    *   Ahora vuelve a la terminal donde tienes tu proyecto Flask y tu entorno virtual (`corre`) activado.
    *   Asegúrate de que la variable de entorno `FLASK_APP` apunte a tu script principal (probablemente `manage.py`):
        ```bash
        export FLASK_APP=manage.py
        ```
    *   Ejecuta el comando de Flask-Migrate para crear todas las tablas definidas en tus modelos (`app/models.py`) dentro de la base de datos recién creada:
        ```bash
        flask db upgrade
        ```
    *   Si este comando se ejecuta sin errores, las tablas (`users`, `roles`, `routes`, etc.) se habrán creado.

8.  **Poblar Datos Iniciales (Roles y Admin):**
    *   Finalmente, usa tu comando personalizado definido en `manage.py` para insertar los roles por defecto y el usuario administrador:
        ```bash
        flask init_data
        ```