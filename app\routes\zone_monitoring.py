# app/routes/zone_monitoring.py

from flask import Blueprint, jsonify, request, current_app, render_template, redirect, url_for, flash
from flask_login import login_required, current_user
from app import db
from app.models import ZoneComplianceLog, ChildNotification, Child
from app.services.zone_monitoring import ZoneMonitoringService
from datetime import date, datetime, timedelta
from sqlalchemy import desc

zone_monitoring_bp = Blueprint('zone_monitoring', __name__)

@zone_monitoring_bp.route('/notifications', methods=['GET'])
@login_required
def notifications_page():
    """
    Página completa de notificaciones de zonas
    """
    try:
        # Parámetros de filtrado y paginación
        page = request.args.get('page', 1, type=int)
        per_page = 20  # 20 notificaciones por página
        notification_type = request.args.get('type', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')

        # Construir query base
        query = ChildNotification.query.filter_by(father_id=current_user.user_id)

        # Filtrar solo notificaciones de zonas
        zone_types = ['zone_early_warning', 'zone_late_arrival', 'zone_missed']
        query = query.filter(ChildNotification.notification_type.in_(zone_types))

        # Aplicar filtros
        if notification_type and notification_type in zone_types:
            query = query.filter_by(notification_type=notification_type)

        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
                query = query.filter(ChildNotification.created_at >= date_from_obj)
            except ValueError:
                flash('Fecha de inicio inválida', 'warning')

        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
                # Agregar 1 día para incluir todo el día
                date_to_obj = datetime.combine(date_to_obj, datetime.max.time())
                query = query.filter(ChildNotification.created_at <= date_to_obj)
            except ValueError:
                flash('Fecha de fin inválida', 'warning')

        # Ordenar por fecha (más recientes primero)
        query = query.order_by(desc(ChildNotification.created_at))

        # Paginar
        notifications = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # Estadísticas
        total_notifications = query.count()
        unread_count = query.filter_by(viewed_at=None).count()

        # Estadísticas por tipo
        stats = {
            'total': total_notifications,
            'unread': unread_count,
            'early_warning': query.filter_by(notification_type='zone_early_warning').count(),
            'late_arrival': query.filter_by(notification_type='zone_late_arrival').count(),
            'missed': query.filter_by(notification_type='zone_missed').count()
        }

        return render_template('zone_monitoring/notifications.html',
                             notifications=notifications,
                             stats=stats,
                             current_type=notification_type,
                             current_date_from=date_from,
                             current_date_to=date_to)

    except Exception as e:
        current_app.logger.error(f"Error en página de notificaciones: {e}")
        flash('Error al cargar las notificaciones', 'danger')
        return redirect(url_for('users.dashboard'))

@zone_monitoring_bp.route('/run_monitoring', methods=['POST'])
@login_required
def run_monitoring():
    """
    Endpoint para ejecutar manualmente el ciclo de monitoreo de zonas
    Solo accesible por administradores
    """
    if not current_user.has_role('ADMIN'):
        return jsonify({'error': 'Acceso no autorizado'}), 403

    try:
        monitoring_service = ZoneMonitoringService()
        stats = monitoring_service.run_monitoring_cycle()

        return jsonify({
            'success': True,
            'message': 'Ciclo de monitoreo ejecutado exitosamente',
            'stats': stats
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error ejecutando monitoreo manual: {e}")
        return jsonify({
            'success': False,
            'error': 'Error interno del servidor'
        }), 500


@zone_monitoring_bp.route('/compliance_stats/<int:child_id>')
@login_required
def get_compliance_stats(child_id):
    """
    Obtener estadísticas de cumplimiento de zonas para un hijo específico
    """
    try:
        # Verificar que el hijo pertenece al padre actual
        from app.models import Child
        child = Child.query.filter_by(child_id=child_id, parent_id=current_user.user_id).first()
        if not child:
            return jsonify({'error': 'Hijo no encontrado'}), 404

        # Obtener estadísticas de los últimos 30 días
        end_date = date.today()
        start_date = end_date - timedelta(days=30)

        logs = ZoneComplianceLog.query.filter(
            ZoneComplianceLog.child_id == child_id,
            ZoneComplianceLog.compliance_date >= start_date,
            ZoneComplianceLog.compliance_date <= end_date
        ).all()

        # Calcular estadísticas
        total_logs = len(logs)
        arrived_on_time = len([log for log in logs if log.status == 'arrived'])
        arrived_late = len([log for log in logs if log.status == 'late'])
        missed = len([log for log in logs if log.status == 'missed'])
        pending = len([log for log in logs if log.status == 'pending'])

        # Calcular porcentajes
        compliance_rate = ((arrived_on_time + arrived_late) / total_logs * 100) if total_logs > 0 else 0
        punctuality_rate = (arrived_on_time / total_logs * 100) if total_logs > 0 else 0

        return jsonify({
            'success': True,
            'child_name': f"{child.first_name} {child.last_name}",
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': 30
            },
            'stats': {
                'total_expected': total_logs,
                'arrived_on_time': arrived_on_time,
                'arrived_late': arrived_late,
                'missed': missed,
                'pending': pending,
                'compliance_rate': round(compliance_rate, 1),
                'punctuality_rate': round(punctuality_rate, 1)
            }
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error obteniendo estadísticas de cumplimiento: {e}")
        return jsonify({
            'success': False,
            'error': 'Error interno del servidor'
        }), 500


@zone_monitoring_bp.route('/recent_notifications')
@login_required
def get_recent_notifications():
    """
    Obtener notificaciones recientes relacionadas con zonas
    """
    try:
        # Obtener notificaciones de los últimos 7 días
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)

        notifications = ChildNotification.query.filter(
            ChildNotification.father_id == current_user.user_id,
            ChildNotification.notification_type.in_([
                'zone_early_warning',
                'zone_late_arrival',
                'zone_missed'
            ]),
            ChildNotification.created_at >= start_date
        ).order_by(ChildNotification.created_at.desc()).limit(50).all()

        notifications_data = []
        for notification in notifications:
            is_viewed = notification.viewed_at is not None
            notifications_data.append({
                'id': notification.notification_id,
                'child_id': notification.child_id,
                'child_name': f"{notification.child.first_name} {notification.child.last_name}",
                'type': notification.notification_type,
                'message': notification.message_text,
                'created_at': notification.created_at.isoformat(),
                'viewed': is_viewed
            })
            print(f"📋 Notificación {notification.notification_id}: viewed={is_viewed}, viewed_at={notification.viewed_at}")

        return jsonify({
            'success': True,
            'notifications': notifications_data,
            'total': len(notifications_data)
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error obteniendo notificaciones recientes: {e}")
        return jsonify({
            'success': False,
            'error': 'Error interno del servidor'
        }), 500


@zone_monitoring_bp.route('/mark_notification_viewed/<int:notification_id>', methods=['POST'])
@login_required
def mark_notification_viewed(notification_id):
    """
    Marcar una notificación como vista
    """
    print("=" * 60)
    print("🔔 MARCANDO NOTIFICACIÓN COMO VISTA")
    print("=" * 60)
    print(f"🆔 Notification ID: {notification_id}")
    print(f"👤 Usuario: {current_user.username} (ID: {current_user.user_id})")
    print(f"🔧 Método: {request.method}")
    print(f"📋 Headers: {dict(request.headers)}")
    print(f"📄 Content-Type: {request.content_type}")

    try:
        # Verificar que el notification_id es válido
        if not notification_id or notification_id <= 0:
            print("❌ ID de notificación inválido")
            return jsonify({
                'success': False,
                'error': 'ID de notificación inválido'
            }), 400

        print(f"🔍 Buscando notificación {notification_id} para padre {current_user.user_id}")

        notification = ChildNotification.query.filter_by(
            notification_id=notification_id,
            father_id=current_user.user_id
        ).first()

        if not notification:
            print("❌ Notificación no encontrada")
            return jsonify({
                'success': False,
                'error': 'Notificación no encontrada'
            }), 404

        print(f"✅ Notificación encontrada: {notification.message_text[:50]}...")
        print(f"📅 Creada: {notification.created_at}")
        print(f"👁️ Ya vista antes: {notification.viewed_at is not None}")
        print(f"👁️ viewed_at antes: {notification.viewed_at}")

        # Marcar como vista
        old_viewed_at = notification.viewed_at
        notification.viewed_at = datetime.now()

        print(f"👁️ viewed_at después: {notification.viewed_at}")
        print("💾 Haciendo commit a la base de datos...")

        db.session.commit()

        print("✅ Notificación marcada como vista exitosamente")
        print(f"🔄 Cambio: {old_viewed_at} -> {notification.viewed_at}")

        return jsonify({
            'success': True,
            'message': 'Notificación marcada como vista'
        }), 200

    except Exception as e:
        print("💥 ERROR MARCANDO NOTIFICACIÓN")
        print(f"❌ Error: {e}")
        print(f"❌ Tipo: {type(e).__name__}")
        import traceback
        print(f"❌ Traceback:")
        print(traceback.format_exc())

        current_app.logger.error(f"Error marcando notificación como vista: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'Error interno del servidor: {str(e)}'
        }), 500
    finally:
        print("=" * 60)


@zone_monitoring_bp.route('/zone_status/<int:zone_id>')
@login_required
def get_zone_status(zone_id):
    """
    Obtener estado actual de una zona específica
    """
    try:
        from app.models import SafeZone, ChildZoneAssignment

        # Verificar que la zona pertenece al padre actual
        zone = SafeZone.query.filter_by(zone_id=zone_id, father_id=current_user.user_id).first()
        if not zone:
            return jsonify({'error': 'Zona no encontrada'}), 404

        # Obtener asignaciones activas para esta zona
        assignments = ChildZoneAssignment.query.filter_by(
            zone_id=zone_id,
            status='assigned',
            notifications_enabled=True
        ).all()

        # Obtener logs de cumplimiento de hoy
        today = date.today()
        compliance_logs = ZoneComplianceLog.query.filter_by(
            zone_id=zone_id,
            compliance_date=today
        ).all()

        # Preparar datos de estado
        children_status = []
        monitoring_service = ZoneMonitoringService()

        for assignment in assignments:
            child = assignment.child

            # Buscar log de cumplimiento de hoy
            compliance_log = next((log for log in compliance_logs if log.child_id == child.child_id), None)

            # Verificar si está actualmente en la zona
            is_in_zone, distance = monitoring_service.is_child_in_zone(child.child_id, zone_id)

            child_status = {
                'child_id': child.child_id,
                'child_name': f"{child.first_name} {child.last_name}",
                'is_currently_in_zone': is_in_zone,
                'distance_to_zone': round(distance, 2) if distance else None,
                'expected_time': zone.schedule_start.strftime('%H:%M'),
                'early_warning_minutes': assignment.early_warning_minutes,
                'compliance_today': {
                    'status': compliance_log.status if compliance_log else 'no_log',
                    'actual_arrival_time': compliance_log.actual_arrival_time.strftime('%H:%M') if compliance_log and compliance_log.actual_arrival_time else None,
                    'minutes_late': compliance_log.minutes_late if compliance_log else 0,
                    'early_warning_sent': compliance_log.early_warning_sent if compliance_log else False,
                    'late_notification_sent': compliance_log.late_notification_sent if compliance_log else False,
                    'missed_notification_sent': compliance_log.missed_notification_sent if compliance_log else False
                }
            }
            children_status.append(child_status)

        return jsonify({
            'success': True,
            'zone': {
                'id': zone.zone_id,
                'name': zone.zone_name,
                'schedule_start': zone.schedule_start.strftime('%H:%M'),
                'schedule_end': zone.schedule_end.strftime('%H:%M') if zone.schedule_end else None
            },
            'children_status': children_status,
            'total_assigned_children': len(assignments)
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error obteniendo estado de zona: {e}")
        return jsonify({
            'success': False,
            'error': 'Error interno del servidor'
        }), 500
