# app/forms/user_forms.py

from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, PasswordField, SubmitField, SelectField
from wtforms.validators import DataRequired, Length, Email, EqualTo, Optional

class AddChildForm(FlaskForm):
    # Formulario para agregar hijos.  SIMPLIFICADO
    first_name = StringField('Nombre', validators=[DataRequired(), Length(max=100)])
    last_name = StringField('Apellido', validators=[DataRequired(), Length(max=100)])
    telefono_hijo = StringField('Teléfono del Hijo', validators=[Length(max=20)])  # Mantener si es necesario
    submit = SubmitField('Agregar Hijo')

class CreateSupervisorForm(FlaskForm):
    # Formulario para la creación de supervisores.
    username = StringField('Usuario', validators=[DataRequired(), Length(min=4, max=25)])
    email = StringField('Correo Electrónico', validators=[DataRequired(), Email()])
    password = PasswordField('Contraseña', validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField('Confirmar Contraseña', validators=[DataRequired(), EqualTo('password')])
    first_name = StringField('Nombre', validators=[Length(max=100)])
    last_name = StringField('Apellido', validators=[Length(max=100)])
    submit = SubmitField('Crear Supervisor')

class CreateParentForm(FlaskForm):
    # Formulario para la creación de padres.
    username = StringField('Usuario', validators=[DataRequired(), Length(min=4, max=25)])
    email = StringField('Correo Electrónico', validators=[DataRequired(), Email()])
    password = PasswordField('Contraseña', validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField('Confirmar Contraseña', validators=[DataRequired(), EqualTo('password')])
    first_name = StringField('Nombre', validators=[Length(max=100)])
    last_name = StringField('Apellido', validators=[Length(max=100)])
    phone = StringField('Teléfono', validators=[Length(max=20)])
    submit = SubmitField('Crear Padre')

class CreateOperatorForm(FlaskForm):
    username = StringField('Usuario', validators=[DataRequired(), Length(min=4, max=25)])
    first_name = StringField('Nombre', validators=[DataRequired(), Length(max=100)])
    last_name = StringField('Apellido', validators=[DataRequired(), Length(max=100)])
    
    # NUEVO: jerarquías personalizadas
    hierarchy = SelectField('Jerarquía', choices=[
        ('comisario_mayor', 'Comisario Mayor'),
        ('comisario_inspector', 'Comisario Inspector'),
        ('comisario', 'Comisario'),
        ('subcomisario', 'Subcomisario'),
        ('oficial_principal', 'Oficial Principal'),
        ('oficial_inspector', 'Oficial Inspector'),
        ('oficial_subinspector', 'Oficial Subinspector'),
        ('oficial_ayudante', 'Oficial Ayudante'),
        ('suboficial_mayor', 'Suboficial Mayor'),
        ('suboficial_principal', 'Suboficial Principal'),
        ('sargento_ayudante', 'Sargento Ayudante'),
        ('sargento_primero', 'Sargento Primero'),
        ('sargento', 'Sargento'),
        ('cabo_primero', 'Cabo Primero'),
        ('cabo', 'Cabo'),
        ('agente', 'Agente')
    ], validators=[DataRequired()])

    # NUEVO: número de legajo
    file_number = StringField('Legajo', validators=[DataRequired(), Length(min=4, max=25)])
    
    email = StringField('Correo Electrónico', validators=[DataRequired(), Email()])
    password = PasswordField('Contraseña', validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField('Confirmar Contraseña', validators=[DataRequired(), EqualTo('password')])
    submit = SubmitField('Crear Operador')

class EditUserForm(FlaskForm):
    """
    Formulario para editar la información de un usuario.
    La contraseña es opcional; si se ingresa se debe confirmar.
    """
    username = StringField('Usuario', validators=[DataRequired(), Length(min=4, max=25)])
    first_name = StringField('Nombre', validators=[DataRequired(), Length(max=100)])
    last_name = StringField('Apellido', validators=[DataRequired(), Length(max=100)])
    email = StringField('Correo Electrónico', validators=[DataRequired(), Email()])
    password = PasswordField('Nueva Contraseña', validators=[Optional(), Length(min=6)])
    confirm_password = PasswordField('Confirmar Contraseña', validators=[Optional(), EqualTo('password', message='Las contraseñas deben coincidir')])
    submit = SubmitField('Actualizar Usuario')

# NEW - Form for linking device
class LinkForm(FlaskForm):
    token = StringField('Token de Vinculación', validators=[DataRequired()])
    submit = SubmitField('Vincular Dispositivo')

class EditChildForm(FlaskForm):
    first_name = StringField('Nombre', validators=[DataRequired(), Length(max=100)])
    last_name = StringField('Apellido', validators=[DataRequired(), Length(max=100)])
    submit = SubmitField('Guardar Cambios')
