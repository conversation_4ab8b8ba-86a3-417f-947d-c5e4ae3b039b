# 🛡️ Corredores Seguros - Servidor Flask

Sistema de seguimiento y monitoreo familiar en tiempo real con geofencing inteligente y notificaciones push.

## 📋 **DESCRIPCIÓN**

**Corredores Seguros** es una plataforma completa de seguimiento familiar que permite a los padres monitorear la ubicación de sus hijos en tiempo real, definir zonas seguras, y recibir alertas automáticas cuando los hijos no llegan a destinos programados.

### **🎯 CARACTERÍSTICAS PRINCIPALES:**

- **👨‍👧‍👦 Gestión de Familias**: Sistema de roles (Padre/Hijo) con autenticación segura
- **📍 Seguimiento en Tiempo Real**: Ubicación GPS continua con encriptación
- **🔔 Notificaciones Push**: Alertas automáticas via Firebase Cloud Messaging
- **🗺️ Geofencing Inteligente**: Zonas seguras con monitoreo automático
- **⚡ Ubicación Bajo Demanda**: Solicitud inmediata de ubicación (2-5s respuesta)
- **📊 Dashboard Web**: Interfaz completa para gestión y monitoreo
- **🔒 Seguridad**: Encriptación de datos y tokens únicos por dispositivo

---

## 🏗️ **ARQUITECTURA**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   📱 App Android │    │  🖥️ Servidor     │    │  🗄️ Base de     │
│   (Padre/Hijo)  │◄──►│   Flask         │◄──►│   Datos MySQL   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                       ┌─────────────────┐
                       │  🔥 Firebase    │
                       │   FCM           │
                       └─────────────────┘
```

### **🔧 TECNOLOGÍAS:**
- **Backend**: Flask + SQLAlchemy + Flask-SocketIO
- **Base de Datos**: MySQL con encriptación Fernet
- **Autenticación**: Flask-Login + JWT tokens
- **Tiempo Real**: WebSockets + Polling dinámico
- **Notificaciones**: Firebase Cloud Messaging
- **Seguridad**: HTTPS + SSL + CSRF Protection

---

## 🚀 **INSTALACIÓN**

### **📋 REQUISITOS:**
- Python 3.8+
- MySQL 8.0+
- Certificados SSL (Let's Encrypt recomendado)
- Firebase Project (para notificaciones push)

### **1. 📥 CLONAR REPOSITORIO:**
```bash
git clone <repository-url>
cd corredores-seguros-servidor
```

### **2. 🐍 ENTORNO VIRTUAL:**
```bash
python -m venv corredores
source corredores/bin/activate  # Linux/Mac
# o
corredores\Scripts\activate     # Windows
```

### **3. 📦 DEPENDENCIAS:**
```bash
pip install -r requirements.txt
```

### **4. 🗄️ BASE DE DATOS:**
```bash
# Crear base de datos
mysql -u root -p
CREATE DATABASE corredores_seguros;
CREATE USER 'corredores_user'@'localhost' IDENTIFIED BY 'tu_password_seguro';
GRANT ALL PRIVILEGES ON corredores_seguros.* TO 'corredores_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# Ejecutar migraciones
flask db upgrade

# Migración FCM (nueva funcionalidad)
mysql -u corredores_user -p corredores_seguros < migrate_fcm_tokens.sql
```

### **5. ⚙️ CONFIGURACIÓN:**
```bash
# Crear archivo .env
cp .env.example .env

# Editar .env con tus configuraciones:
SECRET_KEY=tu_clave_secreta_muy_segura
DATABASE_URL=mysql+pymysql://corredores_user:tu_password@localhost:3306/corredores_seguros
FERNET_KEY=tu_clave_fernet_para_encriptacion
FIREBASE_SERVER_KEY=tu_firebase_server_key
```

### **6. 🔥 FIREBASE SETUP:**
```bash
# Seguir guía completa en:
cat firebase_setup_instructions.md
```

### **7. 🎯 INICIALIZAR DATOS:**
```bash
# Crear roles y usuario admin
flask init_data
```

---

## 🚀 **EJECUCIÓN**

### **🔧 DESARROLLO:**
```bash
python manage.py
```

### **🏭 PRODUCCIÓN:**
```bash
# Con SSL (recomendado)
python manage.py

# Sin SSL (usar proxy reverso)
gunicorn -w 4 -b 0.0.0.0:5004 manage:app
```

### **🌐 ACCESO:**
- **HTTPS**: `https://tu-dominio.com:5004`
- **HTTP**: `http://localhost:5004`

---

## 📱 **INTEGRACIÓN ANDROID**

### **🔗 ENDPOINTS PRINCIPALES:**

#### **👨‍👧‍👦 Para Padres:**
```
POST /auth/login                    # Login con folio/contraseña
GET  /users/dashboard_padre         # Dashboard web
POST /api/register_fcm_token        # Registrar token push
GET  /api/request_immediate_location # Solicitar ubicación hijo
```

#### **👶 Para Hijos:**
```
POST /api/link_device              # Vinculación con token
POST /api/log_location             # Enviar ubicación GPS
POST /api/send_notification        # Notificar al padre
GET  /api/request_immediate_location # Verificar solicitudes pendientes
```

### **🔔 NOTIFICACIONES PUSH:**
```python
# Enviar notificación automática
from app.services.fcm_service import send_zone_notification_to_father

send_zone_notification_to_father(
    father_id=123,
    child_name="Juan",
    zone_name="Escuela", 
    notification_type="zone_late_arrival"
)
```

---

## 🗂️ **ESTRUCTURA DEL PROYECTO**

```
corredores-seguros-servidor/
├── app/
│   ├── __init__.py              # Factory de la aplicación
│   ├── models.py                # Modelos de base de datos
│   ├── routes/                  # Blueprints de rutas
│   │   ├── auth.py             # Autenticación
│   │   ├── users.py            # Gestión de usuarios
│   │   ├── api.py              # API para móviles
│   │   ├── notifications.py    # Sistema de notificaciones
│   │   └── websocket_events.py # Eventos WebSocket
│   ├── services/               # Servicios de negocio
│   │   ├── fcm_service.py      # Firebase Cloud Messaging
│   │   └── zone_monitoring.py  # Monitoreo de zonas
│   ├── utils/                  # Utilidades
│   │   ├── encryption.py       # Encriptación de datos
│   │   └── decorators.py       # Decoradores personalizados
│   └── templates/              # Templates HTML
├── migrations/                 # Migraciones de BD
├── config.py                   # Configuración
├── manage.py                   # Punto de entrada
├── requirements.txt            # Dependencias Python
└── README.md                   # Este archivo
```

---

## 🔒 **SEGURIDAD**

### **🛡️ MEDIDAS IMPLEMENTADAS:**
- **Encriptación**: Datos de ubicación encriptados con Fernet
- **HTTPS**: Certificados SSL obligatorios en producción
- **CSRF**: Protección contra ataques CSRF
- **Tokens**: Autenticación basada en tokens únicos
- **Roles**: Sistema de permisos por roles
- **Validación**: Sanitización de todas las entradas

### **🔑 GESTIÓN DE TOKENS:**
```python
# Los tokens se generan automáticamente y expiran
# Cada dispositivo hijo tiene un token único de 16 caracteres
# Los tokens FCM se renuevan automáticamente
```

---

## 📊 **MONITOREO Y LOGS**

### **📈 MÉTRICAS DISPONIBLES:**
- Ubicaciones procesadas por minuto
- Notificaciones enviadas
- Conexiones activas de dispositivos
- Tiempo de respuesta de ubicación bajo demanda

### **🔍 LOGS:**
```bash
# Ver logs en tiempo real
tail -f logs/app.log

# Logs específicos de FCM
grep "FCM" logs/app.log

# Logs de ubicación bajo demanda
grep "IMMEDIATE" logs/app.log
```

---

## 🧪 **TESTING**

### **🔬 PRUEBAS UNITARIAS:**
```bash
python -m pytest tests/
```

### **🧪 PRUEBAS DE INTEGRACIÓN:**
```bash
# Probar notificaciones FCM
python test_fcm.py

# Probar ubicación bajo demanda  
python test_location_demand.py
```

---

## 🚀 **DEPLOYMENT**

### **🐳 DOCKER:**
```bash
# Construir imagen
docker build -t corredores-seguros .

# Ejecutar contenedor
docker run -p 5004:5004 corredores-seguros
```

### **☁️ CLOUD:**
- Compatible con AWS, Google Cloud, Azure
- Requiere base de datos MySQL externa
- Configurar variables de entorno según proveedor

---

## 🤝 **CONTRIBUCIÓN**

1. Fork del repositorio
2. Crear rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

---

## 📄 **LICENCIA**

Este proyecto está bajo la Licencia MIT. Ver `LICENSE` para más detalles.

---

## 📞 **SOPORTE**

- **Email**: <EMAIL>
- **Documentación**: [Wiki del proyecto]
- **Issues**: [GitHub Issues]

---

## 🔄 **CHANGELOG**

### **v2.1.0** (Actual)
- ✅ Notificaciones push Firebase Cloud Messaging
- ✅ Ubicación bajo demanda (2-5s respuesta)
- ✅ Monitoreo automático de zonas
- ✅ Dashboard mejorado para padres

### **v2.0.0**
- ✅ Sistema de roles Padre/Hijo
- ✅ Encriptación de ubicaciones
- ✅ WebSockets para tiempo real
- ✅ API REST completa

### **v1.0.0**
- ✅ Seguimiento básico de ubicación
- ✅ Dashboard web
- ✅ Autenticación de usuarios
