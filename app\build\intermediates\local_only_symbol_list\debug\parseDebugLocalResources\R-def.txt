R_DEF: Internal format may change without notice
local
color accent_color
color background_color
color black
color primary_color
color text_primary
color text_secondary
color white
drawable ic_launcher_background
drawable ic_launcher_foreground
id btn_child
id btn_connect
id btn_login
id btn_parent
id card_child
id card_parent
id et_folio
id et_password
id et_token
id guideline
id iv_logo
id main
id progress_bar
id til_folio
id til_password
id tv_app_name
id tv_subtitle
id tv_title
id webview_dashboard
id webview_dashboard_child
layout activity_login
layout activity_main
layout activity_main_child
layout activity_main_parent
layout activity_profile_selection
layout activity_splash
layout activity_token
mipmap ic_launcher
mipmap ic_launcher_round
string allow
string app_name
string change_profile
string checking_permissions
string connect
string default_web_client_id
string enter_token
string error_connection
string error_permissions
string exit
string folio
string gcm_defaultSenderId
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string gps_disabled_message
string gps_disabled_title
string loading
string login
string password
string permission_background_message
string permission_background_title
string permission_location_message
string permission_location_title
string profile_child
string profile_parent
string project_id
string select
string select_profile
string settings
string token
style Base.Theme.Corredores
style Theme.Corredores
xml backup_rules
xml data_extraction_rules
