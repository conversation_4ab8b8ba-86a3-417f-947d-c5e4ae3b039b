# ARCHIVO: app/routes/users.py
# AGREGAR ESTA NUEVA RUTA AL FINAL DEL ARCHIVO

@users_bp.route('/dashboard_hijo_app')
def dashboard_hijo_app():
    """
    Ruta especial para la aplicación móvil Android.
    Recibe el child_id como parámetro y establece la cookie automáticamente.
    """
    child_id = request.args.get('child_id')
    
    if not child_id:
        flash('ID de hijo no proporcionado.', 'danger')
        return redirect(url_for('users.vincular_dispositivo'))
    
    try:
        child_id = int(child_id)
    except ValueError:
        flash('ID de hijo inválido.', 'danger')
        return redirect(url_for('users.vincular_dispositivo'))
    
    # Verificar que el hijo existe
    child = Child.query.get(child_id)
    if not child:
        flash('Hijo no encontrado.', 'danger')
        return redirect(url_for('users.vincular_dispositivo'))
    
    # Establecer el hijo en g para que esté disponible en el template
    g.child = child
    
    # Establecer la cookie para futuras navegaciones dentro del WebView
    response = make_response(render_template('users/dashboard_hijo.html'))
    response.set_cookie(
        'child_id',
        str(child_id),
        httponly=True,
        secure=True,
        max_age=60*60*24*30,  # 30 días
        path='/'
    )
    
    return response


# TAMBIÉN AGREGAR ESTA RUTA API PARA CREAR SESIONES WEB
@users_bp.route('/api/create_web_session', methods=['POST'])
def create_web_session():
    """
    API endpoint para crear una sesión web para la aplicación móvil.
    Recibe el child_id y devuelve una URL temporal con token de sesión.
    """
    if not request.is_json:
        return jsonify({'status': 'error', 'message': 'Se esperaba contenido JSON'}), 400
    
    data = request.get_json()
    child_id = data.get('child_id')
    
    if not child_id:
        return jsonify({'status': 'error', 'message': 'Falta el campo "child_id"'}), 400
    
    # Verificar que el hijo existe
    child = Child.query.get(child_id)
    if not child:
        return jsonify({'status': 'error', 'message': 'Hijo no encontrado'}), 404
    
    # Generar un token temporal para la sesión web (válido por 5 minutos)
    import secrets
    import time
    
    session_token = secrets.token_urlsafe(32)
    expiration = int(time.time()) + 300  # 5 minutos
    
    # Guardar el token temporal en caché (puedes usar Redis o base de datos)
    # Por simplicidad, usaremos una variable global (no recomendado para producción)
    if not hasattr(create_web_session, 'temp_sessions'):
        create_web_session.temp_sessions = {}
    
    create_web_session.temp_sessions[session_token] = {
        'child_id': child_id,
        'expiration': expiration
    }
    
    # Limpiar tokens expirados
    current_time = int(time.time())
    expired_tokens = [token for token, data in create_web_session.temp_sessions.items() 
                     if data['expiration'] < current_time]
    for token in expired_tokens:
        del create_web_session.temp_sessions[token]
    
    # Devolver la URL con el token temporal
    dashboard_url = url_for('users.dashboard_hijo_with_token', token=session_token, _external=True)
    
    return jsonify({
        'status': 'success',
        'dashboard_url': dashboard_url,
        'expires_in': 300
    }), 200


@users_bp.route('/dashboard_hijo_token/<token>')
def dashboard_hijo_with_token(token):
    """
    Dashboard del hijo accesible mediante token temporal.
    """
    import time
    
    # Verificar si el token existe y no ha expirado
    if not hasattr(create_web_session, 'temp_sessions'):
        flash('Sesión inválida.', 'danger')
        return redirect(url_for('users.vincular_dispositivo'))
    
    session_data = create_web_session.temp_sessions.get(token)
    if not session_data:
        flash('Token de sesión inválido.', 'danger')
        return redirect(url_for('users.vincular_dispositivo'))
    
    if session_data['expiration'] < int(time.time()):
        # Token expirado
        del create_web_session.temp_sessions[token]
        flash('Token de sesión expirado.', 'danger')
        return redirect(url_for('users.vincular_dispositivo'))
    
    child_id = session_data['child_id']
    child = Child.query.get(child_id)
    if not child:
        flash('Hijo no encontrado.', 'danger')
        return redirect(url_for('users.vincular_dispositivo'))
    
    # Establecer el hijo en g
    g.child = child
    
    # Establecer la cookie permanente y eliminar el token temporal
    response = make_response(render_template('users/dashboard_hijo.html'))
    response.set_cookie(
        'child_id',
        str(child_id),
        httponly=True,
        secure=True,
        max_age=60*60*24*30,  # 30 días
        path='/'
    )
    
    # Eliminar el token temporal ya que se usó
    del create_web_session.temp_sessions[token]
    
    return response


# ASEGÚRATE DE TENER ESTOS IMPORTS AL INICIO DEL ARCHIVO:
# from flask import g, request, make_response, render_template, redirect, url_for, flash, jsonify
# import secrets
# import time
