<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center"
    android:background="@color/white">

    <!-- Logo o icono -->
    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="32dp"
        android:src="@mipmap/ic_launcher"
        android:contentDescription="Logo" />

    <!-- Título -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Conectar Dispositivo"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <!-- Subtítulo -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Ingresa el token proporcionado por tu padre"
        android:textSize="16sp"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="32dp" />

    <!-- Campo de token -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:hint="Token de vinculación">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_token"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textCapCharacters"
            android:maxLength="16"
            android:textAllCaps="true" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Botón conectar -->
    <Button
        android:id="@+id/btn_connect"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:text="Conectar"
        android:textSize="16sp"
        android:layout_marginBottom="16dp" />

    <!-- Progress bar -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_marginBottom="24dp" />

</LinearLayout>
