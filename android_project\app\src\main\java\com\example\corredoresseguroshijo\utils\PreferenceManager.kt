package com.example.corredoresseguroshijo.utils

import android.content.Context
import android.content.SharedPreferences

// --- Gestor de Preferencias Centralizado ---
class PreferenceManager private constructor(context: Context) {

    companion object {
        private const val PREFS_NAME = "CorredoresHijoPrefs"
        private const val CHILD_ID_KEY = "child_id"
        private const val TOKEN_KEY = "token"
        private const val LAST_LOCATION_LAT_KEY = "last_location_lat"
        private const val LAST_LOCATION_LNG_KEY = "last_location_lng"
        private const val LAST_LOCATION_TIME_KEY = "last_location_time"
        private const val WEBSOCKET_ENABLED_KEY = "websocket_enabled"

        @Volatile
        private var INSTANCE: PreferenceManager? = null

        fun getInstance(context: Context): PreferenceManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: PreferenceManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    // --- Child ID ---
    fun saveChildId(childId: Long) {
        sharedPreferences.edit()
            .putLong(CHILD_ID_KEY, childId)
            .apply()
    }

    fun getChildId(): Long {
        return sharedPreferences.getLong(CHILD_ID_KEY, -1L)
    }

    fun clearChildId() {
        sharedPreferences.edit()
            .remove(CHILD_ID_KEY)
            .apply()
    }

    // --- Token ---
    fun saveToken(token: String) {
        sharedPreferences.edit()
            .putString(TOKEN_KEY, token)
            .apply()
    }

    fun getToken(): String? {
        return sharedPreferences.getString(TOKEN_KEY, null)
    }

    fun clearToken() {
        sharedPreferences.edit()
            .remove(TOKEN_KEY)
            .apply()
    }

    // --- Última ubicación ---
    fun saveLastLocation(latitude: Double, longitude: Double, timestamp: Long) {
        sharedPreferences.edit()
            .putFloat(LAST_LOCATION_LAT_KEY, latitude.toFloat())
            .putFloat(LAST_LOCATION_LNG_KEY, longitude.toFloat())
            .putLong(LAST_LOCATION_TIME_KEY, timestamp)
            .apply()
    }

    fun getLastLocationLatitude(): Double {
        return sharedPreferences.getFloat(LAST_LOCATION_LAT_KEY, 0f).toDouble()
    }

    fun getLastLocationLongitude(): Double {
        return sharedPreferences.getFloat(LAST_LOCATION_LNG_KEY, 0f).toDouble()
    }

    fun getLastLocationTimestamp(): Long {
        return sharedPreferences.getLong(LAST_LOCATION_TIME_KEY, 0L)
    }

    fun hasLastLocation(): Boolean {
        return sharedPreferences.contains(LAST_LOCATION_LAT_KEY) &&
               sharedPreferences.contains(LAST_LOCATION_LNG_KEY)
    }

    fun clearLastLocation() {
        sharedPreferences.edit()
            .remove(LAST_LOCATION_LAT_KEY)
            .remove(LAST_LOCATION_LNG_KEY)
            .remove(LAST_LOCATION_TIME_KEY)
            .apply()
    }

    // --- WebSocket configuración ---
    fun setWebSocketEnabled(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(WEBSOCKET_ENABLED_KEY, enabled)
            .apply()
    }

    fun isWebSocketEnabled(): Boolean {
        return sharedPreferences.getBoolean(WEBSOCKET_ENABLED_KEY, true) // Por defecto habilitado
    }

    // --- Limpiar todas las preferencias ---
    fun clearAll() {
        sharedPreferences.edit()
            .clear()
            .apply()
    }

    // --- Verificar si el dispositivo está vinculado ---
    fun isDeviceLinked(): Boolean {
        return getChildId() != -1L
    }
}
