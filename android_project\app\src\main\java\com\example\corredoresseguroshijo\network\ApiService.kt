package com.example.corredoresseguroshijo.network

import retrofit2.Call
import retrofit2.Response
import retrofit2.http.*

// --- Interfaz ApiService: Define todos los endpoints de la API ---
interface ApiService {

    // Endpoint para vincular dispositivo con token
    @POST("api/link_device")
    fun linkDevice(@Body request: LinkRequest): Call<LinkResponse>

    // Endpoint para enviar ubicación del hijo
    @POST("api/log_location")
    suspend fun logLocation(
        @Header("X-Child-ID") childId: String,
        @Body locationData: LocationLogRequest
    ): Response<SimpleApiResponse>

    // Endpoint para verificar solicitudes de ubicación pendientes
    @GET("api/request_immediate_location")
    suspend fun checkPendingLocationRequests(
        @Header("X-Child-ID") childId: String
    ): Response<LocationRequestResponse>

    // Endpoint para registrar conexión activa del hijo
    @POST("api/register_connection")
    suspend fun registerConnection(
        @Header("X-Child-ID") childId: String
    ): Response<SimpleApiResponse>

    // Endpoint para enviar notificación al padre
    @POST("api/send_notification")
    suspend fun sendNotification(
        @Header("X-Child-ID") childId: String,
        @Body notificationData: NotificationRequest
    ): Response<SimpleApiResponse>
}

// --- Data Classes (DTOs) para las solicitudes y respuestas ---

// Request para vincular dispositivo
data class LinkRequest(
    val token: String
)

// Response de vinculación
data class LinkResponse(
    val status: String,
    val message: String,
    val child_id: Long? = null
)

// Request para enviar ubicación
data class LocationLogRequest(
    val latitude: Double,
    val longitude: Double,
    val accuracy: Float? = null,
    val timestamp: String? = null
)

// Response simple para la mayoría de endpoints
data class SimpleApiResponse(
    val status: String,
    val message: String
)

// Response para verificar solicitudes de ubicación
data class LocationRequestResponse(
    val status: String,
    val message: String,
    val request_id: String? = null
)

// Request para notificaciones
data class NotificationRequest(
    val notification_type: String,
    val message_text: String? = null,
    val latitude: Double? = null,
    val longitude: Double? = null
)

// Response genérica para errores de API
data class ApiErrorResponse(
    val status: String? = null,
    val message: String? = null,
    val error: String? = null
)
